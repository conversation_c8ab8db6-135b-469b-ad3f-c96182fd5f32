"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_i18n_locales_en_Corbeille_error_json";
exports.ids = ["_rsc_src_i18n_locales_en_Corbeille_error_json"];
exports.modules = {

/***/ "(rsc)/./src/i18n/locales/en/Corbeille/error.json":
/*!**************************************************!*\
  !*** ./src/i18n/locales/en/Corbeille/error.json ***!
  \**************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"error-heading":"It looks like you followed a link that doesn\'t exist.","page-title ":"Definition: 404 Error - What is a 404 Error? ","app-description":"Discover the definition of Error 404 and all there is to know about this term thanks to the lexicon of the agency medecinsvp","keywords":"What is a 404 error,how to fix  error 404  on android, fake  404 error pages","error-sub-heading":"We\'re sorry! This page is currently unavailable. We request  you to please try again later.","tell-us":"tell us","back-to-Homepage":"Back to Homepage","copyright":" Doctor please. All rights reserved","an error occured":"an error occured"}');

/***/ })

};
;