"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_i18n_locales_en_recipes_json";
exports.ids = ["_ssr_src_i18n_locales_en_recipes_json"];
exports.modules = {

/***/ "(ssr)/./src/i18n/locales/en/recipes.json":
/*!******************************************!*\
  !*** ./src/i18n/locales/en/recipes.json ***!
  \******************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"metadata":{"Title":"Recipes | Find your doctor and make an appointment online","Description":"Medecinsvp is the first platform that takes into account your availability to organize an appointment with a health professional in less than an hour.","Description-Twitter":"With MedecinSvp, find a doctor, specialist or dentist and make an appointment online 100% free, fast and efficient.","Keywords":"Medecinsvp, online appointment, find a doctor, appointment, appointment, doctor, Morocco, Medecinsvp.com, General medicine, Morocco"}}');

/***/ })

};
;