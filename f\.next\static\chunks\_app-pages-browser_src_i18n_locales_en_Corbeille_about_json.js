"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_i18n_locales_en_Corbeille_about_json"],{

/***/ "(app-pages-browser)/./src/i18n/locales/en/Corbeille/about.json":
/*!**************************************************!*\
  !*** ./src/i18n/locales/en/Corbeille/about.json ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

module.exports = /*#__PURE__*/JSON.parse('{"about-p":"Welcome to the blog where words sometimes cooperate and form sentences... sometimes. I\'m your guide through the jungle of jumbled thoughts, the maestro of misplaced commas, the captain of creative chaos. If you\'re looking for perfectly crafted prose, well, you might want to keep looking. But if you\'re up for a rollercoaster ride through the realm of semi-organized randomness, buckle up and join me on this linguistic adventure! Disclaimer: Expect puns, occasional wit (or attempted wit), and a whole lot of trial and error. Stick around, and together, we\'ll navigate this whirlwind of words!"}');

/***/ })

}]);