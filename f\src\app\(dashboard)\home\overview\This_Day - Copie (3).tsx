"use client";
import React, { useCallback,useState ,useEffect, useMemo,  } from "react";
import { DragDropContext, Droppable, Draggable, } from '@hello-pangea/dnd';

import { DictionaryModalsManager } from '@/components/alerte';
import { useDisclosure,  } from "@mantine/hooks";
import SelectJournee from "./SelectJournee";
import { useForm } from '@mantine/form';
import {Menu,Indicator, Group, Button,Text ,Tabs,Container,Badge,ThemeIcon, Avatar,Divider,Tooltip,FloatingIndicator ,Stack,Checkbox
  ,Loader,rem,Card,Modal,NumberInput,Select,Box,Paper,List,ActionIcon,ScrollArea,Flex ,Switch,Alert,Table,MultiSelect,Radio,Textarea,TextInput,
} from '@mantine/core';
import LunchtimeBackgroundModal from './LunchtimeBackgroundModal';
import { patientAPI } from '@/services/api';
import TimeSlot from './TimeSlot';
import Icon from '@mdi/react';
import { mdiArrowLeftThin,mdiChevronDown,mdiPlaylistCheck,mdiChevronLeft, mdiChevronRight, mdiLogout,mdiAccountNetwork,mdiBookmarkOutline,mdiCalendarRange,mdiCalendarPlus,mdiMagnify,
mdiDelete, mdiPencil, mdiCalendarMonth,mdiBedQueenOutline,mdiDotsVertical,mdiDeleteClockOutline,mdiClipboardEditOutline,mdiMapMarkerPath,mdiFilterVariant,
mdiCircle,  mdiTooth,mdiFileDocumentCheck,mdiAccountAlert,mdiAlertPlus,mdiCommentPlus,mdiShare,mdiArrowDown,mdiCurrencyUsd,mdiCashMultiple,mdiArrowLeft,
mdiDeleteSweep,mdiClipboardText,mdiMicrophone,mdiHistory,mdiUpdate,mdiArrowRightThin, mdiAccountTie, mdiListBoxOutline,mdiPower,mdiNeedle,mdiPen,mdiAccountBoxEditOutline,mdiFileAccount,mdiPencilBoxOutline,mdiCheck,mdiFileDocument,mdiReply,mdiAlarm,mdiPhone,mdiAccountArrowRight,mdiArrowUp,mdiPlus,
} from '@mdi/js';
import {  ColorPicker } from '@mantine/core';
import { IconStethoscope, IconCalendarClock,IconChevronDown,IconArrowsExchange, IconRefresh,IconColorPicker, IconEdit,
  IconTrash,IconMessageShare,IconFilePencil,IconDots,IconCalendarPlus,IconArrowRight,IconArrowBackUpDouble,IconWallpaper,
  IconCheck,IconX,IconInfoCircle,IconShieldCheck,IconAlarm,IconPhoneCall,IconClock2,IconClock,
 } from '@tabler/icons-react';
import classes from "@/styles/DndList.module.css";
import "./DayView.css";
import SimpleBar from 'simplebar-react';
import moment from "moment";
import "moment/locale/fr";
import { notifications } from '@mantine/notifications';
import MenuIcons from '@/components/agenda/icons/MenuIcons';
import PatientDetailsModal from './PatientDetailsModal';
import {
 
  EventType
} from '@/types/typesCalendarPatient';
const icon = <IconInfoCircle />;
// Types simplifiés pour Web Speech API
type SpeechRecognitionInstance = {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  onstart: ((event: Event) => void) | null;
  onresult: ((event: unknown) => void) | null;
  onerror: ((event: Event) => void) | null;
  onend: ((event: Event) => void) | null;
  start(): void;
  stop(): void;
};
 interface TreeNodeChoixMultiple {
       uid: string;
       value: string;
       nodes?: TreeNodeChoixMultiple[];
     }
     function TreeItemChoixMultiple({
       node,
       collapsedNodes,
       toggleNodeCollapse,
       selectedNodes,
       toggleNodeSelection,
     }: {
       node: TreeNodeChoixMultiple;
       collapsedNodes: Record<string, boolean>;
       toggleNodeCollapse: (nodeId: string) => void;
       selectedNodes: Set<string>;
       toggleNodeSelection: (nodeId: string) => void;
     }) {
       // Par défaut, tous les nœuds sont ouverts (false = ouvert, true = fermé)
       const isCollapsed = collapsedNodes[node.uid] ?? false;
       const isSelected = selectedNodes.has(node.uid);
       // Calculer l'état indéterminé pour les nœuds parents
       const getIndeterminateState = () => {
         if (!node.nodes || node.nodes.length === 0) return false;
         const selectedChildren = node.nodes.filter(child => selectedNodes.has(child.uid));
         return selectedChildren.length > 0 && selectedChildren.length < node.nodes.length;
       };
       const isIndeterminate = getIndeterminateState();

       return (
         <Stack pl="md" gap="xs">
           <Group gap="xs" align="center">
             {node.nodes && node.nodes.length > 0 && (
               <span onClick={() => toggleNodeCollapse(node.uid)} style={{ cursor: 'pointer' }}>
                 <Icon path={isCollapsed ? mdiChevronRight : mdiChevronDown} size={0.8} />
               </span>
             )}
             <Checkbox
               label={node.value}
               checked={isSelected}
               indeterminate={isIndeterminate}
               onChange={() => toggleNodeSelection(node.uid)}
               radius="xs"
             />
           </Group>

           {!isCollapsed &&
             node.nodes?.map((child) => (
               <TreeItemChoixMultiple
                 key={child.uid}
                 node={child}
                 collapsedNodes={collapsedNodes}
                 toggleNodeCollapse={toggleNodeCollapse}
                 selectedNodes={selectedNodes}
                 toggleNodeSelection={toggleNodeSelection}
               />
             ))}
         </Stack>
       );
     }
interface AlertFormValues {
  trigger_for: string[];
  trigger: string;
  level: 'MINIMUM' | 'MEDIUM' | 'HIGH';
  description: string;
  is_permanent: boolean;
}
// Interface pour les données d'alerte
interface AlertData {
  id: string;
  level: 'MINIMUM' | 'MEDIUM' | 'HIGH';
  is_permanent: boolean;
  Declencheur: string;
  Description: string;
  trigger_for?: string[];
}
// Simple classnames utility
const cx = (classObj: Record<string, boolean>): string => {
  return Object.entries(classObj)
    .filter(([, condition]) => condition)
    .map(([className]) => className)
    .join(' ');
};

// Types
interface CalendarEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  desc?: string;
  color?: string;
  roomId?: string;
  resourceId?: string | number;
  first_name?: string;
  last_name?: string;
  phone_number?: string;
  phone_numbers?: string;
  type?: string;
  checkedListedattente?: boolean;
  duration?: number;
  address?: string;
  notes?: string;
  docteur?: string;
  typeConsultation?: string;
  eventType?: 'visit' | 'visitor-counter' | 'completed' | 'diagnosis';
  sociale?: string;
  // added optional fields referenced in code
  lastVisit?: string;
  consultationDuration?: number;
  isActive?: boolean;
  lunchTime?: boolean;
  currentEventColor?: string;
  patient_id?: string;
  patient?: string; // Alternative patient field
  doctor_name?: string; // Doctor name from backend
  doctor?: string; // Doctor ID
  status?: string; // Appointment status
  room?: string; // Room name
}

interface Room {
  id: string;
  name: string;
  color?: string;
}

// Default rooms
const defaultRooms: Room[] = [
  { id: 'room-a', name: 'Room A', color: '#f8f9fa' },
  { id: 'room-b', name: 'Room B', color: '#f8f9fa' },
];

// Utility functions
const formatTime = (hour: number, minute: number): string => {
  const hourStr = hour.toString().padStart(2, '0');
  const minuteStr = minute.toString().padStart(2, '0');
  return `${hourStr}:${minuteStr}`;
};

// Helper function to format time from Date object or time string
const formatTimeFromDate = (time: Date | string): string => {
  if (typeof time === 'string') {
    return time; // Assume it's already formatted
  }
  if (time instanceof Date) {
    return formatTime(time.getHours(), time.getMinutes());
  }
  return '00:00'; // Fallback
};

// Helper function to compare resourceId with number
const isResourceId = (resourceId: string | number | undefined, targetId: number): boolean => {
  if (resourceId === undefined) return false;
  return Number(resourceId) === targetId;
};

const isSameDay = (date1: Date, date2: Date): boolean => {
  return date1.toDateString() === date2.toDateString();
};

const getEventsForTimeSlot = (
  events: CalendarEvent[],
  day: Date,
  hour: number,
  minute: number,
  roomId: string
): CalendarEvent[] => {
  const dayEvents = events.filter(event => isSameDay(event.start, day));

  // Debug logging for today's events
  if (isSameDay(day, new Date())) {
    console.log(`🔍 getEventsForTimeSlot: Checking slot ${hour}:${minute.toString().padStart(2, '0')} in room ${roomId}`);
    console.log(`📅 Total events for today: ${dayEvents.length}`);
    if (dayEvents.length > 0) {
      console.log(`📋 Today's events:`, dayEvents.map(e => ({
        title: e.title,
        time: `${e.start.getHours()}:${e.start.getMinutes().toString().padStart(2, '0')}`,
        roomId: e.roomId
      })));
    }
  }

  return events.filter(event => {
    if (!isSameDay(event.start, day)) return false;

    // More flexible room matching - if event has no roomId, show in all rooms
    if (event.roomId && event.roomId !== roomId) return false;

    const eventHour = event.start.getHours();
    const eventMinute = event.start.getMinutes();

    // Check if event starts in this 15-minute slot
    const matches = eventHour === hour && eventMinute >= minute && eventMinute < minute + 15;

    if (matches && isSameDay(day, new Date())) {
      console.log(`✅ Event matches slot: ${event.title} at ${eventHour}:${eventMinute.toString().padStart(2, '0')} in room ${event.roomId || 'any'}`);
    }

    return matches;
  });
};
 interface TreeNode {
   value: string;
   children?: TreeNode[];
 }
 const mockTree: TreeNode[] = [
   {
     value: 'Alertes',
     children: [
       { value: "Allaitante depuis:" },
       { value: "Allergique à l'Aspirine" },
       { value: "Allergique à la Pénicilline" },
       { value: "Arthrose" },
       { value: "Cardiaque Anticoagulant sintrom" },
       { value: "Cardiaque prothèse valvulaire" },
       { value: "Cardiaque trouble du rythme" },
       { value: "Diabétique ID" },
       { value: "Diabétique NID" },
       { value: "Enceinte depuis:" },
       { value: "Gastralgie : ulcère anti-inflammatoire" },
       { value: "Hypertension" },
       { value: "Hypotension" },
       { value: "Thyroïde" },
     ],
   },
 ];
 // Composant Tree pour la sidebar
 function Tree({ nodes, onSelect }: { nodes: TreeNode[]; onSelect: (v: string) => void }) {
   // Initialiser tous les nœuds comme ouverts
 
   const [expanded, setExpanded] = useState<Record<string, boolean>>(() => {
     const initialExpanded: Record<string, boolean> = {};
     const expandAllNodes = (nodeList: TreeNode[]) => {
       nodeList.forEach(node => {
         if (node.children && node.children.length > 0) {
           initialExpanded[node.value] = true;
           expandAllNodes(node.children);
         }
       });
     };
     expandAllNodes(nodes);
     console.log('Tree initialized with expanded nodes:', initialExpanded);
     return initialExpanded;
   });
 
   return (
     <ul style={{ listStyle: 'none', paddingLeft: 16,height:'auto' }}>
       {nodes.map((node, idx) => {
         const hasChildren = node.children && node.children.length > 0;
         const isOpen = expanded[node.value] || false;
         return (
           <li key={node.value + idx}>
             <Group gap="xs" align="center" onClick={() => {
               // Ne fermer jamais les nœuds, seulement les ouvrir s'ils ne le sont pas déjà
               if (hasChildren && !isOpen) {
                 console.log('Opening node:', node.value);
                 setExpanded(prev => ({ ...prev, [node.value]: true }));
               } else if (hasChildren && isOpen) {
                 console.log('Node already open, not closing:', node.value);
               }
             }} className="Alertesslidbar">
               {hasChildren ? (
                 <Icon path={isOpen ? mdiChevronDown : mdiChevronRight} size={0.8} />
               ) : null}
               <Text
                 onClick={() => !hasChildren && onSelect(node.value)}
                 style={{ cursor: 'pointer' ,paddingLeft:'10px'
                 }}
               >
                 {node.value}
               </Text>
             </Group>
             {hasChildren && isOpen && <Tree nodes={node.children!} onSelect={onSelect} />}
           </li>
         );
       })}
     </ul>
   );
 }

// Type definition for time slot
interface TimeSlotData {
  hour: number;
  minute: number;
}

// Alert data interface

// Define the patient interface for waiting list
interface WaitingListPatient {
  id: string;
  first_name: string;
  last_name: string;
  typeConsultation?: string;
  eventType?: 'visit' | 'visitor-counter' | 'completed' | 'diagnosis';
  phone_number?: string;
  phone_numbers?: string;
  start?: string;
  duration?: number;
  color?: string;
  docteur?: string;
  date?: string;
  title?: string;
  end?: string;
  desc?: string;
  address?: string;
  notes?: string;
  isActive?: boolean; // Add isActive property for presentation room functionality
  // Additional fields to match PatientData interface
  birth_date?: string;
  age?: number;
  gender?: string;
  cin?: string;
  socialSecurity?: string;
  etatCivil?: string;
  email?: string;
  profession?: string;
  birthPlace?: string;
  fatherName?: string;
  motherName?: string;
  bloodGroup?: string;
  allergies?: string;
  commentairelistedattente?: string;
  checkedAppelvideo?: boolean;
  checkedRappelSms?: boolean;
  checkedRappelEmail?: boolean;
  resourceId?: string | number;
  agenda?: string;
  comment?: string;
  consultationDuration?: number;
}

// Day View Component
const DayView = ({
  currentDate,
  events,
  onTimeSlotClick,
  onDateChange,
  onNavigate,
  onEventAdd,
  onEventEdit,
  onEventDelete,
  rooms = defaultRooms,
  waitingList: externalWaitingList,
  onWaitingListUpdate,
}: {
  currentDate: Date;
  events: CalendarEvent[];
  onTimeSlotClick?: (date: Date, hour: number, minute: number, roomId: string) => void;
  onDateChange?: (date: Date) => void;
  onNavigate?: (direction: 'prev' | 'next' | 'today') => void;
  onEventAdd?: (event: CalendarEvent) => void;
  onEventEdit?: (event: CalendarEvent) => void;
  onEventDelete?: (event: CalendarEvent) => void;
  rooms?: Room[];
  waitingList?: WaitingListPatient[];
  onWaitingListUpdate?: (waitingList: WaitingListPatient[]) => void;
}) => {
  const [localEvents, setLocalEvents] = useState<CalendarEvent[]>(events);

  // Local waiting list state - initialize with external prop or empty array
  const [waitingList, setWaitingList] = useState<WaitingListPatient[]>(externalWaitingList || []);

  // Fetch events from backend on component mount
  useEffect(() => {
    console.log('🔄 This_Day: useEffect triggered with events:', {
      eventsLength: events?.length || 0,
      eventsProvided: !!events,
      firstEvent: events?.[0]?.title || 'none',
      lunchEvents: events?.filter(e => e.title?.includes('🍽️') || e.title?.includes('Déjeuner')).length || 0
    });

    const fetchEvents = async () => {
      try {
        console.log('🔄 This_Day: Fetching events with date range...');

        // Import API dynamically to avoid SSR issues
        const { appointmentAPI,  } = await import('@/services/api');

        // Get appointments for a wider date range to ensure we get all relevant events
        const today = new Date();
        const oneMonthAgo = new Date(today);
        oneMonthAgo.setMonth(today.getMonth() - 1);
        const oneMonthFromNow = new Date(today);
        oneMonthFromNow.setMonth(today.getMonth() + 1);

        const startDate = oneMonthAgo.toISOString().split('T')[0];
        const endDate = oneMonthFromNow.toISOString().split('T')[0];

        console.log('📅 This_Day: Fetching appointments from', startDate, 'to', endDate);
        console.log('🌐 This_Day: API call parameters:', {
          start_date: startDate,
          end_date: endDate,
          limit: 1000
        });

        // Fetch appointments with date range and high limit
        const appointmentsResponse = await appointmentAPI.list({
          start_date: startDate,
          end_date: endDate,
          limit: 1000
        });

        console.log('📥 This_Day: API response received:', {
          count: appointmentsResponse.count,
          resultsLength: appointmentsResponse.results?.length,
          firstResult: appointmentsResponse.results?.[0]
        });
        const appointments = appointmentsResponse.results || [];

        console.log('📥 This_Day: Received', appointments.length, 'appointments');

        // Check for today's appointments
        const todaysAppointments = appointments.filter(apt => apt.appointment_date === today.toISOString().split('T')[0]);
        console.log('🎯 This_Day: Found', todaysAppointments.length, 'appointments for today');

        // Helper function to map resource_id to roomId consistently
        const mapResourceIdToRoomId = (resourceId: string | number | undefined): string => {
          if (!resourceId) return 'room-a'; // Default to room-a

          // Convert to string for consistent comparison
          const resourceIdStr = resourceId.toString();

          // Handle different resource ID formats
          if (resourceIdStr === 'room-a' || resourceIdStr === '1') {
            return 'room-a';
          } else if (resourceIdStr === 'room-b' || resourceIdStr === '2') {
            return 'room-b';
          }

          // If it's a numeric string, convert to room
          const numericId = parseInt(resourceIdStr, 10);
          if (!isNaN(numericId)) {
            return numericId === 1 ? 'room-a' : 'room-b';
          }

          // Default fallback
          return 'room-a';
        };

        // Convert appointments to calendar events
        const calendarEvents = appointments.map((appointment: {
          id: string;
          title: string;
          appointment_date: string;
          appointment_time: string;
          color?: string;
          resource_id?: string;
          appointment_type: string;
          status: string;
          duration_minutes?: number;
          consultation_duration?: number;
          patient_phone?: string;
          doctor_assigned?: string;
          doctor_name?: string; // Add doctor_name field
          patient_name?: string;
          patient?: string; // Patient ID field
          patient_id?: string; // Alternative patient ID field
        }) => {
          // Debug: Log appointment data to see what doctor fields are available
          console.log('🔍 Appointment data:', {
            id: appointment.id,
            doctor_assigned: appointment.doctor_assigned,
            doctor_name: appointment.doctor_name,
            title: appointment.title
          });

          // Resolve doctor name - if doctor_name is not provided or is a UUID, try to resolve it
          let resolvedDoctorName = appointment.doctor_name || 'Dr. Non assigné';

          // Check if doctor_name looks like a UUID (contains hyphens and is 36 chars)
          if (appointment.doctor_name && appointment.doctor_name.includes('-') && appointment.doctor_name.length === 36) {
            console.warn('🔍 Doctor name appears to be a UUID, using fallback:', appointment.doctor_name);
            resolvedDoctorName = 'Dr. Non assigné';
          }

          // If doctor_assigned is available and doctor_name is not, try to use it
          if (!appointment.doctor_name && appointment.doctor_assigned) {
            // If doctor_assigned doesn't look like a UUID, use it as the name
            if (!appointment.doctor_assigned.includes('-') || appointment.doctor_assigned.length !== 36) {
              resolvedDoctorName = `Dr. ${appointment.doctor_assigned}`;
            }
          }

          const event = {
            id: appointment.id,
            title: appointment.title || appointment.patient_name || 'Rendez-vous',
            start: new Date(`${appointment.appointment_date}T${appointment.appointment_time}`),
            end: new Date(`${appointment.appointment_date}T${appointment.appointment_time}`), // Will be calculated
            color: appointment.color || '#3b82f6',
            roomId: mapResourceIdToRoomId(appointment.resource_id), // Properly map resource_id to roomId
            type: appointment.appointment_type,
            status: appointment.status,
            duration: appointment.duration_minutes || 30,
            consultationDuration: appointment.consultation_duration || appointment.duration_minutes || 30,
            patient_phone: appointment.patient_phone,
            doctor: appointment.doctor_assigned,
            docteur: resolvedDoctorName, // Use resolved doctor name
            resource_id: appointment.resource_id, // Keep original resource_id for backend compatibility
            // Include patient ID fields for navigation
            // Note: API now returns 'patient' field with UUID
            patient: appointment.patient,
            patient_id: appointment.patient_id || appointment.patient,
          };

          // Debug logging for today's events
          if (appointment.appointment_date === today.toISOString().split('T')[0]) {
            console.log('🔄 This_Day: Converting today\'s appointment:', {
              id: appointment.id,
              title: event.title,
              time: appointment.appointment_time,
              roomId: event.roomId,
              originalResourceId: appointment.resource_id,
              patient_id: event.patient_id,
              patient: event.patient,
              appointmentPatientId: appointment.patient_id,
              appointmentPatient: appointment.patient,
              fullAppointment: appointment
            });
          }

          return event;
        });

        setLocalEvents(calendarEvents);

        // Skip waiting list API call - endpoints don't exist on backend
        console.log('⚠️ Skipping waiting list API call - endpoints not available');

        // Use empty waiting list as fallback
        const convertedWaitingList: WaitingListPatient[] = [];
        setWaitingList(convertedWaitingList);

      } catch (error) {
        console.error('Failed to fetch events:', error);
      }
    };

    // Only fetch if no events are provided from props
    if (!events || events.length === 0) {
      fetchEvents();
    }
  }, [events]);

  // Working edit functionality states from CetteJournee
 
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<WaitingListPatient | null>(null);
  const [currentPatient, setCurrentPatient] = useState<WaitingListPatient | null>(null);
  const [EditwaitingListOpened, { open: EditwaitingList, close: closeEditwaitingList }] = useDisclosure(false);

  // Note: Presentation room state variables are declared later in the file to avoid duplicates

  // Form for patient editing (based on AjouterUnRendezVous pattern)
  const patientForm = useForm({
    initialValues: {
      title: '',
      first_name: '',
      last_name: '',
      phone_numbers: '',
      duration: 30,
      notes: '',
      birth_date: '',
      age: 0,
      gender: '',
      cin: '',
      socialSecurity: '',
      etatCivil: '',
      docteur: '',
      resourceId: '',
      typeConsultation: '',
      date: '',
      comment: '',
      agenda: '',
      profession: '',
      birthPlace: '',
      fatherName: '',
      motherName: '',
      bloodGroup: '',
      allergies: '',
      commentairelistedattente: '',
      checkedAppelvideo: false,
      checkedRappelSms: false,
      checkedRappelEmail: false,
      addToWaitingList: false,
      removeFromCalendar: false,
      moveToPresentation: false,
    }
  });

  // Sidebar alert state for main component
  const [isSidebarAlert, setIsSidebarAlert] = useState(false);

  // Function to toggle sidebar alert
  const toggleSidebarAlert = () => {
    setIsSidebarAlert(!isSidebarAlert);
  };

  // Search value for sidebar tree filtering
  const [searchValue, ] = useState('');

  // Handle sidebar selection
  const handleSidebarSelect = (value: string) => {
    console.log('Selected from main sidebar:', value);
    // Add your sidebar selection logic here
  };

  // Working edit patient function from CetteJournee
  const openEditPatient = (patient: WaitingListPatient) => {
    console.log('Opening edit for patient:', patient);
    setCurrentPatient(patient);

    // Populate the form with all patient data
    patientForm.setValues({
      title: patient.title || '',
      first_name: patient.first_name || '',
      last_name: patient.last_name || '',
      phone_numbers: patient.phone_number || patient.phone_numbers || '',
      duration: patient.duration || patient.consultationDuration || 30,
      notes: patient.notes || '',
      birth_date: patient.birth_date || '',
      age: patient.age || 0,
      gender: patient.gender || '',
      cin: patient.cin || '',
      socialSecurity: patient.socialSecurity || '',
      etatCivil: patient.etatCivil || '',
      docteur: patient.docteur || '',
      resourceId: patient.resourceId?.toString() || '',
      typeConsultation: patient.typeConsultation || '',
      date: patient.date || '',
      comment: patient.comment || '',
      agenda: patient.agenda || '',
      profession: patient.profession || '',
      birthPlace: patient.birthPlace || '',
      fatherName: patient.fatherName || '',
      motherName: patient.motherName || '',
      bloodGroup: patient.bloodGroup || '',
      allergies: patient.allergies || '',
      commentairelistedattente: patient.commentairelistedattente || '',
      checkedAppelvideo: patient.checkedAppelvideo || false,
      checkedRappelSms: patient.checkedRappelSms || false,
      checkedRappelEmail: patient.checkedRappelEmail || false,
      // Reset switches to false when opening edit modal
      addToWaitingList: false,
      removeFromCalendar: false,
      moveToPresentation: false,
    });

    // Open the edit modal
    EditwaitingList();
  };

  // Handle patient update (based on AjouterUnRendezVous pattern)
  const handleUpdatePatient = async (values: {
    title: string;
    first_name: string;
    last_name: string;
    phone_numbers: string;
    duration: number;
    notes: string;
    birth_date: string;
    age: number;
    gender: string;
    cin: string;
    socialSecurity: string;
    etatCivil: string;
    docteur: string;
    resourceId: string;
    typeConsultation: string;
    date: string;
    comment: string;
    agenda: string;
    profession: string;
    birthPlace: string;
    fatherName: string;
    motherName: string;
    bloodGroup: string;
    allergies: string;
    commentairelistedattente: string;
    checkedAppelvideo: boolean;
    checkedRappelSms: boolean;
    checkedRappelEmail: boolean;
    addToWaitingList: boolean;
    removeFromCalendar: boolean;
    moveToPresentation: boolean;
  }) => {
    console.log('🔍 handleUpdatePatient called with values:', values);
    console.log('🔍 Switch states - addToWaitingList:', values.addToWaitingList, 'moveToPresentation:', values.moveToPresentation);

    if (!currentPatient) {
      console.error('❌ No current patient to update');
      return;
    }

    const updatedPatient: WaitingListPatient = {
      ...currentPatient,
      title: values.title,
      first_name: values.first_name,
      last_name: values.last_name,
      phone_number: values.phone_numbers,
      phone_numbers: values.phone_numbers,
      duration: values.duration,
      notes: values.notes,
      birth_date: values.birth_date,
      age: values.age,
      gender: values.gender,
      cin: values.cin,
      socialSecurity: values.socialSecurity,
      etatCivil: values.etatCivil,
      docteur: values.docteur,
      resourceId: values.resourceId,
      typeConsultation: values.typeConsultation,
      date: values.date,
      comment: values.comment,
      agenda: values.agenda,
      profession: values.profession,
      birthPlace: values.birthPlace,
      fatherName: values.fatherName,
      motherName: values.motherName,
      bloodGroup: values.bloodGroup,
      allergies: values.allergies,
      commentairelistedattente: values.commentairelistedattente,
      checkedAppelvideo: values.checkedAppelvideo,
      checkedRappelSms: values.checkedRappelSms,
      checkedRappelEmail: values.checkedRappelEmail,
      consultationDuration: values.duration,
  
    };

    // Handle different actions based on form values
    if (values.moveToPresentation) {
      console.log('🔄 Moving to presentation room');
      // Move to presentation room
      await moveToCalendarWithOptions(updatedPatient, { moveToPresentation: true });
    } else if (values.addToWaitingList) {
      console.log('🔄 Adding to calendar but keeping in waiting list');
      // Add to calendar but keep in waiting list
      setWaitingList(prev => prev.map(p => p.id === currentPatient.id ? updatedPatient : p));
      await moveToCalendarWithOptions(updatedPatient, { addToWaitingList: true });
    } else {
      console.log('🔄 Standard update');
      // Standard update
      setWaitingList(prev => prev.map(p => p.id === currentPatient.id ? updatedPatient : p));

      notifications.show({
        title: 'Patient mis à jour',
        message: `${values.first_name} ${values.last_name} a été mis à jour avec succès`,
        color: 'green',
        autoClose: 3000
      });
    }

    // Close the modal
    closeEditwaitingList();
    setCurrentPatient(null);
  };

  // Enhanced move to calendar function with backend API integration
  const moveToCalendarWithOptions = async (patient: WaitingListPatient, options: {
    addToWaitingList?: boolean;
    removeFromCalendar?: boolean;
    moveToPresentation?: boolean;
  } = {}) => {
    console.log('Moving patient to calendar with options:', patient, options);

    // Create calendar event
    const now = new Date();
    const startTime = patient.date && patient.start
      ? new Date(`${patient.date}T${patient.start}`)
      : now;
    const endTime = new Date(startTime.getTime() + (patient.duration || 30) * 60 * 1000);

    const calendarEvent: CalendarEvent = {
      id: patient.id,
      title: `${patient.first_name} ${patient.last_name}`,
      start: startTime,
      end: endTime,
      desc: `Consultation avec ${patient.first_name} ${patient.last_name}`,
      isActive: options.moveToPresentation || false,
    };

    console.log('Created calendar event:', calendarEvent);

    try {
      // Import API dynamically
      const { appointmentStateAPI } = await import('@/services/api');

      // Handle different scenarios based on options
      if (options.addToWaitingList) {
        // Keep in waiting list and also add to calendar
        console.log('🔍 Adding to calendar while keeping in waiting list');

        // Update backend: ensure appointment is marked as in waiting list
        await appointmentStateAPI.toggleWaitingList(patient.id, true);
        console.log('✅ Backend updated: appointment marked as in waiting list');

        if (onEventAdd) {
          onEventAdd(calendarEvent);
          console.log('✅ Successfully called onEventAdd for calendar');
          notifications.show({
            title: 'Ajouté au calendrier',
            message: `${patient.first_name} ${patient.last_name} a été ajouté au calendrier (reste en liste d'attente)`,
            color: 'blue',
            autoClose: 3000
          });
        } else {
          console.log('❌ onEventAdd callback not available');
          notifications.show({
            title: 'Erreur',
            message: 'Impossible d\'ajouter au calendrier - fonction non disponible',
            color: 'red',
            autoClose: 3000
          });
        }
      } else if (options.moveToPresentation) {
        // Move to presentation room (activate visit)
        await appointmentStateAPI.togglePresentationRoom(patient.id, true);
        console.log('✅ Backend updated: appointment activated for presentation room');
        activateAppointment(patient);
      } else {
        // Standard move to calendar (remove from waiting list)
        await appointmentStateAPI.toggleWaitingList(patient.id, false);
        console.log('✅ Backend updated: appointment removed from waiting list');

        setWaitingList(prev => prev.filter(p => p.id !== patient.id));

        if (onEventAdd) {
          onEventAdd(calendarEvent);
          notifications.show({
            title: 'Déplacé vers le calendrier',
            message: `${patient.first_name} ${patient.last_name} a été déplacé vers le calendrier`,
            color: 'green',
            autoClose: 3000
          });
        } else {
          // Add back to waiting list if move failed
          setWaitingList(prev => [...prev, patient]);
          notifications.show({
            title: 'Erreur',
            message: 'Impossible de déplacer vers le calendrier - fonction non disponible',
            color: 'red',
            autoClose: 3000
          });
        }
      }
    } catch (error) {
      console.error('Error updating appointment state:', error);
      notifications.show({
        title: 'Erreur',
        message: error instanceof Error ? error.message : 'Impossible de mettre à jour l\'état du rendez-vous',
        color: 'red',
        autoClose: 5000
      });
    }
  };

  // Activate an appointment - from CetteJournee.tsx pattern
  const activateAppointment = (appointment: WaitingListPatient) => {
    console.log('🔍 activateAppointment called for:', appointment.first_name, appointment.last_name, 'isActive:', appointment.isActive);

    // If activating, move to presentation room
    if (!appointment.isActive) {
      console.log('🔄 Activating appointment - moving to presentation room');

      // Check if already in presentation room to prevent duplicates
      setWaitingRoomVisits(prev => {
        const existingIndex = prev.findIndex(visit => visit.id === appointment.id);
        if (existingIndex !== -1) {
          console.log('⚠️ Appointment already in presentation room, skipping');
          return prev;
        }

        // Remove from waiting list
        setWaitingList(waitingPrev => waitingPrev.filter(p => p.id !== appointment.id));

        // Add to presentation room
        const presentationRoomEvent: CalendarEvent = {
          id: appointment.id,
          title: `${appointment.first_name} ${appointment.last_name}`,
          start: new Date(),
          end: new Date(Date.now() + (appointment.duration || 30) * 60 * 1000),
          desc: `Visite active - ${appointment.first_name} ${appointment.last_name}`,
          isActive: true,
          eventType: 'visitor-counter',
          first_name: appointment.first_name,
          last_name: appointment.last_name,
          phone_number: appointment.phone_number,
          duration: appointment.duration,
          notes: appointment.notes,
        };

        console.log('✅ Added to presentation room:', presentationRoomEvent);
        return [...prev, presentationRoomEvent];
      });

      notifications.show({
        title: 'Visite activée',
        message: `${appointment.first_name} ${appointment.last_name} a été déplacé vers la salle de présence`,
        color: 'green',
        autoClose: 3000
      });
    } else {
      console.log('🔄 Deactivating appointment - moving back to waiting list');

      // Remove from presentation room
      setWaitingRoomVisits(prev => prev.filter(visit => visit.id !== appointment.id));

      // Add back to waiting list if not already there
      setWaitingList(prev => {
        const existingIndex = prev.findIndex(patient => patient.id === appointment.id);
        if (existingIndex !== -1) {
          console.log('⚠️ Patient already in waiting list, skipping');
          return prev;
        }

        const waitingListPatient: WaitingListPatient = {
          id: appointment.id,
          first_name: appointment.first_name,
          last_name: appointment.last_name,
          typeConsultation: appointment.typeConsultation,
          eventType: appointment.eventType || 'visit',
          phone_number: appointment.phone_number,
          start: appointment.start,
          duration: appointment.duration,
          color: appointment.color,
          docteur: appointment.docteur,
          date: appointment.date,
          title: appointment.title,
          end: appointment.end,
          desc: appointment.desc,
          address: appointment.address,
          notes: appointment.notes,
          isActive: false,
        };

        console.log('✅ Added back to waiting list:', waitingListPatient);
        return [...prev, waitingListPatient];
      });

      notifications.show({
        title: 'Visite désactivée',
        message: `${appointment.first_name} ${appointment.last_name} a été remis en liste d'attente`,
        color: 'orange',
        autoClose: 3000
      });
    }
  };

  // Update local events when props change
  useEffect(() => {
    console.log('🔄 This_Day: Events prop changed, updating localEvents:', {
      newEventsLength: events?.length || 0,
      currentLocalEventsLength: localEvents?.length || 0,
      firstNewEvent: events?.[0]?.title || 'none'
    });
    setLocalEvents(events);
  }, [events, localEvents?.length]);

  // Update local waiting list when external prop changes
  useEffect(() => {
    if (externalWaitingList) {
      setWaitingList(externalWaitingList);
    }
  }, [externalWaitingList]);

  

  // State for patient details modal
  const [selectedPatient, setSelectedPatient] = useState<WaitingListPatient | null>(null);
  const [patientDetailsOpened, { open: openPatientDetailsModal, close: closePatientDetailsModal }] = useDisclosure(false);

  const openPatientDetails = (patient?: WaitingListPatient) => {
    console.log('Open patient details for:', patient);
    if (patient) {
      console.log('Setting selectedPatient to:', patient);
      setSelectedPatient(patient);
      openPatientDetailsModal();
    } else {
      console.log('No patient data provided to openPatientDetails');
    }
  };

  // Function to move patient from waiting list to calendar
 
// Event Component
const EventComponent = ({
  event,
  onEdit,
  onDelete,
  staffOptions = realStaffOptions, // Use the real staff options instead of hardcoded ones
  triggerOptions = [
    { label: 'Salle d\'attente', value: 'salle-attente' },
    { label: 'Démarrage de la visite', value: 'demarrage-visite' },
    { label: 'Fin de la visite', value: 'fin-visite' }
  ],
  onToggleSidebar
}: {
  event: CalendarEvent;
  onEdit?: (event: CalendarEvent) => void;
  onDelete?: (event: CalendarEvent) => void;
  staffOptions?: { label: string; value: string }[];
  triggerOptions?: { label: string; value: string }[];
  onToggleSidebar?: () => void;
}) => {

  // Function to resolve doctor name from UUID or value
  const resolveDoctorName = (doctorValue: string | undefined): string => {
    if (!doctorValue) return 'Dr. Non assigné';

    // Check if it's a UUID (36 chars with hyphens)
    const isUUID = doctorValue.includes('-') && doctorValue.length === 36;

    if (isUUID) {
      // Try to find doctor by UUID in staffOptions (if they have UUID values)
      const foundStaff = staffOptions.find(staff => staff.value === doctorValue);
      if (foundStaff) {
        return foundStaff.label;
      }
      // If UUID not found in staffOptions, return default
      return 'Dr. Non assigné';
    }

    // If not a UUID, try to find by value
    const foundStaff = staffOptions.find(staff => staff.value === doctorValue);
    if (foundStaff) {
      return foundStaff.label;
    }

    // If it's already a readable name (doesn't look like a UUID), use it
    if (!doctorValue.includes('-')) {
      return doctorValue.startsWith('Dr.') ? doctorValue : `Dr. ${doctorValue}`;
    }

    return 'Dr. Non assigné';
  };
   
 const [isAlertsModalOpen, setIsAlertsModalOpen] = useState(false);
   const [isAlertsAddModalOpen, setIsAlertsAddModalOpen] = useState(false);
   const [isSidebarAlert, setIsSidebarAlert] = useState(false);
   const [isSidebarVisible, setIsSidebarVisible] = useState(false);
   const [, setViewPatient] = useState<CalendarEvent | null>(null);
   const [, setInfoModalOpen] = useState(false);
   // Add delete confirmation modal states
   const [isDeleteConfirmModalOpen, setIsDeleteConfirmModalOpen] = useState(false);
   const [eventToDelete, setEventToDelete] = useState<CalendarEvent | null>(null);
 const [isChoixMultipleModalOpen, setIsChoixMultipleModalOpen] = useState(false);
    // États pour la gestion des modèles
      const [showModels, setShowModels] = useState(false);
      const [showAddModel, setShowAddModel] = useState(false);
      const [modelTitle, setModelTitle] = useState('');
      const [editingModelId, setEditingModelId] = useState<string | null>(null);
      const [savedModels, setSavedModels] = useState<Array<{id: string, title: string, selections: string[], selected?: boolean}>>([]);
         const [isLoadingStaff, ] = useState(false);
         
           const [isMicrophoneModalOpen, setIsMicrophoneModalOpen] = useState(false);
           const [isClipboardTextModalOpen, setIsClipboardTextModalOpen] = useState(false);

       // Fonctions pour gérer la sidebar
      //  const toggleSidebarAlert = () => {
      //    setIsSidebarAlert(!isSidebarAlert);
      //  };

//   const [searchValue, setSearchValue] = useState('');
//  const handleSidebarSelect = (value: string) => {
//    console.log('Selected from sidebar:', value);

//    // 1. Ajouter la sélection au formulaire en cours d'édition
//    const currentDescription = form.values.description || '';
//    const newFormDescription = currentDescription
//      ? `${currentDescription}, ${value}`
//      : value;
//    form.setFieldValue('description', newFormDescription);

//    // 2. Si une alerte est en cours d'édition, mettre à jour aussi ses données
//    if (currentEditingAlertId) {
//      setAlertsData(prevData =>
//        prevData.map(alert => {
//          if (alert.id === currentEditingAlertId) {
//            const currentAlertDescription = alert.Description || '';
//            const newAlertDescription = currentAlertDescription
//              ? `${currentAlertDescription}, ${value}`
//              : value;
//            return {
//              ...alert,
//              Description: newAlertDescription
//            };
//          }
//          return alert;
//        })
//      );
//    }

//    console.log('Added to form description:', newFormDescription);

//    // Optionnel : fermer la sidebar après sélection
//    // setIsSidebarVisible(false);
//  };


           // Fonctions pour la reconnaissance vocale
       const initSpeechRecognition = () => {
         if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
           const SpeechRecognitionConstructor = (window as unknown as {
             webkitSpeechRecognition: new () => SpeechRecognitionInstance;
             SpeechRecognition: new () => SpeechRecognitionInstance;
           }).webkitSpeechRecognition || (window as unknown as {
             webkitSpeechRecognition: new () => SpeechRecognitionInstance;
             SpeechRecognition: new () => SpeechRecognitionInstance;
           }).SpeechRecognition;

           const newRecognition = new SpeechRecognitionConstructor();

           newRecognition.continuous = true;
           newRecognition.interimResults = true;
           newRecognition.lang = 'fr-FR';

           newRecognition.onstart = () => {
             setIsListening(true);
             setMicrophoneColor('green'); // Changer la couleur en vert
             setInvalidSpeech('Écoute en cours...');
           };

           newRecognition.onresult = (event: unknown) => {
             const speechEvent = event as {
               resultIndex: number;
               results: {
                 length: number;
                 [index: number]: {
                   isFinal: boolean;
                   [index: number]: { transcript: string };
                 };
               };
             };

             let finalTranscript = '';
             let interimTranscript = '';

             for (let i = speechEvent.resultIndex; i < speechEvent.results.length; i++) {
               const transcript = speechEvent.results[i][0].transcript;
               if (speechEvent.results[i].isFinal) {
                 finalTranscript += transcript;
               } else {
                 interimTranscript += transcript;
               }
             }

             setValidSpeech(finalTranscript);
             setInvalidSpeech(interimTranscript || 'Parlez maintenant.');
           };

           newRecognition.onerror = () => {
             setIsListening(false);
             setMicrophoneColor('#3799CE'); // Remettre la couleur par défaut en cas d'erreur
             setInvalidSpeech('Erreur de reconnaissance vocale');
           };

           newRecognition.onend = () => {
             setIsListening(false);
             setMicrophoneColor('#3799CE'); // Remettre la couleur par défaut
             setInvalidSpeech('Parlez maintenant.');
           };

           setRecognition(newRecognition);
         }
       };
       useEffect(() => {
            initSpeechRecognition();
          }, []);

 const emptyContent = () => {
         setValidSpeech('');
         setInvalidSpeech('Parlez maintenant.');
         setMicrophoneColor('#3799CE'); // Remettre la couleur par défaut
         if (recognition && isListening) {
           recognition.stop();
         }
       };
               // Fonction pour obtenir les sélections actuelles
       const getSelectedValues = () => {
         const getAllNodes = (nodes: TreeNodeChoixMultiple[]): TreeNodeChoixMultiple[] => {
           const result: TreeNodeChoixMultiple[] = [];
           nodes.forEach(node => {
             result.push(node);
             if (node.nodes) {
               result.push(...getAllNodes(node.nodes));
             }
           });
           return result;
         };

         const allNodes = getAllNodes(exampleData);
         return Array.from(selectedNodes)
           .map(id => allNodes.find(node => node.uid === id))
           .filter(Boolean)
           .map(node => node!.value);
       };
       const handleSaveModel = () => {
         if (modelTitle.trim()) {
           if (editingModelId) {
             // Mode édition : mettre à jour le modèle existant
             setSavedModels(prev => prev.map(model =>
               model.id === editingModelId
                 ? { ...model, title: modelTitle.trim() }
                 : model
             ));
             setEditingModelId(null);
             console.log('Model title updated for ID:', editingModelId);
           } else {
             // Mode création : créer un nouveau modèle
             const selectedValues = getSelectedValues();
             const newModel = {
               id: `model-${Date.now()}`,
               title: modelTitle.trim(),
               selections: selectedValues
             };
             setSavedModels(prev => [...prev, newModel]);
             setSelectedNodes(new Set());
             console.log('New model created:', newModel);
           }

           setModelTitle('');
           setShowAddModel(false);
           // Afficher les modèles après sauvegarde
           setShowModels(true);
         }
       };
  // État pour gérer les sélections multiples
      const [selectedNodes, setSelectedNodes] = useState<Set<string>>(new Set());
   // État pour savoir quelle alerte est en cours d'édition
          const [currentEditingAlertId, setCurrentEditingAlertId] = useState<string | null>(null);

          // États pour le modal de confirmation de suppression
          const [alertToDelete, setAlertToDelete] = useState<string | null>(null);

          // Initialiser avec tous les nœuds ouverts par défaut (false = ouvert)
     const [collapsedNodes, setCollapsedNodes] = useState<Record<string, boolean>>(() => {
       console.log('TreeItemChoixMultiple initialized with all nodes open');
       return {}; // Tous les nœuds sont ouverts par défaut (pas besoin de les lister)
     });
      // Fonction pour basculer l'effondrement d'un nœud (modifiée pour ne jamais fermer)
     const toggleNodeCollapse = (nodeId: string) => {
       setCollapsedNodes(prev => {
         const currentState = prev[nodeId] ?? false; // false = ouvert par défaut
         // Ne fermer jamais les nœuds, seulement les ouvrir s'ils sont fermés
         if (currentState === true) { // Si fermé, ouvrir
           console.log('Opening TreeItemChoixMultiple node:', nodeId);
           return {
             ...prev,
             [nodeId]: false // false = ouvert
           };
         } else {
           console.log('TreeItemChoixMultiple node already open, not closing:', nodeId);
           return prev; // Ne rien changer si déjà ouvert
         }
       });
     };


       const toggleRecognition = () => {
         if (!recognition) {
           initSpeechRecognition();
           return;
         }

         if (isListening) {
           recognition.stop();
         } else {
           recognition.start();
         }
       };
       
    const exampleData: TreeNodeChoixMultiple[] = [
         {
           uid: '1',
           value: 'Alertes',
           nodes: [
             { uid: '1-1', value: 'Allaitante depuis:' },
             { uid: '1-2', value: 'Allergique à l\'Aspirine' },

             { uid: '1-3', value: 'Allergique à la Pénicilline' },
             { uid: '1-4', value: 'Arthrose' },
             { uid: '1-5', value: 'Cardiaque Anticoagulant sintrom' },
             { uid: '1-6', value: 'Diabétique NID' },
             { uid: '1-7', value: 'Enceinte depuis:' },
             { uid: '1-8', value: 'Diabétique ID' },
             { uid: '1-9', value: 'Gastralgie : ulcère anti-inflammatoire' },
             { uid: '1-10', value: 'Hypertension' },
              { uid: '1-11', value: 'Hypotension' },
             { uid: '1-12', value: 'Thyroïde' },


           ],
         },

       ];
       const toggleNodeSelection = (nodeId: string) => {
         setSelectedNodes(prev => {
           const newSet = new Set(prev);

           // Trouver le nœud correspondant
           const findNode = (nodes: TreeNodeChoixMultiple[], id: string): TreeNodeChoixMultiple | null => {
             for (const node of nodes) {
               if (node.uid === id) return node;
               if (node.nodes) {
                 const found = findNode(node.nodes, id);
                 if (found) return found;
               }
             }
             return null;
           };

           const currentNode = findNode(exampleData, nodeId);

           if (newSet.has(nodeId)) {
             // Désélectionner le nœud et tous ses enfants
             newSet.delete(nodeId);
             if (currentNode?.nodes) {
               const removeAllChildren = (nodes: TreeNodeChoixMultiple[]) => {
                 nodes.forEach(child => {
                   newSet.delete(child.uid);
                   if (child.nodes) {
                     removeAllChildren(child.nodes);
                   }
                 });
               };
               removeAllChildren(currentNode.nodes);
             }
           } else {
             // Sélectionner le nœud et tous ses enfants
             newSet.add(nodeId);
             if (currentNode?.nodes) {
               const addAllChildren = (nodes: TreeNodeChoixMultiple[]) => {
                 nodes.forEach(child => {
                   newSet.add(child.uid);
                   if (child.nodes) {
                     addAllChildren(child.nodes);
                   }
                 });
               };
               addAllChildren(currentNode.nodes);
             }
           }

           return newSet;
         });
       };

       
            const handleEditModel = (modelId: string) => {
         const modelToEdit = savedModels.find(model => model.id === modelId);
         if (modelToEdit) {
           setModelTitle(modelToEdit.title);
           setEditingModelId(modelId);
           setShowModels(false);
           setShowAddModel(true);
           console.log('Editing model:', modelToEdit);
         }
       };
   const handleDeleteModel = (modelId: string) => {
         setSavedModels(prev => prev.filter(model => model.id !== modelId));
         setSelectedNodes(prev => {
           const newSet = new Set(prev);
           newSet.delete(modelId);
           return newSet;
         });
       };
        const handleValidate = () => {
         let textToAdd = '';

         if (showModels) {
           // Valider les modèles sélectionnés
           const selectedModelTexts = savedModels
             .filter(model => model.selected === true)
             .flatMap(model => model.selections);
           textToAdd = selectedModelTexts.join(', ');
           console.log('Selected models:', savedModels.filter(model => model.selected === true));
           console.log('Text to add from models:', textToAdd);
         } else {
           // Valider les sélections du dictionnaire
           const selectedValues = getSelectedValues();
           textToAdd = selectedValues.join(', ');
         }

         if (textToAdd) {
           // 1. Ajouter le texte au champ de reconnaissance vocale (à la place de "Parlez maintenant")
           setValidSpeech(textToAdd);
           setInvalidSpeech(''); // Effacer le texte "Parlez maintenant"

           // 2. Ajouter le texte au champ description du formulaire
           const currentDescription = form.values.description || '';
           const newDescription = currentDescription
             ? `${currentDescription}, ${textToAdd}`
             : textToAdd;
           console.log('Setting form description:', { currentDescription, textToAdd, newDescription });
           form.setFieldValue('description', newDescription);

           // 3. Ajouter le texte à la description de l'alerte en cours d'édition dans la table
           // Utiliser la nouvelle description mise à jour
           const combinedText = newDescription;
           console.log('Combined text for alert:', combinedText);

           if (currentEditingAlertId) {
             setAlertsData(prevData =>
               prevData.map(alert => {
                 if (alert.id === currentEditingAlertId) {
                   return {
                     ...alert,
                     Description: combinedText
                   };
                 }
                 return alert;
               })
             );
           } else {
             // Si aucune alerte n'est en cours d'édition, mettre à jour la première par défaut
             setAlertsData(prevData =>
               prevData.map(alert => {
                 if (alert.id === '1') {
                   return {
                     ...alert,
                     Description: combinedText
                   };
                 }
                 return alert;
               })
             );
           }
         }



         // Fermer le modal et réinitialiser
         setIsClipboardTextModalOpen(false);
         setIsChoixMultipleModalOpen(false);
         setShowModels(false);
         setSelectedNodes(new Set());
         // Réinitialiser l'ID de l'alerte en cours d'édition
         setCurrentEditingAlertId(null);
       };

       const handleCancel = () => {
         // Réinitialiser tous les états
         setSelectedNodes(new Set());
         setShowModels(false);
         setShowAddModel(false);
         setModelTitle('');
         setEditingModelId(null);
         setIsClipboardTextModalOpen(false);
       };
          // Form object will be defined later

       // Fonctions pour sélectionner/désélectionner tous les nœuds
       const selectAllNodes = () => {
         const allNodeIds: string[] = [];

         const collectAllIds = (nodes: TreeNodeChoixMultiple[]) => {
           nodes.forEach(node => {
             allNodeIds.push(node.uid);
             if (node.nodes && node.nodes.length > 0) {
               collectAllIds(node.nodes);
             }
           });
         };

         collectAllIds(exampleData);
         setSelectedNodes(new Set(allNodeIds));
       };

       const deselectAllNodes = () => {
         setSelectedNodes(new Set());
       };




          const handleDeleteAlert = (alertId: string) => {
            console.log('Delete alert:', alertId);
            // Ouvrir le modal de confirmation
            setAlertToDelete(alertId);
            setIsDeleteConfirmModalOpen(true);
          };

          // Fonction pour confirmer la suppression
          const confirmDeleteAlert = () => {
            if (alertToDelete) {
              // Supprimer l'alerte de la liste
              setAlertsData(prevData => prevData.filter(alert => alert.id !== alertToDelete));
              console.log('Alert deleted:', alertToDelete);
            }
            // Fermer le modal et réinitialiser
            setIsDeleteConfirmModalOpen(false);
            setAlertToDelete(null);
          };

          // Fonction pour annuler la suppression
          const cancelDeleteAlert = () => {
            setIsDeleteConfirmModalOpen(false);
            setAlertToDelete(null);
          };

          // Handlers for appointment deletion confirmation
          const confirmDeleteAppointment = (e: React.MouseEvent) => {
            e.stopPropagation();
            if (eventToDelete && onDelete) {
              console.log('🗑️ User confirmed deletion of:', eventToDelete.title);
              onDelete(eventToDelete);
            }
            // Close modal and reset
            setIsDeleteConfirmModalOpen(false);
            setEventToDelete(null);
          };

          const cancelDeleteAppointment = (e: React.MouseEvent) => {
            e.stopPropagation();
            console.log('🚫 User cancelled deletion');
            setIsDeleteConfirmModalOpen(false);
            setEventToDelete(null);
          };

          // Separate function for modal onClose (no parameters)
          const handleCloseDeleteConfirmModal = () => {
            console.log('🚫 User closed deletion modal');
            setIsDeleteConfirmModalOpen(false);
            setEventToDelete(null);
          };

          // Fonction pour obtenir la couleur selon le niveau
          const getLevelColor = (level: string) => {
            switch(level) {
              case 'MINIMUM': return 'green';
              case 'MEDIUM': return 'orange';
              case 'HIGH': return 'red';
              default: return 'gray';
            }
          };



        // Fonction pour créer les actions d'une alerte
                const createAlertActions = (alertId: string) => (
                  <Group gap="xs">
                    <ActionIcon
                      variant="subtle"
                      color="blue"
                      size="sm"
                      onClick={() => {
                        console.log('Edit alert clicked:', alertId);
                        setCurrentEditingAlertId(alertId);

                        // Trouver l'alerte à éditer et pré-remplir le formulaire
                        const alertToEdit = alertsData.find(alert => alert.id === alertId);
                        console.log('Alert to edit found:', alertToEdit);
                        if (alertToEdit) {
                          // Trouver la valeur correspondante pour le trigger
                          const triggerValue = triggerOptions.find(option => option.label === alertToEdit.Declencheur)?.value || '';

                          console.log('Editing alert:', alertToEdit);
                          console.log('Trigger value found:', triggerValue);

                          form.setValues({
                            trigger_for: alertToEdit.trigger_for || [], // Récupérer depuis les données existantes
                            trigger: triggerValue,
                            level: alertToEdit.level,
                            description: alertToEdit.Description,
                            is_permanent: alertToEdit.is_permanent
                          });

                          console.log('Form values set for editing:', {
                            trigger_for: alertToEdit.trigger_for || [],
                            trigger: triggerValue,
                            level: alertToEdit.level,
                            description: alertToEdit.Description,
                            is_permanent: alertToEdit.is_permanent
                          });

                          console.log('Form values set:', form.values);
                        }

                        setIsAlertsAddModalOpen(true);
                        setIsSidebarAlert(true); // Ouvrir aussi la sidebar pour les sélections
                      }}
                    >
                      <Icon path={mdiPencil} size={0.8} color={'#3799CE'}/>
                    </ActionIcon>
                    <ActionIcon
                      variant="subtle"
                      color="red"
                      size="sm"
                      onClick={() => {
                        console.log('Delete alert clicked:', alertId);
                        handleDeleteAlert(alertId);
                      }}
                    >
                      <Icon path={mdiDelete} size={0.8} color={'red'}/>
                    </ActionIcon>
                  </Group>
                );

 // Alerts table - État pour pouvoir modifier les descriptions (données seulement)
          const [alertsData, setAlertsData] = useState<AlertData[]>([]);

          // Créer les éléments avec les actions pour le rendu
          const elements = alertsData.map(alert => ({
            ...alert,
            Niveau: <Icon path={mdiCircle} size={1} color={getLevelColor(alert.level)}/>,
            Publique: <Icon path={mdiCircle} size={1} color={'green'}/>,
            Permanente: <Icon path={mdiCircle} size={1} color={alert.is_permanent ? 'green' : 'red'}/>,
            Actions: createAlertActions(alert.id)
          }));
      const rows = elements.map((element) => (
          <Table.Tr key={element.id}>
            <Table.Td w={'150px'}>{element.Declencheur}</Table.Td>
            <Table.Td>{element.Niveau}</Table.Td>
            <Table.Td>{element.Publique}</Table.Td>
            <Table.Td>{element.Permanente}</Table.Td>
            <Table.Td>{element.Description}</Table.Td>
            <Table.Td w={'100px'}>{element.Actions}</Table.Td>
          </Table.Tr>
        ));
         const form = useForm<AlertFormValues>({
          initialValues: {
            trigger_for: [],
            trigger: '',
            level: 'MINIMUM',
            description: '',
            is_permanent: false,
          },
          validate: {
            trigger_for: (value) => (value.length === 0 ? 'Champ requis' : null),
            trigger: (value) => (!value ? 'Champ requis' : null),
            description: (value) => (!value ? 'Champ requis' : null),
          },
        });

      const handleAddReminder = (event: CalendarEvent) => {
        notifications.show({
          title: 'Reminder Added',
          message: `Reminder set for ${event.title}'s appointment`,
          color: 'blue',
        });
      };
     // Add this function with your other handlers
     const handleLastVisit = (event: CalendarEvent) => {
       // Check if the event has last visit data
       if (event?.lastVisit) {
         setViewPatient(event);
         setInfoModalOpen(true);
       } else {
         notifications.show({
           title: 'No Previous Visit',
           message: 'This patient has no recorded previous visits.',
           color: 'yellow',
           autoClose:1000
         });
       }
     };


	    // Local stubs to satisfy event actions within EventComponent scope
	    const activateAppointment = (e: CalendarEvent) => {
	      console.log('Activate/Toggle appointment (local stub):', e);
	      // TODO: wire to parent via a prop callback if needed
	    };
	    const rescheduleevent = (e: CalendarEvent) => {
	      console.log('Reschedule event (local stub):', e);
	    };

  // Add missing variables for the EventComponent
  const activeEvent_Ids = new Set<number>();
  const activeIds: number[] = [];
  const activeVisits: CalendarEvent[] = [];
const handleCloseSidebar = () => {
   setIsSidebarAlert(false);
 };
   // Fonction pour gérer la soumission du formulaire d'alerte
         const handleAlertSubmit = (values: AlertFormValues, autoTrigger: boolean) => {
           console.log('Alert form submitted:', values, 'Auto trigger:', autoTrigger);
           console.log('Current editing alert ID:', currentEditingAlertId);
           if (currentEditingAlertId) {
             // Mode édition : mettre à jour l'alerte existante
             console.log('Editing existing alert:', currentEditingAlertId);
             const updatedAlertData = {
               id: currentEditingAlertId,
               level: values.level,
               is_permanent: values.is_permanent,
               Declencheur: triggerOptions.find(option => option.value === values.trigger)?.label || values.trigger,
               Description: values.description,
               trigger_for: values.trigger_for
             };

             setAlertsData(prevData => {
               const updatedData = prevData.map(alert =>
                 alert.id === currentEditingAlertId ? updatedAlertData : alert
               );
               console.log('Updated alerts data (edit mode):', updatedData);
               return updatedData;
             });
           } else {
             // Mode ajout : créer une nouvelle alerte
             console.log('Adding new alert');
             const newAlertId = `alert-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
             const newAlertData = {
               id: newAlertId,
               level: values.level,
               is_permanent: values.is_permanent,
               Declencheur: triggerOptions.find(option => option.value === values.trigger)?.label || values.trigger,
               Description: values.description,
               trigger_for: values.trigger_for
             };

             setAlertsData(prevData => {
               const updatedData = [...prevData, newAlertData];
               console.log('Updated alerts data (add mode):', updatedData);
               return updatedData;
             });
           }
           // Fermer le modal et réinitialiser le formulaire
           setIsAlertsAddModalOpen(false);
           setIsSidebarAlert(false);
           setCurrentEditingAlertId(null);
           form.reset();
         };

             // State for real backend data
             const [realStaffOptions, ] = useState(staffOptions);
  // EventComponent should return JSX

       // États pour la reconnaissance vocale
       const [isListening, setIsListening] = useState(false);
       const [validSpeech, setValidSpeech] = useState('');
       const [invalidSpeech, setInvalidSpeech] = useState('Parlez maintenant.');
       const [recognition, setRecognition] = useState<SpeechRecognitionInstance | null>(null);
       const [microphoneColor, setMicrophoneColor] = useState('#3799CE'); // Couleur par défaut

         const [lunchTimeDeleteOpened, { open: openedlunchTimeDelete, close: closelunchTimeDelete }] = useDisclosure(false);
     const handleRemoveLunchEvent = (eventToRemove: CalendarEvent) => {
       console.log('🗑️ Removing lunch event:', eventToRemove);

       // Use the parent component's onEventDelete callback to remove the event
       if (onEventDelete) {
         onEventDelete(eventToRemove);
         console.log('✅ Event deletion requested via onEventDelete callback');
       } else {
         console.error('❌ onEventDelete callback not available');
         // Fallback: try to update local events (though this might not work)
         setLocalEvents((prevEvents: CalendarEvent[]) => {
           const newEvents = prevEvents.filter((e: CalendarEvent) => e.id !== eventToRemove.id);
           console.log('Fallback: Updated local events:', newEvents.length);
           return newEvents;
         });
       }

       notifications.show({
         title: 'Pause déjeuner supprimée',
         message: 'La pause déjeuner a été supprimée avec succès',
         color: 'green',
       });
     }
     const EVENT_TYPE_COLORS = {
       'visit': "#34D1BF",
       'visitor-counter': "#F17105",
       'completed': "#3799CE",
       'diagnosis': "#F3124E",
       'default': "var(--mantine-color-dark-0)",
       'ALICE': "red",
       'Analyse': "blue",
       'Consultation': "#FF5252", // Added red color
       'Consultation pesée': "#FF9E80", // Added light orange
       'Contrôle': "orange",
       'Débaguage': "purple",
       'ELECRTO': "green",
       'Echographie': "green",
       'LIPO': "green",
       'Motif 1': "green",
       'Motif 2': "green",
       'PRESSO': "green",
       'Regime': "green",
       'SOIN DE VISAGE': "green",
       'microneedling': "green",
       'Urgence': "#FF9800", // Added orange
       'Avis': "#9C27B0", // Added purple
       'Autre': "#4CAF50", // Added green
       'Re-diagnose': "#E91E63" // Added pink
     };
     const [rescheduleModalOpen, setRescheduleModalOpen] = useState(false);
       const [appointmentToReschedule, setAppointmentToReschedule] = useState<CalendarEvent | null>(null);
         const getEventTypeColor = (type: EventType | undefined) => {
           return type && EVENT_TYPE_COLORS[type as keyof typeof EVENT_TYPE_COLORS] ? EVENT_TYPE_COLORS[type as keyof typeof EVENT_TYPE_COLORS] : EVENT_TYPE_COLORS.default;
         }; 
         // Reschedule for next available time
         const rescheduleAppointment = (event: CalendarEvent) => {
           setAppointmentToReschedule(event);
           setRescheduleModalOpen(true);
           // For demo purposes, we'll just add a day
           const newStart = new Date(event.start);
           newStart.setDate(newStart.getDate() + 1);
         
           const newEnd = new Date(event.end);
           newEnd.setDate(newEnd.getDate() + 1);
         
           const updatedAppointment = {
             ...event,
             start: newStart,
             end: newEnd,
           };
         
           setAppointments(prevAppointments =>
             prevAppointments.map(app =>
               app.id === event.id ? updatedAppointment : app
             )
           );
         
           notifications.show({
             title: 'Appointment Rescheduled',
             message: `${event.title}'s appointment has been rescheduled`,
             color: 'blue',
             autoClose:1000
           });
         }; 
             const eventForm = useForm({
                initialValues: {
                  patientId: '',
                  notes: '',
                  date: new Date(),
                  duration: 15, // 30minutes
                  type: "visit",
                  resourceId:Number(eventResourceId),
                  addToWaitingList: false,
                  removeFromCalendar: false,
                  rescheduleDateTime: '', // Add this field
                },
              });
  return (
    <>
            {event.lunchTime  ?
            <div
           className="absolute left-1 right-1  text-xs rounded cursor-pointer hover:opacity-90 transition-all z-10 group"
          style={{
            backgroundColor: event.color || changeEndValuelunch || '#15AABF',  // ✅ Use event.color first, then fallback
            color: 'white',
            top: '2px',
            bottom: '2px'
          }}
           onClick={(e) => e.stopPropagation()}
          >
             <Group className="flex  items-center w-full  pl-1 h-[26px]">
                 <Group  className={isSidebarVisible ?  "w-[81%] ": "w-[91%]"}>
             <IconClock2 stroke={1.25} />
             <div className="font-bold " >{event.title}</div>
             </Group>
               <span onClick={(e) => {
             e.stopPropagation();
             openedlunchTimeDelete();
           }} className="cursor-pointer">
  <Icon path={mdiDeleteClockOutline} size={1} color="#ffffff" className="hover:fill-[#ED0423] transition-colors duration-200" />
</span>
             {/* <Icon path={mdiDeleteClockOutline} size={1} className="hover:fill-[#ED0423] " onClick={() => openedlunchTimeDelete}    /> */}
            <Modal
                opened={lunchTimeDeleteOpened}
                onClose={closelunchTimeDelete}
                withCloseButton={false}
              >
                <Alert variant="light" color="red" title="Avertissement" icon={icon}>
                  Êtes-vous sûr de vouloir supprimer l&apos;heure du déjeuner ?
                </Alert>
                <Group justify="space-between" mt="md" mb="xs">
                  <Button
                    variant="filled"
                    size="xs"
                    radius="lg"
                    color="red"
  
                     onClick={() => {
                    handleRemoveLunchEvent(event);
                    closelunchTimeDelete(); // Close the modal after deletion
                  }}
                  >
                    Oui
                  </Button>
                  <Button
                    variant="filled"
                    size="xs"
                    radius="lg"
                    color="lime.4"
                    onClick={closelunchTimeDelete}
                  >
                    Non
                  </Button>
                </Group>
              </Modal>
             </Group>
             </div>
            :
      <>
     <div  
  className="absolute left-1 right-1  text-xs rounded cursor-pointer hover:opacity-90 transition-all z-10 group"
      style={{
        backgroundColor: event.currentEventColor || event.color || getEventTypeColor(event.eventType as EventType),
        color: 'white',
        top: '2px',
        bottom: '2px'
      }}
  > 
     {/* <div className="event-content" style={{ backgroundColor: event.currentEventColor || event.color || getEventTypeColor(event.eventType as EventType) }} > */}
     <Group justify="space-between" h={"26px"} >
      <Menu  shadow="md" width={429} position="bottom-start" transitionProps={{ transition: 'rotate-right', duration: 150 }} withArrow arrowPosition="center">
      <Menu.Target >
        <div className="font-bold flex" onClick={(e) => e.stopPropagation()}>
          <span className=" ml-2 mb-2">
        <Icon path={mdiListBoxOutline} size={1}/>
        </span>
      
  
         <Text tt="capitalize" ml={6} pt={1}>{event.last_name} {event.first_name}</Text>
        
        <Group gap="xs" mb={4} className={isSidebarVisible ?  "ml-2 ": "ml-20"} >
          <IconClock2 size={16} />
        {event.consultationDuration} min 
        </Group>
        </div>
      </Menu.Target>
      <Menu.Dropdown onClick={(e) => e.stopPropagation()}
        style={{ backgroundColor: event.color }}
        className="left-[156px]"
        >
        <Card shadow="sm" padding="lg" radius="md" withBorder>
        <Menu.Divider />
        <List size="sm" withPadding className="capitalize" type="ordered">
        <List.Item
        icon={
          <>
          <Flex  mih={10} gap="md" justify="flex-start"  align="flex-start"  direction="row"  wrap="wrap" >
            <Icon path={mdiAccountTie} size={1} color={"#3799CE"} />
            <Icon path={mdiArrowRightThin} size={1} color={event.color} />
            </Flex>
          </>
        }
  
      >
       {event.last_name} {event.first_name}
          </List.Item>
  
          <List.Item
        icon={
          <>
          <Flex  mih={10} gap="md" justify="flex-start"  align="flex-start"  direction="row"  wrap="wrap" >
            <Icon path={mdiCalendarMonth} size={1} color={"#3799CE"} />
            <Icon path={mdiArrowRightThin} size={1}  color={event.color} />
            </Flex>
          </>
        }
      >
  
        {moment(event.start).format("DD/MM/YYYY")}
          </List.Item>
      <List.Item
      icon={
      <>
      <Flex  mih={10} gap="md" justify="flex-start"  align="flex-start"  direction="row"  wrap="wrap" >
        <Icon path={mdiUpdate} size={1} color={"#3799CE"} />
        <Icon path={mdiArrowRightThin} size={1} color={event.color} />
        </Flex>
      </>
      }
      >
        {moment(event.start).format("HH:mm")}- {moment(event.end).format("HH:mm")}
          </List.Item>
  
          <List.Item
        icon={
        <>
        <Flex  mih={10} gap="md" justify="flex-start"  align="flex-start"  direction="row"  wrap="wrap" >
          <svg stroke="currentColor" fill={"#3799CE"} strokeWidth="0" viewBox="0 0 448 512" height="18px" width="18px" xmlns="http://www.w3.org/2000/svg"><path d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-96 55.2C54 332.9 0 401.3 0 482.3C0 498.7 13.3 512 29.7 512l388.6 0c16.4 0 29.7-13.3 29.7-29.7c0-81-54-149.4-128-171.1l0 50.8c27.6 7.1 48 32.2 48 62l0 40c0 8.8-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16s7.2-16 16-16l0-24c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 24c8.8 0 16 7.2 16 16s-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16l0-40c0-29.8 20.4-54.9 48-62l0-57.1c-6-.6-12.1-.9-18.3-.9l-91.4 0c-6.2 0-12.3 .3-18.3 .9l0 65.4c23.1 6.9 40 28.3 40 53.7c0 30.9-25.1 56-56 56s-56-25.1-56-56c0-25.4 16.9-46.8 40-53.7l0-59.1zM144 448a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"></path></svg>
          <Icon path={mdiArrowRightThin} size={1} color={event.color} />
          </Flex>
        </>
      }
        >
        {resolveDoctorName(event.docteur || event.doctor_name)}
          </List.Item>
          <List.Item
        icon={
          <>
          <Flex  mih={10} gap="md" justify="flex-start"  align="flex-start"  direction="row"  wrap="wrap" >
            <svg stroke="currentColor" fill="#3799CE" strokeWidth="0" viewBox="0 0 16 16" height="18px" width="18px" xmlns="http://www.w3.org/2000/svg"><path d="M14.5 3a.5.5 0 0 1 .5.5v9a.5.5 0 0 1-.5.5h-13a.5.5 0 0 1-.5-.5v-9a.5.5 0 0 1 .5-.5zm-13-1A1.5 1.5 0 0 0 0 3.5v9A1.5 1.5 0 0 0 1.5 14h13a1.5 1.5 0 0 0 1.5-1.5v-9A1.5 1.5 0 0 0 14.5 2z"></path><path d="M7 5.5a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5m-1.496-.854a.5.5 0 0 1 0 .708l-1.5 1.5a.5.5 0 0 1-.708 0l-.5-.5a.5.5 0 1 1 .708-.708l.146.147 1.146-1.147a.5.5 0 0 1 .708 0M7 9.5a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5m-1.496-.854a.5.5 0 0 1 0 .708l-1.5 1.5a.5.5 0 0 1-.708 0l-.5-.5a.5.5 0 0 1 .708-.708l.146.147 1.146-1.147a.5.5 0 0 1 .708 0"></path></svg> 
            <Icon path={mdiArrowRightThin} size={1} color={event.color} />
            </Flex>
          </>
        }
      >
        {event.typeConsultation}
          </List.Item>
      </List>
      <Menu.Divider />
  
      </Card>
        </Menu.Dropdown>
      </Menu>
      <Menu shadow="md" width={200} position="bottom-end">
      <Menu.Target>
                    <span
                      className={`event.color hover:${event.color} rounded-full p-1 ml-auto`}
                      onClick={(e) => e.stopPropagation()}
                    >
                       <Group>
                       <Indicator
                        processing
                        inline
                        size={10}
                        offset={0}
                        position="bottom-end"
                        color="red"
                        withBorder
                        className={event.lunchTime ? "hidden" : ""}
                        disabled={!activeEvent_Ids.has(Number(event.id)) || activeIds.includes(Number(event.id))}
                        //  activeEventIds.has(Number(event.id))
                        mr={isSidebarVisible ?  40: 65}
                        
                      />
                        <Icon path={mdiDotsVertical} size={1}  className="-mt-1" />
                      </Group>
                    </span>
                  </Menu.Target>
                  <Menu.Dropdown onClick={(e) => e.stopPropagation()}>
                  <Menu.Item leftSection={<MenuIcons.sltVisit size={16} />} >
                  <Group justify="space-between"  className="flex justify-between items-center w-full pt-.5 pl-1">
                 <span> SLT</span>
              <Button component="div" size="compact-xs" color="#15AABF" >
                {activeVisits.filter(visit => visit.resourceId === event.resourceId).length}/30
              </Button>
  
                  </Group>
              </Menu.Item>
                  <Menu.Divider />
               <Menu.Item
              leftSection={<MenuIcons.activeVisit size={16} />}
              onClick={() => {
                console.log('🖱️ Menu.Item clicked for appointment:', event.first_name, event.last_name, 'Current isActive:', event.isActive);
                // Always use activateAppointment function for both activation and deactivation
                activateAppointment(event);
              }}
  >
    <Text c={event.isActive ? 'red' : 'green'}>
      {event.isActive ? 'Désactiver la visite' : 'Activer la visite'}
    </Text>
  </Menu.Item>
  
      <Menu.Item leftSection={<MenuIcons.lastVisit size={16} />} onClick={() => handleLastVisit(event)}>
        Dernière visite
      </Menu.Item>
       <Menu.Item
        leftSection={<MenuIcons.lastVisit size={16} />}
        onClick={async (clickEvent) => {
          clickEvent.stopPropagation();

          console.log('🔄 Moving to waiting room visits:', event.title);

          try {
            // Import API dynamically
            const { appointmentStateAPI } = await import('@/services/api');

            // Update backend: mark appointment as in presentation room (waiting room visits)
            await appointmentStateAPI.togglePresentationRoom(event.id, true);
            console.log('✅ Backend updated: appointment moved to waiting room visits');

            // Move to waiting room visits locally
            moveToWaitingRoom(event);

            notifications.show({
              title: 'Déplacé vers la salle d\'attente',
              message: `${event.title} a été déplacé vers les visites en salle d'attente`,
              color: 'blue',
              autoClose: 3000
            });
          } catch (error) {
            console.error('Error moving to waiting room:', error);
            notifications.show({
              title: 'Erreur',
              message: error instanceof Error ? error.message : 'Impossible de déplacer vers la salle d\'attente',
              color: 'red',
              autoClose: 5000
            });
          }
        }}
      >
        Dans la salle d&apos;attente
      </Menu.Item>
      <Menu.Divider />
        <Menu.Item
      leftSection={<MenuIcons.editAppointment size={16} />}
      onClick={(clickEvent) => {
              clickEvent.stopPropagation();
              if (onEdit) {
                onEdit(event);
              }
            }}
      >
      Modifier RDV
      </Menu.Item>
      <Menu.Item leftSection={<MenuIcons.nextAppointment size={16} />} 
      onClick={() => rescheduleAppointment(event)} 
      >
        Prochain RDV
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item
      leftSection={<Icon path={mdiDeleteClockOutline} size={1} />}
       onClick={(clickEvent) => {
              clickEvent.stopPropagation();
              clickEvent.preventDefault();
              // Open confirmation modal instead of directly deleting
              setEventToDelete(event);
              setIsDeleteConfirmModalOpen(true);
             
            }}
      >
        Supprimer RDV
      </Menu.Item>
      <Menu.Item
        leftSection={<MenuIcons.patientFile size={16} />}
        onClick={(e) => {
          e.stopPropagation();
          // Use direct navigation for faster redirection instead of Next.js Link
          const patientId = event.patient || event.patient_id || event.id || 'no-patient-id';
          window.location.href = `/patient/patient-form/${patientId}/`;
        }}
      >
        Fiche patient
      </Menu.Item>
      <Menu.Item
        leftSection={<MenuIcons.patientDetails size={16} />}
       onClick={(clickEvent) => {
              clickEvent.stopPropagation();
              // Convert CalendarEvent to WaitingListPatient format
              const patientData: WaitingListPatient = {
                id: event.id,
                first_name: event.first_name || '',
                last_name: event.last_name || '',
                typeConsultation: event.typeConsultation,
                eventType: event.eventType,
                phone_number: event.phone_number || event.phone_numbers,
                start: event.start ? event.start.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' }) : '',
                duration: event.duration,
                color: event.color,
                docteur: event.docteur,
                date: event.start?.toISOString().split('T')[0],
                title: event.title,
                end: event.end ? event.end.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' }) : '',
                desc: event.desc,
                address: event.address,
                notes: event.notes
              };
              openPatientDetails(patientData);
            }}>
      Détails patient
    </Menu.Item>
    <Menu.Divider />
      <Menu.Divider />
      <Menu.Item leftSection={<MenuIcons.addAlert size={16} />}
    //   onClick={() => {
    //     handleAddAlert(appointment);
    //      setAlertsOpened(true);
    //   }
    // }
    onClick={() => setIsAlertsModalOpen(true)}
      >
        Ajouter une alerte
      </Menu.Item>
      <Menu.Item leftSection={<MenuIcons.addReminder size={16} />} onClick={() => handleAddReminder(event)}>
        Ajouter un rappel
      </Menu.Item>
      <Menu.Divider />
      
 <Menu.Item
  leftSection={event.resourceId === "1" ? <Icon path={mdiArrowRightThin} size={1} /> : <Icon path={mdiArrowLeftThin} size={1} />}
  onClick={async (clickEvent) => {
    clickEvent.stopPropagation();

    // Determine target room
    const currentResourceId = event.resourceId?.toString();
    const targetRoomId = currentResourceId === "1" ? "room-b" : "room-a";
    const targetRoomName = currentResourceId === "1" ? "B" : "A";

    console.log('🔄 Moving appointment between rooms:', {
      appointmentId: event.id,
      currentRoom: currentResourceId === "1" ? "A" : "B",
      targetRoom: targetRoomName,
      targetRoomId
    });

    try {
      // Import API dynamically
      const { appointmentAPI } = await import('@/services/api');

      // Prepare update data for room movement
      const updateData = {
        resource_id: targetRoomId,
        room: `Room ${targetRoomName}`,
        // Preserve existing appointment data
        title: event.title,
        appointment_date: event.start.toISOString().split('T')[0],
        appointment_time: event.start.toTimeString().slice(0, 5),
        appointment_type: event.type || 'consultation',
        status: event.status || 'scheduled',
        duration_minutes: event.duration || event.consultationDuration || 30,
      };

      // Update appointment in backend
      const updatedAppointment = await appointmentAPI.update(event.id, updateData);
      console.log('✅ Appointment room updated successfully:', updatedAppointment);

      // Show success notification
      notifications.show({
        title: 'Succès',
        message: `Le rendez-vous a été déplacé vers la salle ${targetRoomName}`,
        color: 'green',
      });

      // Update the event in the calendar
      if (onEdit) {
        onEdit({
          ...event,
          resourceId: targetRoomId,
          room: `Room ${targetRoomName}`,
        });
      }
    } catch (error) {
      console.error('Error moving appointment between rooms:', error);
      notifications.show({
        title: 'Erreur',
        message: error instanceof Error ? error.message : 'Impossible de déplacer le rendez-vous. Veuillez réessayer.',
        color: 'red',
      });
    }
  }}
>
     {event.resourceId === "1" ? 'Déplacer vers B' : 'Déplacer vers A'}
            </Menu.Item>
      <Menu.Divider />
      <Menu.Item
      leftSection={<Icon path={mdiDeleteClockOutline} size={1} />}
       onClick={(clickEvent) => {
              clickEvent.stopPropagation();
              // Open confirmation modal instead of directly deleting
              setEventToDelete(event);
              setIsDeleteConfirmModalOpen(true);
             
            }}
      >
        Supprimer RDV
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item  color="red" leftSection={<MenuIcons.cancel size={16} color="red" />}>
        Annuler
      </Menu.Item>
          </Menu.Dropdown>
      </Menu>
      </Group>
      </div>
      </>
      }
          {/* menu Alerts */}
                                    <Modal.Root
                                       opened={isAlertsModalOpen}
                                       onClose={() => setIsAlertsModalOpen(false)}
                                       transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
                                       centered
                                       size="xl"
                                     >

                                    <Modal.Content className="overflow-y-hidden">
                                     <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
                                       <Modal.Title>
                                         <Group>
                                           <Icon path={mdiHistory} size={1} className="mantine-focus-always" style={{ color: "white" }}/>
                                             <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">Alerts - ABDESSALMAD AGADIR</Text>
                                         </Group>
                                       </Modal.Title>
                                         <Group justify="flex-end">
                                           <ActionIcon variant="filled" aria-label="Plus" color="#3799CE"
                          onClick={(event) => {
                        event.preventDefault();
                        event.stopPropagation(); // Prevent event bubbling
                        console.log('Plus button clicked - toggling sidebar');
                        onToggleSidebar?.(); // Use the passed function from main component
                      }}
                                          >
                                    <Icon path={mdiPlus} size={1} className="mantine-focus-always" style={{ color: "white" }}/>
                                           </ActionIcon>
                                                   <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }}
                                                    onClick={handleCloseSidebar}
                                                   />
                                                 </Group>
                                     </Modal.Header>
                                       <Modal.Body style={{ padding: '0px' }}>
                                        <div className={rows.length <=3 ?"py-2 pl-4 h-auto overflow-hidden" : "py-2 pl-4 h-[200px]  overflow-hidden"}>

                                                 <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
                                                   <div className="pr-4">
                                                    <Table striped highlightOnHover withTableBorder withColumnBorders>
                                                      <Table.Thead>
                                   <Table.Tr>
                                     <Table.Th>Déclencheur</Table.Th>
                                     <Table.Th>Niveau</Table.Th>
                                     <Table.Th>Publique</Table.Th>
                                     <Table.Th>Permanente</Table.Th>
                                      <Table.Th>Description</Table.Th>
                                     <Table.Th></Table.Th>
                                   </Table.Tr>
                                 </Table.Thead>
                                 <Table.Tbody>{rows}</Table.Tbody>
                                 {rows.length === 0 && (
                                   <Table.Caption>Aucun élément trouvé.</Table.Caption>
                                 )}
                                                     </Table>
                                                   </div>
                                                 </SimpleBar>
                                               </div>
                                             </Modal.Body>
                                    </Modal.Content>
                                     </Modal.Root>
                                      {/* add Alerts */}
                          <Modal.Root
                           opened={isAlertsAddModalOpen}
                           onClose={() => {setIsAlertsAddModalOpen(false); setIsSidebarVisible(false)}}
                           transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
                           centered
                           size="xl"
                         >

                        <Modal.Content className="overflow-y-hidden">
                         <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
                           <Modal.Title>
                             <Group>
                               <Icon path={mdiAccountAlert} size={1} className="mantine-focus-always" style={{ color: "white" }}/>
                                 <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
                                    {currentEditingAlertId ? `Modifier Alerte - ${event.first_name}` : `Ajouter Alerte - ${event.first_name}`}
                                   </Text>
                             </Group>
                           </Modal.Title>
                             <Group justify="flex-end">
                                       <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }} />
                                     </Group>
                         </Modal.Header>
                           <Modal.Body style={{ padding: '0px' }}>

                            <div className={rows.length <=3 ?"py-2 pl-4 h-auto overflow-hidden" : "py-2 pl-4 h-[300px]  overflow-hidden"}>

                                     <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
                                       <div className="pr-4">
                                      <form
                                             onSubmit={form.onSubmit((values) => handleAlertSubmit(values, false))}
                                             style={{ display: 'flex', flexDirection: 'column', gap: 16 }}
                                           >
                                             <Group>
                                             <MultiSelect
                                               label="Déclencher pour"
                                               data={realStaffOptions}
                                               {...form.getInputProps('trigger_for')}
                                               required
                                               w={"30%"}
                                               disabled={isLoadingStaff}
                                               placeholder={isLoadingStaff ? "Chargement des médecins..." : "Sélectionner les médecins"}
                                             />
                                             <Select
                                               label="Déclencheur"
                                               data={triggerOptions}
                                               {...form.getInputProps('trigger')}
                                               required
                                                w={"30%"}
                                             />
                                             <Radio.Group label="Niveau" {...form.getInputProps('level')}>
                                               <Group>
                                                 <Radio value="MINIMUM" label="Minimum" />
                                                 <Radio value="MEDIUM" label="Moyen" />
                                                 <Radio value="HIGH" label="Haut" />
                                               </Group>
                                             </Radio.Group>
                                             </Group>
                                             <Group justify="space-between">
                                               <Text>Description *</Text>
                                               <Group>
                                                <ActionIcon variant="filled" aria-label="Microphone" color="#3799CE"
                                             onClick={
                                               ()=>setIsMicrophoneModalOpen(true)
                                             }>
                                             <Icon path={mdiMicrophone} size={1} />
                                                     </ActionIcon>
                                              <ActionIcon variant="filled" aria-label="Microphone" color="#3799CE"
                                             onClick={
                                               ()=>{
                                                 console.log('Dictionary button clicked, sidebar visible:', isSidebarAlert);
                                                 setIsClipboardTextModalOpen(true);
                                                    setShowModels(true);
                                                    setShowAddModel(false);
                                                    handleCloseSidebar()

                                               }
                                             }>
                                             <Icon path={mdiClipboardText} size={1} />
                                                     </ActionIcon>
                                              <ActionIcon
                                                variant="filled"
                                                aria-label="Clear Description"
                                                color="red"
                                                onClick={() => {
                                                  console.log('Clear button clicked, clearing description field');
                                                  form.setFieldValue('description', '');
                                                  console.log('Description field cleared');
                                                }}
                                              >
                                                <Icon path={mdiDeleteSweep} size={1} />
                                              </ActionIcon>
                                               </Group>
                                             </Group>
                                             <Textarea
                                               // label="Description"
                                               placeholder="Ajouter"
                                               {...form.getInputProps('description')}
                                               required
                                             />

                                             <Switch
                                               label="Permanente"
                                               {...form.getInputProps('is_permanent', { type: 'checkbox' })}
                                             />

                                             <Group justify="flex-end" mt="md">
                                               <Button color="gray" onClick={() => {setIsAlertsAddModalOpen(false)}}>
                                                 Annuler
                                               </Button>
                                               <Button
                                                 onClick={() => {
                                                   if (form.isValid()) {
                                                     handleAlertSubmit(form.values, true); // submit with autoTrigger = true
                                                   }
                                                 }}
                                                 disabled={!form.isValid()}
                                               >
                                                 Enregistrer et déclencher
                                               </Button>
                                               <Button type="submit" disabled={!form.isValid()}>
                                                 Enregistrer
                                               </Button>
                                             </Group>
                                           </form>
                                       </div>
                                     </SimpleBar>
                                   </div>
                                 </Modal.Body>
                        </Modal.Content>
                         </Modal.Root>
                           {/* Choix multiple */}
                          <Modal.Root
                           opened={isChoixMultipleModalOpen}
                           onClose={() => {setIsChoixMultipleModalOpen(false); setIsSidebarVisible(false)}}
                           transitionProps={{ transition: 'fade', duration: 600, timingFunction: 'linear' }}
                           centered
                           size="xl"
                         >
                           <Modal.Overlay />
                        <Modal.Content className="overflow-y-hidden">
                         <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
                           <Modal.Title>
                             <Group>
                               <Icon path={mdiPlaylistCheck} size={1} className="mantine-focus-always" style={{ color: "white" }}/>
                                 <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
                                    Choix multiple
                                   </Text>
                             </Group>
                           </Modal.Title>
                             <Group justify="flex-end">
                                       <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }} />
                                     </Group>
                         </Modal.Header>
                           <Modal.Body style={{ padding: '0px' }}>
                                   <div className="py-2 pl-4 h-[300px]">
                                     <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
                                       <div className="pr-4">

                      <Stack>
                       {/* Boutons de contrôle */}
                       <Group justify="space-between" mb="sm">
                         <Button
                           size="xs"
                           variant="light"
                           onClick={() => {
                             // Sélectionner tous les nœuds
                             const getAllNodeIds = (nodes: TreeNodeChoixMultiple[]): string[] => {
                               const ids: string[] = [];
                               nodes.forEach(node => {
                                 ids.push(node.uid);
                                 if (node.nodes) {
                                   ids.push(...getAllNodeIds(node.nodes));
                                 }
                               });
                               return ids;
                             };
                             setSelectedNodes(new Set(getAllNodeIds(exampleData)));
                           }}
                         >
                           Tout sélectionner
                         </Button>
                         <Button
                           size="xs"
                           variant="light"
                           color="red"
                           onClick={() => setSelectedNodes(new Set())}
                         >
                           Tout désélectionner
                         </Button>
                       </Group>

                       {exampleData.map((node) => (
                         <TreeItemChoixMultiple
                           key={node.uid}
                           node={node}
                           collapsedNodes={collapsedNodes}
                           toggleNodeCollapse={toggleNodeCollapse}
                           selectedNodes={selectedNodes}
                           toggleNodeSelection={toggleNodeSelection}
                         />
                       ))}
                     </Stack>

                     <Group justify="space-between" mt="md">
                       <Group>
                         <Text size="sm" c="dimmed">
                           {selectedNodes.size} élément{selectedNodes.size !== 1 ? 's' : ''} sélectionné{selectedNodes.size !== 1 ? 's' : ''}
                         </Text>

                       </Group>
                       <Group>
                         {selectedNodes.size > 0 && (
                           <Button
                             variant="filled"
                             color="blue"
                             onClick={() => {
                               // Sauvegarder automatiquement comme nouveau modèle
                               const selectedValues = getSelectedValues();
                               const timestamp = new Date().toLocaleString('fr-FR', {
                                 day: '2-digit',
                                 month: '2-digit',
                                 year: 'numeric',
                                 hour: '2-digit',
                                 minute: '2-digit'
                               });
                               const autoTitle = `Modèle ${timestamp}`;

                               const newModel = {
                                 id: `model-${Date.now()}`,
                                 title: autoTitle,
                                 selections: selectedValues
                               };

                               setSavedModels(prev => [...prev, newModel]);
                               console.log('Model saved:', newModel);

                               // Passer à la vue des modèles
                               setShowModels(true);
                               setSelectedNodes(new Set());
                             }}
                           >
                             Ajouter model
                           </Button>
                         )}
                         <Button onClick={handleValidate} disabled={selectedNodes.size === 0}>
                           Valider ({selectedNodes.size})
                         </Button>
                         <Button variant="outline" color="red" onClick={handleCancel}>
                           Annuler
                         </Button>
                       </Group>
                     </Group>

                                       </div>
                                     </SimpleBar>
                                   </div>
                                 </Modal.Body>
                        </Modal.Content>
                         </Modal.Root>

                         {/* Modal de confirmation de suppression */}
                         <Modal.Root
                           opened={isDeleteConfirmModalOpen}
                           onClose={handleCloseDeleteConfirmModal}
                           centered
                           size="sm"
                         >
                           <Modal.Content>
                             <Modal.Header>
                               <Modal.Title>Confirmation de suppression</Modal.Title>
                               <Modal.CloseButton />
                             </Modal.Header>
                             <Modal.Body>
                               <Text size="md" mb="md">
                                 Êtes-vous sûr de vouloir supprimer cet événement ??
                               </Text>
                               <Group justify="flex-end" gap="sm">
                                 <Button
                                   variant="outline"
                                   color="blue"
                                   onClick={confirmDeleteAppointment}
                                 >
                                   Oui
                                 </Button>
                                 <Button
                                   variant="filled"
                                   color="red"
                                   onClick={cancelDeleteAppointment}
                                 >
                                   Non
                                 </Button>
                               </Group>
                             </Modal.Body>
                           </Modal.Content>
                         </Modal.Root>
                          {/* Modal Microphone - Reconnaissance vocale */}
                                          <Modal
                                            opened={isMicrophoneModalOpen}
                                            onClose={() => setIsMicrophoneModalOpen(false)}
                                            title="Reconnaissance vocale"
                                            size="lg"
                                            radius={0}
                                            transitionProps={{ transition: 'fade', duration: 200 }}
                                            centered
                                            withCloseButton={false}
                                            yOffset="30vh" xOffset={0}

                                          >
                                            <div style={{ padding: '20px' }}>
                                              {/* Interface de reconnaissance vocale */}
                                              <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'flex-start', marginBottom: '20px' }}>
                                                <div style={{ flex: 1, marginRight: '16px' }}>
                                                  <div style={{
                                                    border: '1px solid #e0e0e0',
                                                    borderRadius: '4px',
                                                    padding: '12px',
                                                    minHeight: '80px',
                                                    backgroundColor: '#fafafa',
                                                   height:'150px'
                                                  }}>
                                                    {/* Texte valide reconnu */}
                                                    <span
                                                      style={{
                                                        color: '#2e7d32',
                                                        fontWeight: 500,
                                                        display: validSpeech ? 'inline' : 'none'
                                                      }}
                                                      contentEditable
                                                    >
                                                      {validSpeech}
                                                    </span>
                                                    {/* Texte en cours de reconnaissance */}
                                                    <span
                                                      style={{
                                                        color: '#757575',
                                                        fontStyle: 'italic'
                                                      }}
                                                    >
                                                      {invalidSpeech}
                                                    </span>
                                                  </div>
                                                </div>

                                                {/* Boutons de contrôle */}
                                                <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                                                  <ActionIcon
                                                    variant="subtle"
                                                    color={isListening ? 'orange' : 'blue'}
                                                    size="lg"
                                                    onClick={toggleRecognition}
                                                    style={{ backgroundColor: isListening ? '#ffecb3' : undefined }}
                                                  >
                                                    <Icon path={mdiMicrophone} size={1} color={microphoneColor} />
                                                  </ActionIcon>

                                                  <ActionIcon
                                                    variant="subtle"
                                                    color="red"
                                                    size="lg"
                                                    onClick={emptyContent}
                                                  >
                                                    <Icon path={mdiDeleteSweep} size={1} />
                                                  </ActionIcon>
                                                </div>
                                              </div>

                                              {/* Boutons d'action */}
                                              <Group justify="flex-end" mt="md">
                                                <Button
                                                  variant="filled"
                                                  onClick={() => {
                                                    // Ici vous pouvez traiter le texte reconnu
                                                    console.log('Texte reconnu:', validSpeech);
                                                    setIsMicrophoneModalOpen(false);
                                                  }}
                                                >
                                                  Valider
                                                </Button>
                                                <Button
                                                  variant="outline"
                                                  color="red"
                                                  onClick={() => setIsMicrophoneModalOpen(false)}
                                                >
                                                  Annuler
                                                </Button>
                                              </Group>
                                            </div>
                                          </Modal>

                                          {/* Gestionnaire des modaux de dictionnaire */}
                                          <DictionaryModalsManager
                                            // États des modaux
                                            isAddModelModalOpen={isClipboardTextModalOpen && showAddModel}
                                            isSavedModelsModalOpen={isClipboardTextModalOpen && showModels}
                                            isDictionaryTreeModalOpen={isChoixMultipleModalOpen}

                                            // Données
                                            modelTitle={modelTitle}
                                            savedModels={savedModels}
                                            exampleData={exampleData}
                                            selectedNodes={selectedNodes}
                                            collapsedNodes={collapsedNodes}
                                            editingModelId={editingModelId}

                                            // Fonctions de gestion des états
                                            setModelTitle={setModelTitle}
                                            setIsAddModelModalOpen={setShowAddModel}
                                            setIsSavedModelsModalOpen={setShowModels}
                                            setIsDictionaryTreeModalOpen={setIsChoixMultipleModalOpen}

                                            // Fonctions de gestion des modèles
                                            onSaveModel={handleSaveModel}
                                            onToggleModel={(modelId) => {
                                              console.log('Toggling model:', modelId);
                                              setSavedModels(prev => {
                                                const updated = prev.map(model =>
                                                  model.id === modelId
                                                    ? { ...model, selected: !model.selected }
                                                    : model
                                                );
                                                console.log('Updated savedModels:', updated);
                                                return updated;
                                              });
                                            }}
                                            onDeleteModel={handleDeleteModel}
                                            onEditModel={handleEditModel}

                                            // Fonctions de gestion de l'arbre
                                            onToggleNodeCollapse={toggleNodeCollapse}
                                            onToggleNodeSelection={toggleNodeSelection}
                                            onSelectAll={selectAllNodes}
                                            onDeselectAll={deselectAllNodes}

                                            // Fonctions d'action
                                            onValidate={handleValidate}
                                            onCancel={handleCancel}
                                            onCloseSidebar={() => {
                                              console.log('Closing sidebar from SavedModelsModal');
                                              setIsSidebarVisible(false);
                                            }}
                                            getSelectedValues={getSelectedValues}

                                            // Composants
                                            TreeItemChoixMultiple={TreeItemChoixMultiple}
                                          />

                                          {/* Modal ClipboardText - Redirection automatique vers les modaux séparés */}
                                          {isClipboardTextModalOpen && !showModels && !showAddModel && (
                                            <div style={{ display: 'none' }}>
                                              {/* Ce modal est maintenant géré par DictionaryModalsManager */}
                                            </div>
                                          )}
                                             {/* Edit Patient Modal */}
                                                 <Modal
                                                   opened={rescheduleModalOpen}
                                                   onClose={() => {
                                                     setRescheduleModalOpen(false);
                                                     setAppointmentToReschedule(null);
                                                   }}
                                                   title="Reschedule Appointment"
                                                 >
                                                   {appointmentToReschedule && (
                                                     <form onSubmit={(e) => {
                                                       e.preventDefault();
                                                       const newDateTime = new Date(eventForm.values.rescheduleDateTime);
                                                       const duration = appointmentToReschedule.end.getTime() - appointmentToReschedule.start.getTime();
                                          
                                                       const updatedAppointment = {
                                                         ...appointmentToReschedule,
                                                         start: newDateTime,
                                                         end: new Date(newDateTime.getTime() + duration),
                                                       };
                                          
                                                       setAppointments(prevAppointments =>
                                                         prevAppointments.map(app =>
                                                           app.id === appointmentToReschedule.id ? updatedAppointment : app
                                                         )
                                                       );
                                          
                                                       notifications.show({
                                                         title: 'Appointment Rescheduled',
                                                         message: `${updatedAppointment.title}'s appointment has been rescheduled`,
                                                         color: 'blue',
                                                       });
                                          
                                                       setRescheduleModalOpen(false);
                                                       setAppointmentToReschedule(null);
                                                       eventForm.reset();
                                                     }}>
                                                       <TextInput
                                                         label="Current Date/Time"
                                                         value={moment(appointmentToReschedule.start).format('MMMM D, YYYY h:mm A')}
                                                         disabled
                                                       />
                                          
                                                       <input
                                                         type="datetime-local"
                                                         {...eventForm.getInputProps('rescheduleDateTime')}
                                                         style={{
                                                           width: '100%',
                                                           padding: '10px',
                                                           marginTop: '10px',
                                                           marginBottom: '20px',
                                                           borderRadius: '4px',
                                                           border: '1px solid #ced4da',
                                                         }}
                                                         required
                                                       />
                                          
                                                       <Group justify="flex-end" mt="md">
                                                         <Button type="submit">
                                                           Reschedule
                                                         </Button>
                                                       </Group>
                                                     </form>
                                                   )}
                                                 </Modal>
                                                   
      </>
   
  );
};
  // Duplicate function removed - using the one defined earlier
  const isToday = isSameDay(currentDate, new Date());
  // Force re-render every minute to update time indicator
  const [, forceUpdate] = useState({});
  useEffect(() => {
    if (isToday) {
      const interval = setInterval(() => {
        forceUpdate({});
      }, 60000); // Update every minute

      return () => clearInterval(interval);
    }
  }, [isToday]);

  // Default delete function if none provided
  const handleEventDelete = useCallback((event: CalendarEvent) => {
    if (onEventDelete) {
      onEventDelete(event);
    } else {
      // Remove event from local state
      setLocalEvents(prevEvents => prevEvents.filter(e => e.id !== event.id));

      notifications.show({
        title: 'Événement supprimé',
        message: `L'événement "${event.title}" a été supprimé`,
        color: 'green',
      });
    }
  }, [onEventDelete]);

  // Handle date change for SelectJournee
  const handleDateChange = useCallback((newDate: Date) => {
    onDateChange?.(newDate);
  }, [onDateChange]);

  // Handle navigation
  const handleNavigate = useCallback((direction: 'prev' | 'next' | 'today') => {
    onNavigate?.(direction);
  }, [onNavigate]);

  // Format date title in French format: "12 août 2025"
  const getCurrentDateTitle = () => {
    return currentDate.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  // Label for SelectJournee
  const label = getCurrentDateTitle();
// tab content Visites Actives


      const [rootRef, setRootRef] = useState<HTMLDivElement | null>(null);
  const [value, setValue] = useState<string | null>('1');
  const [controlsRefs, setControlsRefs] = useState<Record<string, HTMLButtonElement | null>>({});
  const setControlRef = (val: string) => (node: HTMLButtonElement) => {
    controlsRefs[val] = node;
    setControlsRefs(controlsRefs);
  };
 const [isSidebarVisible, setIsSidebarVisible] = useState(false); // State to control sidebar visibility
 const toggleSidebar = () => {
      setIsSidebarVisible(!isSidebarVisible);
    };

    const [StartofworkOpened, { open: openedStartofwork, close: closeStartofwork }] = useDisclosure(false)
    const [minHour, setMinHour] = useState<number>(8); // Default to 8 AM
       const [minMinute, setMinMinute] = useState<number>(0); // Default to 0 minutes
       const [maxHour, setMaxHour] = useState<number>(18); // Default to 6 PM
       const [maxMinute, setMaxMinute] = useState<number>(0); // Default to 0 minutes
       const [step, setStep] = useState<number>(15); // Default
       const [, setMinTime] = useState(() => {
         const date = new Date();
         date.setHours(8, 0, 0); // Default to 8:00 AM
         return date;
       });
       const [, setMaxTime] = useState(() => {
         const date = new Date();
         date.setHours(18, 0, 0); // Default to 6:00 PM
         return date;
       });
       const handleSave = () => {
         const newMinTime = moment()
           .startOf("day")
           .set({ hour: minHour, minute: minMinute })
           .local()
           .toDate();
         const newMaxTime = moment()
           .startOf("day")
           .set({ hour: maxHour, minute: maxMinute })
           .local()
           .toDate();
         setMinTime(newMinTime);
         setMaxTime(newMaxTime);

         console.log('Work hours updated:', {
           start: `${minHour}:${minMinute.toString().padStart(2, '0')}`,
           end: `${maxHour}:${maxMinute.toString().padStart(2, '0')}`,
           step: step
         });

         // Close the modal after saving
         closeStartofwork();
       };

       // Generate time slots based on work hours with configurable intervals
       const timeSlots = useMemo((): TimeSlotData[] => {
         const slots: TimeSlotData[] = [];
         const startHour = minHour;
         const startMinute = minMinute;
         const endHour = maxHour;
         const endMinute = maxMinute;

         for (let hour = startHour; hour <= endHour; hour++) {
           const startMin = (hour === startHour) ? startMinute : 0;
           const endMin = (hour === endHour) ? endMinute : 60;

           for (let minute = startMin; minute < endMin; minute += step) {
             // Don't add slots beyond the end time
             if (hour === endHour && minute >= endMinute) break;
             slots.push({ hour, minute });
           }
         }
         return slots;
       }, [minHour, minMinute, maxHour, maxMinute, step]);
  const [dateSelected, ] = useState<Date>(new Date());
       const [opened, { open, close }] = useDisclosure(false)
        const [selectedHour, setSelectedHour] = useState<number | null>(null);
        const hours = [11, 12, 13, 14, 15, 16];
        const [selectedMinute, setSelectedMinute] = useState<number | null>(null);
        const [duration, setDuration] = useState<number>(60); // Durée par défaut: 60 minutes
              // Générer les options de minutes (0, 15, 30, 45)
    const minutes = [0, 15, 30, 45];
     // Options de durée (15min à 120min par intervalles de 15min)
  const durations = [15, 30, 45, 60, 75, 90, 105, 120];
  const [eventResourceId, setEventResourceId] = useState<number>(1); // Default to Room A
  const [lunchColorPickeropened, { open:openedColorPickerlunch, close:closeColorPickerlunch }] = useDisclosure(false);
   const [changeEndValuelunch, setChangeEndValuelunch] = useState('#15AABF');

   // Enhanced lunchtime background modal state
   const [lunchtimeModalOpened, { open: openLunchtimeModal, close: closeLunchtimeModal }] = useDisclosure(false);
   const [selectedLunchRoom, setSelectedLunchRoom] = useState<string>('room-a');
   const [selectedLunchDoctor, setSelectedLunchDoctor] = useState<string>('');
    const addLunchEvent = () => {
       if (selectedHour === null || selectedMinute === null) return;

       const start = new Date(dateSelected);
       start.setHours(selectedHour, selectedMinute, 0);
       const end = new Date(start);
       end.setMinutes(end.getMinutes() + duration);

       const durationText = duration >= 60
         ? `${Math.floor(duration/60)}h${duration % 60 === 0 ? '' : duration % 60}`
         : `${duration}min`;

       // Create lunch event
       const newLunchEvent: CalendarEvent = {
         id: Date.now().toString(),
         title: `🍽️ Déjeuner (${durationText})`,
         start: start,
         end: end,
         desc: `Pause déjeuner de ${selectedHour}h${selectedMinute === 0 ? '00' : selectedMinute} à ${end.getHours()}h${end.getMinutes() === 0 ? '00' : end.getMinutes()}`,
         color: changeEndValuelunch || '#15AABF',
         roomId: eventResourceId === 1 ? 'room-a' : 'room-b',
         resourceId: eventResourceId,
         lunchTime: true
       };

       // Add the lunch event to the calendar
       console.log('🍽️ Adding lunch event:', newLunchEvent);
       if (onEventAdd) {
         onEventAdd(newLunchEvent);
         console.log('✅ Lunch event added successfully');
       } else {
         console.error('❌ onEventAdd callback not available');
       }

       // Reset form

       setSelectedHour(null);
       setSelectedMinute(null);
       setDuration(60);
       setEventResourceId(1);

       notifications.show({
         title: 'Pause déjeuner ajoutée',
         message: `Pause déjeuner programmée de ${selectedHour}h${selectedMinute === 0 ? '00' : selectedMinute} à ${end.getHours()}h${end.getMinutes() === 0 ? '00' : end.getMinutes()}`,
         color: 'green',
       });
     };

     // Enhanced lunchtime modal handler
     const handleLunchtimeSave = (lunchData: {
       id: string;
       title: string;
       start: Date;
       end: Date;
       desc?: string;
       color: string;
       roomId: string;
       resourceId: string | number;
       lunchTime: boolean;
       doctorId: string;
       doctorName: string;
       notes?: string;
       isRecurring?: boolean;
     }) => {
       console.log('🍽️ Enhanced lunch event received:', lunchData);

       // Convert to CalendarEvent format
       const calendarEvent: CalendarEvent = {
         id: lunchData.id,
         title: lunchData.title,
         start: lunchData.start,
         end: lunchData.end,
         desc: lunchData.desc,
         color: lunchData.color,
         roomId: lunchData.roomId,
         resourceId: lunchData.resourceId,
         lunchTime: lunchData.lunchTime,
         docteur: lunchData.doctorName,
         doctor: lunchData.doctorId,
         notes: lunchData.notes
       };

       // Add the lunch event to the calendar
       if (onEventAdd) {
         onEventAdd(calendarEvent);
         console.log('✅ Enhanced lunch event added successfully');
       } else {
         console.error('❌ onEventAdd callback not available');
       }
     };

     // Function to open lunchtime modal with room and doctor context
     const openLunchtimeModalWithContext = (roomId?: string, doctorId?: string) => {
       console.log('🔍 Opening lunchtime modal with context:', { roomId, doctorId });
       console.log('🔍 Current realStaffOptions:', realStaffOptions);
       if (roomId) setSelectedLunchRoom(roomId);
       if (doctorId) setSelectedLunchDoctor(doctorId);
       openLunchtimeModal();
     };

         const [activeVisits, setActiveVisits] = useState<CalendarEvent[]>([]);

         // Real staff options loaded from backend
         const [realStaffOptions, setRealStaffOptions] = useState([
           { label: 'Dr. Loading...', value: 'loading' }
         ]);

         // Load real doctors and assistants from backend using UserManagementPage approach
         useEffect(() => {
           const loadRealDoctorsAndAssistants = async () => {
             try {
               console.log('🔍 Loading doctors and assistants from UserManagementService...');
               
               // First, test the getDoctors endpoint directly for debugging
               try {
                 console.log('🧪 === DEBUGGING GETDOCTORS ENDPOINT ===');
                 await patientAPI.testGetDoctors();
               } catch (debugError) {
                 console.error('🧪 Debug test failed:', debugError);
               }
               
               // Use the same service as UserManagementPage for consistency
               const service = process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true'
                 ? (await import('@/services/mockUserManagementService')).default
                 : (await import('@/services/userManagementService')).default;

               console.log('🔍 Using service:', process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true' ? 'MOCK' : 'REAL');

               // Get user accounts (assistants and staff) - only for current doctor
               const userAccounts = await service.getUserAccounts();
               console.log('🔍 Retrieved user accounts (assistants for current doctor):', userAccounts);

               // Get doctors from patientAPI for comprehensive list
               let doctorUsers: Array<{
                 id: string;
                 first_name: string;
                 last_name: string;
                 email: string;
               }> = [];
               try {
                 console.log('🔍 Fetching doctors from patientAPI.getDoctors()...');
                 const doctorResponse = await patientAPI.getDoctors();
                 console.log('🔍 Raw doctorResponse:', doctorResponse);
                 
                 const doctorResults = Array.isArray(doctorResponse) ? doctorResponse : (doctorResponse.results || []);
                 console.log('🔍 Processed doctorResults:', doctorResults);
                 
                 // Since backend /users/doctors/ endpoint only returns doctors, we don't need to filter by user_type
                 // All results from this endpoint are doctors by definition
                 doctorUsers = doctorResults;
                 console.log('🔍 All doctor users (no filtering needed):', doctorUsers);
                 
                 // Log each doctor's data structure
                 doctorUsers.forEach((doctor, index) => {
                   console.log(`🔍 Doctor ${index + 1} details:`, {
                     id: doctor.id,
                     first_name: doctor.first_name,
                     last_name: doctor.last_name,
                     email: doctor.email,
                     fullStructure: doctor
                   });
                 });
               } catch (doctorError) {
                 console.warn('⚠️ Could not fetch doctors from patientAPI:', doctorError);
               }

               // Get current user from localStorage to add as doctor if needed
               const currentUserId = localStorage.getItem('userId');
               const currentUserType = localStorage.getItem('userType');
               const currentUserFirstName = localStorage.getItem('firstName') || '';
               const currentUserLastName = localStorage.getItem('lastName') || '';
               const currentUserEmail = localStorage.getItem('email') || '';

               // Combine and format all staff options
               const staffOptions: { label: string; value: string }[] = [];

               // Add current user as doctor only if they are the owner and not already in the doctors list
               if (currentUserType === 'doctor' && currentUserId) {
                 const isCurrentUserInDoctorList = doctorUsers.some(user => user.id === currentUserId);
                 if (!isCurrentUserInDoctorList) {
                   const displayName = currentUserFirstName && currentUserLastName 
                     ? `${currentUserFirstName} ${currentUserLastName}`
                     : currentUserEmail.split('@')[0];
                   
                   staffOptions.push({
                     label: `Dr. ${displayName}`.trim(),
                     value: currentUserId
                   });
                   console.log('🔍 Added current user (owner) as doctor:', `Dr. ${displayName}`);
                 }
               } else {
                 console.log('🔍 Current user is not a doctor or not logged in, skipping');
               }

               // Add doctors from patientAPI
               doctorUsers.forEach(user => {
                 // Better name formatting logic to avoid "Dr. " only
                 const firstName = user.first_name?.trim() || '';
                 const lastName = user.last_name?.trim() || '';
                 const emailPrefix = user.email ? user.email.split('@')[0] : '';
                 
                 let displayName = '';
                 if (firstName && lastName) {
                   displayName = `${firstName} ${lastName}`;
                 } else if (firstName) {
                   displayName = firstName;
                 } else if (lastName) {
                   displayName = lastName;
                 } else if (emailPrefix) {
                   displayName = emailPrefix;
                 } else {
                   displayName = 'Nom non défini';
                 }
                 
                 staffOptions.push({
                   label: `Dr. ${displayName}`,
                   value: user.id
                 });
                 console.log('🔍 Added doctor:', `Dr. ${displayName}`, 'from user:', user);
               });

               // Add assistants and staff from UserManagementService
               userAccounts
                 .filter(user => user.user_type === 'assistant' || user.user_type === 'staff')
                 .forEach(user => {
                   const title = user.user_type === 'assistant' ? 'Dr.' : '';
                   staffOptions.push({
                     label: `${title} ${user.first_name} ${user.last_name}`.trim(),
                     value: user.id
                   });
                 });

               console.log('🔍 Final staff options:', staffOptions);
               if (staffOptions.length > 0) {
                 setRealStaffOptions(staffOptions);
                 console.log('✅ Updated realStaffOptions with', staffOptions.length, 'staff members');
               } else {
                 console.log('⚠️ No staff found, keeping default options');
                 // Set a default option indicating no staff found
                 setRealStaffOptions([{ label: 'No staff available', value: 'none' }]);
               }
             } catch (error) {
               console.error('❌ Error loading staff:', error);
               console.log('⚠️ Keeping default options due to error');
               setRealStaffOptions([{ label: 'Error loading staff', value: 'error' }]);
             }
           };

           loadRealDoctorsAndAssistants();
         }, []);
         const [activeIds, setActiveIds] = useState<number[]>([]); // Use an array to track multiple active IDs
          const [dailyCounter, ] = useState(0);
          const [eventIdCounter,] = useState(1);

          // Add missing state variables that don't exist yet
          const [, setWaitingListEvents] = useState<CalendarEvent[]>([]);
          const [, setAppointments] = useState<CalendarEvent[]>([]);
          const [, setSelectedSlot] = useState<{start: Date, end: Date} | null>(null);
          const [, setShowAddModal] = useState(false);
          const [, setEventTime] = useState<string>('');
          const [, setEventDate] = useState<string>('');

          // Add missing functions that don't exist yet
          const updatePatientWithDuration = useCallback((eventId: number, duration: number, startTime?: Date, endTime?: Date) => {
            console.log('Updating patient duration:', eventId, duration, startTime, endTime);
            // Implementation for updating patient duration
          }, []);

          const resetForm = useCallback(() => {
            console.log('Resetting form');
            // Implementation for resetting form
          }, []);

          const openRendezVous = useCallback(() => {
            console.log('Opening rendez-vous');
            // Implementation for opening rendez-vous
          }, []);

          const openedFichepatient = (visitEvent?: CalendarEvent) => {
            console.log('Opening patient file');
            // Navigate to patient form with proper patient ID
            if (visitEvent) {
              const patientId = visitEvent.patient || visitEvent.patient_id || visitEvent.id || 'no-patient-id';
              window.location.href = `/patient/patient-form/${patientId}/`;
            } else {
              // Fallback to a general patient form page
              window.location.href = '/patient/patient-form/';
            }
          };

          const handleEditClick = (visit: CalendarEvent) => {
            console.log('Editing visit:', visit);
            // Implementation for editing visit
          };

          const handleDragEnd = (result: { destination?: { index: number } | null; source: { index: number }; draggableId: string }) => {
            console.log('Drag ended:', result);
            // Implementation for drag end
            if (!result.destination) return;

            // Handle reordering logic here
            const { source, destination } = result;
            if (source.index !== destination.index) {
              // Reorder the items
              console.log(`Moving item from ${source.index} to ${destination.index}`);
            }
          };

          const toggleEvent = useCallback((eventId: number) => {
            console.log('Toggling event:', eventId);
            // Implementation for toggling event
            setActiveIds(prev => {
              if (prev.includes(eventId)) {
                return prev.filter(id => id !== eventId);
              } else {
                return [...prev, eventId];
              }
            });
          }, []);

          const handlers = {
            reorder: ({ from, to }: { from: number; to: number }) => {
              console.log('Reordering from', from, 'to', to);
              // Implementation for reordering
            }
          };

          // Add setEvents function
          const setEvents = (updater: (prev: CalendarEvent[]) => CalendarEvent[]) => {
            console.log('Setting events');
            // This should be connected to the parent component's setEvents
            // For now, we'll just log it
          };
          const options = [
  'Tous',
  'Consultation',
  'Contrôle',
  'Chirurgie/Paro',
  'Composite',
  'Depose Sutures',
  '1er Consultation',
  'Devis',
  'Endodontie',
  'Formation',
  'Implantologie',
  'Orthodontie',
  'PA ES Chassis/Monta',
  'PA Pose',
  'PC TP EMP',
  'PC ESS Armature',
  'PC Scellement',
  'Prophylaxie',
  'Urgent',
  'Detartrage',
  'Obturation Canalaire',
  'polissage',
  'Changement D\'élastique',
  'Collage',
  'Contention',
  'Echographie',

];


 const [selected, setSelected] = useState<string[]>([]);
 const [activeEvent_Ids, setActiveEvent_Ids] = useState<Set<number>>(new Set());
  const toggleSelection = (item: string) => {
    setSelected((current) =>
      current.includes(item)
        ? current.filter((val) => val !== item)
        : [...current, item]
    );
  }
  // To remove an event ID from the Set
  const removeEventId = useCallback((eventId: number) => {
    setActiveEvent_Ids(prevIds => {
      const newIds = new Set(prevIds);
      newIds.delete(eventId);
      return newIds;
    });
  }, []);
  const [waitingRoomVisits, setWaitingRoomVisits] = useState<CalendarEvent[]>([]);
  // Add missing historiquejournalier state for daily history
  const [historiquejournalier, setHistoriquejournalier] = useState<CalendarEvent[]>([]);

  // Function to move appointment to waiting room
  const moveToWaitingRoom = useCallback((appointment: CalendarEvent) => {
    // Remove from active visits
    setActiveVisits(prev => prev.filter(visit => visit.id !== appointment.id));
    // Add to waiting room
    setWaitingRoomVisits(prev => [...prev, appointment]);
    // Remove from active event IDs
    removeEventId(Number(appointment.id));

    notifications.show({
      title: 'Moved to Waiting Room',
      message: `${appointment.first_name} ${appointment.last_name} moved to waiting room`,
      color: 'blue',
      autoClose: 2000,
    });
  }, [removeEventId]);

  // Enhanced function to move appointment back to active visits with backend integration
  const moveToActiveVisits = useCallback(async (appointment: CalendarEvent) => {
    console.log('🔄 Moving to active visits:', appointment.title);

    try {
      // Import API dynamically
      const { appointmentStateAPI } = await import('@/services/api');

      // Update backend: mark appointment as active visit
      await appointmentStateAPI.toggleActiveVisits(appointment.id, true);
      console.log('✅ Backend updated: appointment activated for active visits');

      // Remove from waiting room
      setWaitingRoomVisits(prev => prev.filter(visit => visit.id !== appointment.id));
      // Add to active visits
      setActiveVisits(prev => [...prev, appointment]);

      notifications.show({
        title: 'Déplacé vers les visites actives',
        message: `${appointment.first_name} ${appointment.last_name} a été déplacé vers les visites actives`,
        color: 'green',
        autoClose: 3000,
      });
    } catch (error) {
      console.error('Error moving to active visits:', error);
      notifications.show({
        title: 'Erreur',
        message: error instanceof Error ? error.message : 'Impossible de déplacer vers les visites actives',
        color: 'red',
        autoClose: 5000
      });
    }
  }, []);
    const [currentTimes, setCurrentTimes] = useState<Map<number, string>>(new Map());
      const [eventDurations, setEventDurations] = useState<Map<number, number>>(new Map());
      const [, setEventStartTimes] = useState<Map<number, Date>>(new Map());

      // Add missing addEventId function
      const addEventId = useCallback((eventId: number) => {
        setActiveEvent_Ids(prevIds => {
          const newIds = new Set(prevIds);
          newIds.add(eventId);
          return newIds;
        });
      }, []);
      // Time tracking functions
      const handleTimeTracking = useCallback((eventId: number, isChecked: boolean) => {
        const currentTime = new Date();

        if (isChecked) {
          // Starting the timer - record start time
          setEventStartTimes(prev => new Map(prev.set(eventId, currentTime)));
          addEventId(eventId);

          notifications.show({
            title: 'Intervention Started',
            message: `Started at ${currentTime.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}`,
            color: 'green',
            autoClose: 2000,
          });
        } else {
          // Stopping the timer - calculate duration
          setEventStartTimes(prev => {
            const startTime = prev.get(eventId);
            if (startTime) {
              const duration = Math.round((currentTime.getTime() - startTime.getTime()) / (1000 * 60)); // Duration in minutes
              setEventDurations(prevDurations => new Map(prevDurations.set(eventId, duration)));

              // Update the appointment/patient record with the duration
              updatePatientWithDuration(eventId, duration, startTime, currentTime);

              // Clean up current time
              setCurrentTimes(prevTimes => {
                const newMap = new Map(prevTimes);
                newMap.delete(eventId);
                return newMap;
              });

              notifications.show({
                title: 'Intervention Completed',
                message: `Duration: ${duration} minutes`,
                color: 'blue',
                autoClose: 3000,
              });
            }

            // Clean up start time
            const newMap = new Map(prev);
            newMap.delete(eventId);
            return newMap;
          });

          removeEventId(eventId);
        }
      }, [addEventId, removeEventId, updatePatientWithDuration]);

          // Generate draggable items
      const [VisitesActivesDeleteOpened, { open: openedVisitesActivesDelete, close: closeVisitesActivesDelete }] = useDisclosure(false);
      const [visitToDelete, setVisitToDelete] = useState<CalendarEvent | null>(null);
       const deleteVisits = (id: number | string, fromWaitingList = false) => {
          // Convert id to number if it's a string
          const numericId = typeof id === 'string' ? parseInt(id, 10) : id;

          if (isNaN(numericId)) {
            console.error("Invalid event ID:", id);
            notifications.show({
              title: 'Error',
              message: 'Could not delete event: Invalid ID',
              color: 'red',
            });
            return;
          }

          

          if (fromWaitingList) {
            setWaitingListEvents((prevEvents) => {
              return prevEvents.filter((event) => Number(event.id) !== numericId);
            });
          } else {
            setEvents((prevEvents) => {
              return prevEvents.filter((event) => Number(event.id) !== numericId);
            });

            // Also update appointments
            setAppointments((prevAppointments) => {
              return prevAppointments.filter((appointment) => Number(appointment.id) !== numericId);
            });
          }

          // Also remove from activeVisits if present
          setActiveVisits((prevVisits) => {
            return prevVisits.filter((event) => Number(event.id) !== numericId);
          });

          // Close the modal after deletion
          closeVisitesActivesDelete();
        };
           // Handle creating an appointment from calendar click
           const handleSlotSelect = useCallback((slotInfo: {start: Date, end: Date}) => {
            setSelectedSlot(slotInfo);

            // Reset form first, then set the slot-specific values
            resetForm();

            // Pre-fill date and time from the selected slot (after reset)
            const slotDate = moment(slotInfo.start).format('YYYY-MM-DD');
            const slotTime = moment(slotInfo.start).format('HH:mm');
            setEventDate(slotDate);
            setEventTime(slotTime);
            setShowAddModal(true);
            openRendezVous();
          }, [resetForm, openRendezVous]);
     // Fonctions pour gérer la sidebar

  // Add missing functions to fix TypeScript errors
 

  // activeVisits and activeIds are already declared as state variables above

  return (
       <DragDropContext onDragEnd={handleDragEnd}>
    <>
      <div className="h-[40px] bg-[#E6E9EC] flex my-2 p-1">
        <div className="w-[50%]">
           <Group>
             <div className="flex justify-start">
                                           {/* liste d'attente */}
                       <span   onClick={(event) => {
                        event.preventDefault();
                        toggleSidebar(); // Toggle sidebar visibility
                      }}
                      className=" cursor-pointer"
                      >
                          <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"
                          className="lucide lucide-list-tree bg-[#E6E9EC] text-[#3799CE]" aria-hidden="true"><path d="M21 12h-8"></path><path d="M21 6H8"></path><path d="M21 18h-8"></path><path d="M3 6v4c0 1.1.9 2 2 2h3"></path><path d="M3 10v6c0 1.1.9 2 2 2h3"></path></svg>
                       </span>
                        <Divider orientation="vertical" color="#3799CE" h={20} mt={4}/>
                        <Avatar size={26}  radius={30} variant="filled" color="cyan" className="border-2 rounded-full border-[#fff] mr-[2px] ">
                          <Text fz="sm" fw={500} c={"white"}>
                           0
                          </Text>
                          </Avatar>
                        <ThemeIcon className="cursor-pointer">
                        {/* <IconTextPlus stroke={2} size={30} className="bg-[#E6E9EC] text-[#3799CE] "
                                          /> */}
                                          <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-list-plus bg-[#E6E9EC] text-[#3799CE]" aria-hidden="true"><path d="M11 12H3"></path><path d="M16 6H3"></path><path d="M16 18H3"></path><path d="M18 9v6"></path><path d="M21 12h-6"></path></svg>
                                          </ThemeIcon>
                                          </div>
                                        <Group gap={0}>
               <h3 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4 capitalize text-[#797C7F]">
                         <IconCalendarClock stroke={1.5} className="mr-1 h-3.5 w-3.5 " />
                         {getCurrentDateTitle()}
                       </h3>
                    <Divider orientation="vertical" color="#000000" h={20} mt={6}/>
                    </Group>
              <Group gap="xs">
                <Button
                      size="xs"
                      className="HoverButton"
                      rightSection={<IconChevronDown stroke={2} />}
                       onClick={open}
                    >
                      La durée du déjeuner
                    </Button>

                    <Button
                      size="xs"
                      className="HoverButton"
                      variant="light"
                      color="teal"
                      leftSection={<IconClock size={14} />}
                      onClick={() => openLunchtimeModalWithContext()}
                    >
                      Pause Avancée
                    </Button>
                  </Group>
                     <Modal
                              opened={opened}
                              onClose={close}
                              size="auto"
                               title={`Sélectionnez l'heure et la durée du déjeuner :`}
                            >
                             <div className="time-selector p-4 border rounded mb-4">
                             <div className="grid gap-2">
                                  <div className="flex gap-2 mb-2">
                              {/* Sélection de l'heure */}
                                    <Select
                                id="lunchHour"
                                label="Heure:"
                                placeholder="Heure:"
                               onChange={(value) => setSelectedHour(Number(value))}
                                value={selectedHour === null ? "" : selectedHour.toString()}
                                data={hours.map(hour => ({ value: hour.toString(), label: `${hour}h` }))}
                              />
                              {/* Sélection des minutes */}
                          <Select
                            id="lunchMinute"
                            label="Minutes:"
                            placeholder="Minutes:"
                            onChange={(value) => setSelectedMinute(Number(value))}
                            value={selectedMinute === null ? "" : selectedMinute.toString()}
                            data={minutes.map(minute => ({
                             value: minute.toString(),
                             label: minute === 0 ? "00" : minute.toString(),
                            }))}
                            disabled={selectedHour === null}
                          />
                          {/* Sélection de la durée */}
                          <Select
                            id="lunchDuration"
                            label="Durée:"
                            placeholder="Durée:"
                            onChange={(value) => setDuration(Number(value))}
                            value={duration === null ? "" : duration.toString()}
                            data={durations.map(dur => ({
                              value: dur.toString(),
                              label: dur >= 60
                                ? `${Math.floor(dur / 60)}h${dur % 60 === 0 ? '' : dur % 60 + 'min'}`
                                : `${dur}min`
                            }))}
                            disabled={selectedHour === null || selectedMinute === null}
                          />

                          <Select
                    value={eventResourceId ? eventResourceId.toString() : ""}
                    onChange={(value) => {
                      console.log("Setting resourceId to:", value);
                      setEventResourceId(Number(value) || 1);
                    }}
                    name="resourceId"
                    placeholder="Room"
                    data={[
                      { value: "1", label: "Room A" },
                      { value: "2", label: "Room B" },
                    ]}
                    required
                    className="select w-full max-w-xs mt-6"
                    leftSection={<Icon path={mdiBedQueenOutline} size={1} />}
                  />
                  <Avatar color="#4BA3D3" radius="sm"  ml={4} h={36} onClick={openedColorPickerlunch} mt={22}>
                   <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 200 200"
                   style={{width: "26px",height:"26px"}}
                   >
                     <path fill="#FF5178" d="M100 0a100 100 0 00-50 13.398l30 51.961A40 40 0 01100 60V0z"></path><path fill="#FF9259" d="M49.982 13.408a99.999 99.999 0 00-36.595 36.61l51.968 29.99a40 40 0 0114.638-14.645l-30.01-51.955z"></path><path fill="#FFD23B" d="M13.386 50.02A100 100 0 000 100.025l60-.014a40 40 0 015.354-20.002L13.386 50.021z"></path><path fill="#89C247" d="M0 100a99.999 99.999 0 0013.398 50l51.961-30A40.001 40.001 0 0160 100H0z"></path><path fill="#49B296" d="M13.39 149.989a100.001 100.001 0 0036.599 36.607l30.006-51.958a39.99 39.99 0 01-14.639-14.643l-51.965 29.994z"></path><path fill="#2897B1" d="M49.989 186.596A99.995 99.995 0 0099.987 200l.008-60a39.996 39.996 0 01-20-5.362l-30.007 51.958z"></path><path fill="#3EC3FF" d="M100 200c17.554 0 34.798-4.621 50-13.397l-30-51.962A40 40 0 01100 140v60z"></path><path fill="#09A1E5" d="M150.003 186.601a100.001 100.001 0 0036.601-36.604l-51.962-29.998a40 40 0 01-14.641 14.641l30.002 51.961z"></path><path fill="#077CCC" d="M186.607 149.992A99.993 99.993 0 00200 99.99l-60 .006a39.998 39.998 0 01-5.357 20.001l51.964 29.995z"></path><path fill="#622876" d="M200 100c0-17.554-4.621-34.798-13.397-50l-51.962 30A39.997 39.997 0 01140 100h60z"></path><path fill="#962B7C" d="M186.597 49.99a99.994 99.994 0 00-36.606-36.598l-29.995 51.965a40 40 0 0114.643 14.64l51.958-30.006z"></path><path fill="#CB2E81" d="M149.976 13.384A99.999 99.999 0 0099.973 0l.016 60a40.001 40.001 0 0120.002 5.353l29.985-51.97z"></path></svg>
                   </Avatar>
                   <Modal opened={lunchColorPickeropened} onClose={closeColorPickerlunch}  size="auto" yOffset="18vh" xOffset={30} withCloseButton={false}>
                           <ColorPicker  defaultValue="#FFFFFF"
                        onChangeEnd={setChangeEndValuelunch} format="hex" swatches={['#2e2e2e', '#868e96', '#fa5252', '#e64980', '#be4bdb', '#7950f2', '#4c6ef5', '#228be6', '#15aabf', '#12b886', '#40c057', '#82c91e', '#fab005', '#fd7e14']} />
                        <Group justify="center" mt={8}>
                          {/* <Text > <b>{changeEndValue}</b> </Text> */}
                          <Button variant="filled" w={"100%"} color={`${changeEndValuelunch}`} leftSection={<IconColorPicker stroke={1} size={18} />} onClick={() => { closeColorPickerlunch()}}>Registre</Button>
                        </Group>
                  </Modal>
                            </div>
                            <Button
                              className="w-full hover:bg-[#3799CE]/90"
                              onClick={() => {
                                close();
                              }}
                            >
                              Annuler
                            </Button>
                            <Button
                              className={selectedHour === null || selectedMinute === null ? "w-full disabled":"w-full hover:bg-[#3799CE]/90"}
                              onClick={() => {
                              addLunchEvent();
                                close();
                              }}
                            >
                                Confirmer
                            </Button>
                          </div>
                          </div>
                             </Modal>
            </Group>
        </div>
        <div className="w-[50%]">
           <Group justify="flex-end">
                 <Button
                  size="xs"
                  className="HoverButton"
                  rightSection={<IconChevronDown stroke={2} />}
                 onClick={openedStartofwork}
                >
                  Début des travaux
                </Button>
                    <Modal
                                                opened={StartofworkOpened}
                                                onClose={closeStartofwork}
                                                withCloseButton={false}
                                                size="auto"
                                                 title={`Vous pouvez spécifier le début et la fin des travaux.`}
                                              >
                                               <div className="time-selector p-4 border rounded mb-4">
                                               <div className="grid gap-2">
                                                    <div className="flex gap-2 mb-2">
                                                 <div className="grid gap-2">
                                                          <div className="flex gap-2">
                                                            <NumberInput
                                                              label="Heure de début des travaux"
                                                              placeholder="Select hour"
                                                                  clampBehavior="strict"
                                                                  step={1}
                                                        min={0}
                                                        max={23}
                                                        value={minHour}
                                                        onChange={(value) => setMinHour(value as number)}
                                                            />
                                                            <NumberInput
                                                              label="Minute de début"
                                                              placeholder="Select minute"
                                                              clampBehavior="strict"
                                                              step={5}
                                                              min={0}
                                                              max={59}
                                                              value={minMinute}
                                                              onChange={(value) => setMinMinute(value as number)}
                                                            />
                                                          </div>
                                                        </div>
                                                          <div className="grid gap-2">
                                                  <div className="flex gap-2">
                                                    <NumberInput
                                                      label="Fin de l'heure de travail"
                                                      placeholder="Select hour"
                                                      clampBehavior="strict"
                                                      step={1}
                                                      min={0}
                                                      max={23}
                                                      value={maxHour}
                                                      onChange={(value) => setMaxHour(value as number)}
                                                      className="w-[50%]"
                                                    />
                                                    <NumberInput
                                                      label="Minute de fin"
                                                      placeholder="Select minute"
                                                      clampBehavior="strict"
                                                      step={5}
                                                      min={0}
                                                      max={59}
                                                      value={maxMinute}
                                                      onChange={(value) => setMaxMinute(value as number)}
                                                      className="w-[50%]"
                                                    />
                                                  </div>
                                                </div>
                                                <div className="grid gap-2">
                                                  <div className="flex gap-2">
                                        <Select
                                        label="Intervalle (minutes) :"
                                        value={step.toString()} // Ensure value is a string
                                        onChange={(value) => {
                                          if (value) {
                                            setStep(parseInt(value, 10)); // Convert string to number
                                          }
                                        }}
                                        placeholder="Select a step"
                                        data={[
                                          // { value: "5", label: "5" },
                                          // { value: "10", label: "10" },
                                          { value: "15", label: "15" },
                                          { value: "20", label: "20" },
                                          { value: "25", label: "25" },
                                          { value: "30", label: "30" },
                                          { value: "45", label: "45" },
                                        ]}
                                        className="w-full"
                                        defaultValue="15" // Set default to 15 minutes
                                      />
                                                  </div>

                                                </div>
                                              </div>
                                              <Button
                                                className="w-full hover:bg-[#3799CE]/90"
                                                onClick={() => {
                                                  closeStartofwork();
                                                }}
                                              >
                                                Annuler
                                              </Button>
                                              <Button
                                                className="w-full hover:bg-[#3799CE]/90"
                                                onClick={handleSave}
                                              >
                                                  Confirmer
                                              </Button>
                                            </div>
                                            </div>
                                               </Modal>
                <Group>
                  <Tooltip
                      label="Changer ressource"
                      position="bottom"
                      withArrow
                      className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                       style={{color:"var(--mantine-color-text)"}}
                    >

                        {/* <h2 className="flex px-[.75rem] py-[.5rem] align-middle text-xs font-bold leading-4  ">
                        <IconFileTypography stroke={1.75} className="h-4 w-4  hover:text-[#3799CE]" />
                        </h2> */}
                        <Icon path={mdiAccountNetwork} size={1} color={"#797C7F"}/>
                    </Tooltip>
                    <Divider orientation="vertical" color="#000000" h={20} mt={6}/>
                       <Tooltip
                      label="Filtrer par motifs"
                      position="bottom"
                      withArrow
                      className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                       style={{color:"var(--mantine-color-text)"}}
                    >
                        <Icon path={mdiBookmarkOutline} size={1} color={"#797C7F"}/>
                    </Tooltip>
                    <Divider orientation="vertical" color="#000000" h={20} mt={6}/>
                      <Tooltip
                      label="Nouvel événement"
                      position="bottom"
                      withArrow
                      className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                       style={{color:"var(--mantine-color-text)"}}
                    >


                       <Icon path={mdiCalendarRange} size={1} color={"#797C7F"} />

                    </Tooltip>
                     <Tooltip
                      label="List des événements"
                      position="bottom"
                      withArrow
                      className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                       style={{color:"var(--mantine-color-text)"}}
                    >
        <Icon path={mdiCalendarMonth} size={1} color={"#797C7F"}/>


                    </Tooltip>
                     <Tooltip
                      label="Planifier des rendez-vous"
                      position="bottom"
                      withArrow
                      className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                       style={{color:"var(--mantine-color-text)"}}
                    >
        <Icon path={mdiCalendarPlus} size={1}  color={"#797C7F"}/>


                    </Tooltip>
                       <Divider orientation="vertical" color="#000000" h={20} mt={6}/>
                     <Tooltip
                      label="Chercher des rendez-vous"
                      position="bottom"
                      withArrow
                      className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                       style={{color:"var(--mantine-color-text)"}}
                    >
        <Icon path={mdiMagnify} size={1}  color={"#797C7F"}/>
                    </Tooltip>
                  </Group>
                {/* Refresh button for appointments */}
                <Button
                  variant="light"
                  size="xs"
                  // onClick={fetchTodayAppointments}
                  // loading={loadingAppointments}
                  leftSection={<IconRefresh size={14} />}
                  color="#15aabf"
                >
                  Rafraîchir
                </Button>
            </Group>
        </div>
        </div>
        <div className="flex h-full">
    <div className={isSidebarAlert ? "w-[80%] flex flex-col h-full " : " w-full flex flex-col h-full"}>
        <div className="flex">
        <div className= "w-[70%] overflow-hidden" >
      {/* Navigation Header */}
           <Group justify="space-between" p={4} className="border-2 border-[#3799CE] rounded-t-md bg-[#3799CE]">
          <Button variant="filled" size="sm" onClick={() => handleNavigate("today")} className=" rounded-md  text-[var(--text-tab)] ButtonHover" fz="md">
                    Aujourd’hui
                  </Button>
         <div className="flex items-center">
                   <button className="btn-sm  mr-1" onClick={() => handleNavigate('prev')}> <Icon path={mdiChevronLeft} size={1} color={"white"}/></button>
                   <Text fw={550} c={"var(--text-daisy-white)"}>{getCurrentDateTitle()}</Text>
                   <button className="btn-sm  ml-1" onClick={() => handleNavigate('next')}><Icon path={mdiChevronRight} size={1} color={"white"}/></button>
                 </div>
         {/* Date Selector */}
        <SelectJournee date={currentDate} setter={handleDateChange} label={label} />
    </Group>
    <div></div>
    
     <Card shadow="sm" padding="0px" radius="md" withBorder mb={60} >
      <div className={isSidebarVisible ? "w-[910px]" : " w-full "}>
        <div className="flex">
                 {isSidebarVisible && (
        <div  style={{padding:"5px", width:226 , height: 600, borderRight: "1px solid light-dark(var(--mantine-color-gray-3), var(--mantine-color-dark-4))"}}>
          {waitingList.length === 0 ? (
             <div className="mb-2 flex justify-end w-[216px]" >
            <Button
                // Added null check
                fullWidth
              bg={"#3799CE"}
              classNames={classes}
              radius="md"
              rightSection={
                <IconArrowsExchange stroke={1.75}
                  style={{ width: rem(20) }}
                  onClick={(event) => {
                    event.preventDefault();
                    toggleSidebar(); // Toggle sidebar visibility
                  }}
                />
              }
            >
              <Text pr={8}>Loading events</Text>
              <Loader color="white" size="xs" type="dots" pt={6}/>
            </Button>
            </div>
          ) : (
            <div className="mb-2 flex justify-end w-[216px]" >
            <Button
                leftSection={waitingList?.length || 0} // Added null check
                fullWidth
              bg={"#3799CE"}
              classNames={classes}
              radius="md"
              rightSection={
                <IconArrowsExchange stroke={1.75}
                  style={{ width: rem(20) }}
                  onClick={(event) => {
                    event.preventDefault();
                    toggleSidebar(); // Toggle sidebar visibility
                  }}
                />
              }
            >
              Nombre de patients
            </Button>
          </div>
          )}
           <div className={waitingList.length <=3 ?" w-[226px] z-40" : "h-[544px] w-[220px] "}>
           <SimpleBar className="simplebar-scrollable-y h-[calc(100%)] z-10">
           <div className="pr-3">
           
   <Droppable droppableId="waitingList" direction="vertical">
 {(provided) => (
 
 
   <div {...provided.droppableProps} ref={provided.innerRef}className="pr-3 " >
   {/*
    */}
   {waitingList.map((patient, index) => (
               <Draggable
                 key={patient.id}
                 draggableId={patient.id}
                 index={index}
               >
                 {(provided) => (
 
                   <Box
                     ref={provided.innerRef}
                     {...provided.draggableProps}
                     {...provided.dragHandleProps}
 
                   >
              <Paper  className={`${
                  patient.eventType === "visit"
                  ? "border-l-4 border-l-[#34D1BF]"
                  : patient.eventType === "visitor-counter"
                  ? "border-l-4 border-l-[#F17105]"
                  : patient.eventType === "completed"
                  ? "border-l-4 border-l-[#3799CE]"
                  : patient.eventType === "diagnosis"
                  ? "border-l-4 border-l-[#ED0423]"
                  : "text-[var(--mantine-color-dark-0)]"
              }`}
 
                mb="sm"
                     p="4px"
                     withBorder >
                       <Group justify="space-between" mb="0px" >
                       <Group >
                       <Badge
                           size="sm"
                           variant="filled"
                           style={{
                             pointerEvents: "none",
                             padding: 0,
                             width: rem(20),
                             height: rem(20),
                             backgroundColor:`#3799CE`
                           }}
 
                         >
                            {index + 1}
                         </Badge>
                         <Text fz="xs" fw={700} className="uppercase" ml={-10}>
                         {patient.first_name.slice(0, 8)}&nbsp;{patient.last_name.slice(0, 3)}..
                         </Text>
                         </Group>
 
                           <Indicator
                           position="middle-end"
                               inline
                               size={12}
                               offset={0}
                               color="#3799CE"
                               mt={0}
                               withBorder
                               mr={4}
                               ml={10}
                             />
                         {/* <CloseButton mr={-9} mt={0} /> */}
                          <Menu
                         shadow="md"
                         position="left-start"
                         width={200}
                         transitionProps={{
                           transition: "rotate-right",
                           duration: 150,
                         }}
                       >
                         <Menu.Target>
                           <ActionIcon variant="subtle" aria-label="Menu options">
                             <IconDots size={18} />
                           </ActionIcon>
                         </Menu.Target>
                         <Menu.Dropdown>
                           <Menu.Item
                             leftSection={
                               <IconFilePencil stroke={2} size={14}  className="mt-1" color="#3799CE" />
                             }

                            onClick={(clickEvent) => {
                              clickEvent.stopPropagation();
                              if (onEventEdit) {
                                // Convert patient to CalendarEvent format for editing
                                const calendarEvent: CalendarEvent = {
                                  id: patient.id,
                                  title: patient.title || `${patient.first_name} ${patient.last_name}`,
                                  start: patient.date && patient.start
                                    ? new Date(`${patient.date}T${patient.start}`)
                                    : new Date(),
                                  end: patient.date && patient.end
                                    ? new Date(`${patient.date}T${patient.end}`)
                                    : new Date(Date.now() + (patient.duration || 30) * 60 * 1000),
                                  desc: patient.desc || patient.notes,
                                  color: patient.color,
                                  roomId: patient.resourceId?.toString(),
                                  resourceId: patient.resourceId,
                                  first_name: patient.first_name,
                                  last_name: patient.last_name,
                                  phone_number: patient.phone_number,
                                  phone_numbers: patient.phone_numbers,
                                  duration: patient.duration,
                                  address: patient.address,
                                  notes: patient.notes,
                                  docteur: patient.docteur,
                                  typeConsultation: patient.typeConsultation,
                                  eventType: patient.eventType,
                                  consultationDuration: patient.consultationDuration,
                                };
                                onEventEdit(calendarEvent);
                              } else {
                                // Fallback to local edit modal
                                openEditPatient(patient);
                              }
                            }}
                           >
                             Modifier
                           </Menu.Item>
                           <Menu.Item
                                 leftSection={
                                  <IconEdit
                                    size={14}
                                    className="mt-1"
                                    color="#3799CE"
                                  />

                                }
                                onClick={() => {
                                  setSelectedEvent(patient);
                                  setShowViewModal(true);
                                }}
                                >
                                  Détails
                                </Menu.Item>

                           <Menu.Divider />

                           

                          <Menu.Item
                            leftSection={
                              <IconCalendarPlus stroke={2} size={14} className="mt-1" color="blue" />
                            }
                             onClick={async (clickEvent) => {
                               clickEvent.stopPropagation();
                               await moveToCalendarWithOptions(patient, { addToWaitingList: true });
                             }}
                             color="blue"
                          >
                            Add to Calendar
                          </Menu.Item>

                         

                          <Menu.Divider />

                           <Menu.Item
                             leftSection={
                               <IconTrash stroke={2} size={14} className="mt-1" color="red" />
                             }
                              onClick={() => setWaitingList(waitingList.filter(p => p.id !== patient.id))}
                                  color="red"
                           >
                             Supprimer
                           </Menu.Item>
                         </Menu.Dropdown>
                       </Menu>
                       </Group>
                       <Box className="flex" w={100}>
                         <List
                          spacing="2px"
                          size="xs"
                          center
                          style={{width:"100px"}}
 
                        >
                          <List.Item  icon={<span  color={patient.color}><svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 32 32" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-96 55.2C54 332.9 0 401.3 0 482.3C0 498.7 13.3 512 29.7 512l388.6 0c16.4 0 29.7-13.3 29.7-29.7c0-81-54-149.4-128-171.1l0 50.8c27.6 7.1 48 32.2 48 62l0 40c0 8.8-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16s7.2-16 16-16l0-24c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 24c8.8 0 16 7.2 16 16s-7.2 16-16 16l-16 0c-8.8 0-16-7.2-16-16l0-40c0-29.8 20.4-54.9 48-62l0-57.1c-6-.6-12.1-.9-18.3-.9l-91.4 0c-6.2 0-12.3 .3-18.3 .9l0 65.4c23.1 6.9 40 28.3 40 53.7c0 30.9-25.1 56-56 56s-56-25.1-56-56c0-25.4 16.9-46.8 40-53.7l0-59.1zM144 448a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"></path></svg></span>}><Text size="xs" c="dimmed" truncate="end">{patient.docteur}</Text> </List.Item>
                          <List.Item icon={<Icon path={mdiClipboardEditOutline} size={1}  color={patient.color}/>}><Text size="xs" c="dimmed" truncate="end">{patient.typeConsultation?.slice(0, 8) || 'N/A'}</Text></List.Item>
                          {/* <List.Item  icon={  <MdOutlineUpdate size={12} color={patient.color} />}> <Text size="xs" c="dimmed">{moment(patient.date).format("DD/MM/YYYY")} </Text></List.Item> */}
 
                        </List>
                        <List
                          spacing="2px"
                          size="xs"
                          center
                          style={{marginLeft:"4px",width:"100px",paddingLeft:"6px"}}
                          className={`${
                                patient.eventType === "visit"
                              ? "border-l-4 border-l-[#34D1BF]"
                              : patient.eventType === "visitor-counter"
                              ? "border-l-4 border-l-[#F17105]"
                              : patient.eventType === "completed"
                              ? "border-l-4 border-l-[#3799CE]"
                              : patient.eventType === "diagnosis"
                              ? "border-l-4 border-l-[#ED0423]"
                              : "text-[var(--mantine-color-dark-0)]"
                          }`}
                        >
 
                          <List.Item  icon={<Icon path={mdiMapMarkerPath} size={1}   color={patient.color}/>}><Text size="xs" c="dimmed" truncate="end" >{patient.address?.slice(0,9) || 'N/A'}..</Text></List.Item>
                          <List.Item  icon={ <IconMessageShare stroke={2}  size={10} strokeWidth={1.5}  color={patient.color}/>}><Text size="xs" c="dimmed" truncate="end">{patient.notes}...</Text></List.Item>
                        </List>
                        </Box>
                        </Paper>
                   </Box>
                 )}
               </Draggable>
             ))}
             {/*
        </div>       */}
 {provided.placeholder}
   </div>
 
 
 )}
           </Droppable>
          
          </div>
          </SimpleBar>
          </div>
   </div>
   )}
   <div className="flex flex-col h-full bg-white w-full">
      {/* Header with room names */}
      <div className="flex border-b border-gray-300 bg-[#E6E9EC] h-[23px]">
        <div className="w-16 p-3 border-r border-gray-200"></div>
        {rooms.map(room => (
          <div
            key={room.id}
            className="flex-1  text-center font-semibold text-base text-[#565B61] border-r border-gray-200 last:border-r-0 bg-[#E6E9EC]"
          >
            {room.name}
          </div>
        ))}
      </div>

      {/* Time slots and rooms grid */}
      <div className="flex-1  relative ">
        <div className="py-2  h-[600px] ">
        <SimpleBar className="simplebar-scrollable-y h-[calc(100%)] " style={{overflowX: 'hidden' }} autoHide={true}>
           <div className="pr-4">
        {timeSlots.map(({ hour, minute }) => {
          // Create a Date object for this time slot
          const slotDate = new Date(currentDate);
          slotDate.setHours(hour, minute, 0, 0);

          return (
            <TimeSlot
              key={`${hour}-${minute}`}
              value={slotDate}
              step={15} // 15-minute intervals
              isRender={isToday} // Only show time indicator for today
            >
              <div className="flex   relative " style={{ height: '32px' }}>
                {/* Time label */}
                <div style={{color:"#74a4c3" , zIndex:"1" ,width:'83px'}} className=" border-x border-[var(--rbc-border)] " >

                    <div className="rbc-timeslot-group mt-1.5">
                      <div className="rbc-time-slot" style={{maxHeight: "26.5px",}}>
                        <span className="rbc-label" style={{top: "-14px !important"}}>{formatTime(hour, minute)}</span>
                        </div>
                        </div>
 </div>
                {/* Room columns */}
                {rooms.map(room => {
                  const slotEvents = getEventsForTimeSlot(localEvents, currentDate, hour, minute, room.id);
                  return (
                    <div
                      key={`${room.id}-${hour}-${minute}`}
                      className="flex-1 border-r  last:border-r-0 relative cursor-pointer hover:bg-blue-25 transition-colors bg-white"
                      style={{borderTop: "1px dashed var(--rbc-border) !important"}}
                      onClick={() => onTimeSlotClick?.(currentDate, hour, minute, room.id)}
                    >
                      {slotEvents.map((event, eventIndex) => (
                        <EventComponent
                          key={`${event.id}-${hour}-${minute}-${room.id}-${eventIndex}`}
                          event={event}
                           onEdit={onEventEdit}
                           onDelete={handleEventDelete}
                           onToggleSidebar={toggleSidebarAlert}
                        />

                      ))}
                    </div>
                  );
                })}
              </div>
            </TimeSlot>
          );
        })}
        </div>
        </SimpleBar>
        </div>
      </div>
    </div>
      </div>
      </div>
      
      </Card>
     
      
      </div>
       <div className="overflow-hidden w-[30%] mx-1 mb-15 shadow-md">
        <Card  padding="0px" radius="md"  h={'100%'}>
     <Tabs variant="none" value={value} onChange={setValue}>
         <Tabs.List ref={setRootRef} className={classes.list}>
                 <div className="rounded-t-lg bg-[#3799CE] p-3 text-[#ffffff] flex w-full justify-between items-center" style={{ position: "relative" }}>
<span className="text-sm font-medium capitalize flex gap-2">
        <IconStethoscope stroke={1.25} />
       <Text size="md"> Visites Actives</Text>
      </span>
 <div className="flex gap-2">
      <Tabs.Tab
    value="1"
    ref={setControlRef('1')}
    styles={{
      tab: {
        zIndex: 1,
        fontWeight: 500,
        transition: 'all 100ms ease',
        color: '#ffffff',
        padding: '8px 12px',

      },
    }}
  >
    Room A
  </Tabs.Tab>
        <Tabs.Tab
          value="2"
          ref={setControlRef('2')}
          styles={{
            tab: {
              zIndex: 1,
              fontWeight: 500,
              transition: 'all 100ms ease',
              color: '#ffffff',
              padding: '8px 12px',

            },
          }}
        >
          Room B
        </Tabs.Tab>

 <FloatingIndicator
          target={value ? controlsRefs[value] : null}
          parent={rootRef}
          styles={{
            root: {
              color: 'red',
              backgroundColor: '#15AABF',
              borderRadius: 'var(--mantine-radius-md)',
              borderBottom: '2px solid #3799CE',
              boxShadow: 'var(--mantine-shadow-lg)',
            },
          }}
        />
      </div>
                 </div>






      </Tabs.List>

     <Tabs.Panel value="1">
        <Container
          fluid
          w={"100%"}
          className="rounded-b-lg bg-[--content-background]"
          >
      <div
          style={{
          width: "100%",
          padding: 10,
          overflowY: "auto",
          }}
          >
          <div>
          {activeVisits.length === 0  ? (
          <Text>Loading events...</Text>
          ) : (
          <>
          <div className="mb-2 flex justify-between">
        <Tooltip
          label="Nombre de patients"
          withArrow
          position="bottom"
          className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
        >
          <Group className="bg-[--content-background]">
            NP:
            <Badge
              size="sm"
              variant="filled"
              style={{
                pointerEvents: "none",
                padding: 0,
                width: rem(20),
                height: rem(20),
              }}
            >
              {/* {activeVisits.length}  */}
              {activeVisits.filter(visit => isResourceId(visit.resourceId, 1)).length}
            </Badge>
          </Group>
        </Tooltip>
        <Tooltip
          label="Résultats du diagnostic"
          withArrow
          className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
          position="bottom"
        >
          <Group className="bg-[--content-background]">
            RDD:
            <Badge
              size="sm"
              variant="filled"
              style={{
                pointerEvents: "none",
                padding: 0,
                width: rem(20),
                height: rem(20),
              }}
            >

      {activeVisits
        .filter(event =>
          isResourceId(event.resourceId, 1) &&
          event.eventType === "visitor-counter"
        ).length}

            </Badge>
          </Group>
        </Tooltip>
        <Group>
        <Menu shadow="md" width={200}>
          <Menu.Target>
            <Button

          leftSection={
            activeVisits
            .filter(event => isResourceId(event.resourceId, 1))
            .filter(event => activeIds.includes(Number(event.id)))
            .length
          }
              bg={"#3799CE"}
              classNames={classes}
              radius="md"
              rightSection={
                <IconArrowRight
                  style={{ width: rem(18) }}
                />
              }
            >
              NPD
            </Button>
          </Menu.Target>

          <Menu.Dropdown >
            <Tooltip
              label="Nombre de patients diagnostiqués"
              withArrow
              className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
              position="top"
            >
              <Menu.Label className="bg-[--content-background]" >
                Historique journalier
              </Menu.Label>
            </Tooltip>
            <div className={activeVisits.filter(visit => visit.resourceId === 1).length <= 5 ?"w-[193px] overflow-hidden" : "h-[120px] w-[193px] overflow-hidden"}>
            <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
             <div className="pr-3" >

   <Menu.Divider />
            {activeVisits.length > 0 ? (
              // activeVisits.map((event, index) => {
              activeVisits.filter(visit => visit.resourceId === 1).map((event, index) => {
                const counterId = dailyCounter + index;
                // Increment the counter for each event
                return (
                  <React.Fragment
                    key={`${event.id}-${counterId}`}
                  >
                    {activeIds.includes(Number(event.id)) && ( // Show only if the event is in active IDs
                      <Menu.Item
                      className={
                        event.id === eventIdCounter.toString() ? "hidden" : "py-1 px-0"
                      }
                        onClick={() => {

                          setActiveIds(
                            activeIds.filter(
                              (id) => id !== Number(event.id),
                            ),
                          );
                        }}
                      >
                          <Group pl={1}>
                         <Badge
                           size="sm"
                           variant="filled"
                           style={{
                             pointerEvents: "none",
                             padding: 0,
                             width: rem(20),
                             height: rem(20),
                             marginRight: "auto",
                           }}
                           bg={"green"}
                         >
                           {/* {event.id} */}
                           {/* {activeVisits.length}  */}
                           {index + 1}
                         </Badge>
                         <Text size="xs" c="green" style={{alignItems: "var(--group-align, center)"}}>
                         {event.first_name?.slice(0, 8) || 'N/A'}&nbsp;
                         {event.last_name?.slice(0, 8) || 'N/A'}
                         </Text>
                         <IconArrowBackUpDouble stroke={1.5} size={20} color="#3799CE" style={{ marginLeft: "auto"}}/>
                       </Group>
                      </Menu.Item>
                    )}
                  </React.Fragment>
                );
              })
            ) : (
              <Text>No results found</Text>
            )}
     <Menu.Divider />
            </div>
          </SimpleBar>
      </div>
          </Menu.Dropdown>
        </Menu>
   <Menu shadow="md" width={280} closeOnItemClick={false}>
      <Menu.Target>
        <Tooltip label="Filtrer">
          <Icon path={mdiFilterVariant} size={1} color="#3799ce" />
        </Tooltip>
      </Menu.Target>

      <Menu.Dropdown>
        <ScrollArea h={500} scrollbarSize={4} offsetScrollbars>
          {options.map((item) => (
            <Menu.Item
              key={item}
              onClick={() => toggleSelection(item)}
              leftSection={
                <div style={{ width: 20 }}>
                  {selected.includes(item) && (
                    <Icon path={mdiCheck} size={0.8} />
                  )}
                </div>
              }
              styles={{
                item: {
                  display: 'flex',
                  alignItems: 'center',
                },
              }}
            >
              {item}
            </Menu.Item>
          ))}
        </ScrollArea>
      </Menu.Dropdown>
    </Menu>
        </Group>
      </div>
    
  <Droppable droppableId="dnd-list" direction="vertical">
    {(provided) => (
      <div {...provided.droppableProps} ref={provided.innerRef}>
          {activeVisits.length > 0 ? (
         
          <Droppable droppableId="dnd-list" direction="vertical">
            {(provided) => (
              <div className={activeVisits.filter(visit => visit.resourceId === 1).length <=3 ?" max-w-[570px] " : "h-[180px] max-w-[570px] "}>
               <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
              <div {...provided.droppableProps} ref={provided.innerRef}
               className="pr-3 ">
          {activeVisits.length > 0 ? (
             activeVisits.filter(visit => visit.resourceId === 1).map((visit, index) => (
              <Draggable
                key={`visit-${visit.id}-${index}`}
                draggableId={(visit.id || `visit-${index}`).toString()}
                index={index}
              >
                {(provided) => (
                  <div
                    className={cx({
                      hidden: activeIds.includes(Number(visit.id)) || (visit.checkedListedattente ?? false),
                    })}
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                  >
                    <>
                      <Card
                        className={`my-3 ${classes.card} ${
                          visit.type === "visit"
                            ? "border-l-4 border-l-[#34D1BF]"
                            : visit.type === "visitor-counter"
                            ? "border-l-4 border-l-[#F17105]"
                            : visit.type === "completed"
                            ? "border-l-4 border-l-[#3799CE]"
                            : visit.type === "diagnosis"
                            ? "border-l-4 border-l-[#ED0423]"
                            : "text-[var(--mantine-color-dark-0)]"
                        }`}
                        shadow="sm"
                        radius="md"
                        style={{
                          cursor: "grab",
                          borderLeftWidth: "2px",
                        }}
                        mt={3}
                      >
                        <Card.Section p="xs">
                          <Flex align="center" justify="space-between">
                            <Group gap="8px">
                              <Badge size="sm" variant="filled" style={{ pointerEvents: "none", padding: 0, width: 20, height: 20 }}>
                                {index + 1} {/* Display counter starting from 1 */}
                              </Badge>

                              <Avatar variant="light" bg="#E9ECEF" radius="sm" h={22} color={visit.color}>
                              <span style={{ fontSize: "12px", fontWeight: "600" }}>{(visit.sociale || "").slice(0, 5)}</span>
                              </Avatar>
                              {/* Display current time or duration */}
                              {activeEvent_Ids.has(Number(visit.id)) && (
                                <Text size="xs" c="green" fw={600}>
                                  {currentTimes.get(Number(visit.id)) || new Date().toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}
                                </Text>
                              )}
                              {eventDurations.has(Number(visit.id)) && (
                                <Text size="xs" c="blue" fw={600}>
                                  {eventDurations.get(Number(visit.id))}min
                                </Text>
                              )}
                               <Text tt="capitalize">{visit.last_name?.slice(0, 6) || 'N/A'} &nbsp;{visit.first_name?.slice(0, 8) || 'N/A'} </Text>
                            </Group>
                            {/* Right Section */}
                            <Group >

                              <Group>
                                <Indicator
                                  disabled={!activeEvent_Ids.has(Number(visit.id))}
                                  processing
                                  inline
                                  size={12}
                                  offset={0}
                                  position="bottom-end"
                                  color="red"
                                  mt={2}
                                  withBorder
                                />
                                <Indicator
                                  disabled={activeEvent_Ids.has(Number(visit.id))}
                                  inline
                                  size={12}
                                  offset={0}
                                  position="bottom-end"
                                  color="#3799CE"
                                  mt={2}
                                  withBorder
                                />
                              </Group>
                                 <Switch
                                checked={activeEvent_Ids.has(Number(visit.id))}
                                onChange={(e) => {
                                  handleTimeTracking(Number(visit.id), e.currentTarget.checked);
                                }}
                                color="teal"
                                size="sm"
                                thumbIcon={
                                  activeEvent_Ids.has(Number(visit.id)) ? (
                                    <IconCheck size={12} color="var(--mantine-color-teal-6)" stroke={3} />
                                  ) : (
                                    <IconX size={12} color="var(--mantine-color-red-6)" stroke={3} />
                                  )
                                }
                              />

                            </Group>
                            <Group gap="2px">
                               <Avatar  bg="#ff5722" radius="sm" h={22} pt={1}>
                              <Text fw={600} c={"white"}>FLT</Text>
                              </Avatar>


                              <Icon path={mdiTooth} size={1} color={"#d50000"} />

                            <Menu
                              shadow="md"
                              position="left-start"
                              width={280}
                              transitionProps={{
                                transition: "rotate-right",
                                duration: 150,
                              }}
                            >
                              <Menu.Target>
                                <ActionIcon variant="subtle" aria-label="Menu options">
                                  <Icon path={mdiDotsVertical} size={1} color="#6b7280" />
                                </ActionIcon>
                              </Menu.Target>

                              <Menu.Dropdown>
                                {!activeIds.includes(Number(visit.id)) && ( // Show "Active" only if not in active IDs
                                  <Menu.Item
                                    leftSection={
                                      <IconShieldCheck stroke={2}
                                        className="mt-1"
                                        color="green"
                                      />
                                    }
                                    onClick={() => {
                                      setActiveIds([...activeIds, Number(visit.id)]);
                                      toggleEvent(Number(visit.id));
                                    }}
                                  >
                                    Active
                                  </Menu.Item>
                                )}

                                <Menu.Item
                                  leftSection={
                                   <Icon path={mdiFileAccount} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  onClick={() => openedFichepatient(visit)}
                                >
                                  Fiche patient
                                </Menu.Item>
                                 {/* <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCalendarPlus} size={0.65}  className="mt-1" color="#3799CE" />
                                  }

                                >
                                   Nouveau rendez-vous

                                </Menu.Item> */}
                                  <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAccountBoxEditOutline} size={0.65}  className="mt-1" color="#3799CE" />
                                  }
                                   onClick={() => {handleEditClick(visit)}}
                                >

                                  Modifier rendez-vous
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAccountAlert} size={0.65} className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Alerts du patient
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAlertPlus} size={0.65}  className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Ajouter une alerte
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCommentPlus} size={0.65} className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Ajouter un rappel
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiShare} size={0.65} className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Créer ordre
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiShare} size={0.65} className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Créer DICOM ordre
                                </Menu.Item>
                                 <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiPen} size={0.65} className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Changer la salle de consultation
                                </Menu.Item>
                                 <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiTooth} size={0.65} className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Démarrer / Afficher la visite
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiFileDocument} size={0.65} className="mt-1" color="#3799CE" />
                                  }

                                >
                                 Prescription
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiNeedle} size={0.65}className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Comptes rendus
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiPower} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  onClick={async (clickEvent) => {
                                    clickEvent.stopPropagation();

                                    console.log('🔄 Ending visit:', visit.title);

                                    try {
                                      // Import API dynamically
                                      const { appointmentStateAPI } = await import('@/services/api');

                                      // Update backend: complete appointment and move to history journal
                                      await appointmentStateAPI.completeAppointment(visit.id, 'Visit completed');
                                      console.log('✅ Backend updated: appointment completed and moved to history journal');

                                      // Remove from active visits
                                      setActiveVisits(prev => prev.filter(v => v.id !== visit.id));
                                      // Add to history journal
                                      setHistoriquejournalier(prev => [...prev, {
                                        ...visit,
                                        status: 'completed',
                                        eventType: 'completed'
                                      }]);

                                      notifications.show({
                                        title: 'Visite terminée',
                                        message: `La visite de ${visit.first_name} ${visit.last_name} a été terminée et ajoutée à l'historique`,
                                        color: 'green',
                                        autoClose: 3000
                                      });
                                    } catch (error) {
                                      console.error('Error ending visit:', error);
                                      notifications.show({
                                        title: 'Erreur',
                                        message: error instanceof Error ? error.message : 'Impossible de terminer la visite',
                                        color: 'red',
                                        autoClose: 5000
                                      });
                                    }
                                  }}
                                >
                                  End Visit
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCurrencyUsd} size={0.65}className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Réglement à l&apos;avance
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiArrowDown} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  onClick={() => moveToWaitingRoom(visit)}
                                >
                                 Revenir à la salle d&apos;attente
                                </Menu.Item>

                              <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <IconTrash stroke={2} size={14} className="mt-1" color="red" />
                                  }
                                  onClick={openedVisitesActivesDelete}
                                >
                                  Supprimer
                                </Menu.Item>
                              </Menu.Dropdown>
                            </Menu>
                            </Group>
                          </Flex>
                        </Card.Section>
                      </Card>
                    </>
                  </div>
                )}
              </Draggable>
            ))
          ) : (
            <Text>No results found</Text>
          )}
                {provided.placeholder}
              </div>
              </SimpleBar>
              </div>
            )}
          </Droppable>
       
        ) : (
          <Text>No results found</Text>
        )}
        {provided.placeholder}
      </div>
    )}
  </Droppable>
  

                      </>
                    )}
                  </div>
                  <Button
                    fullWidth
                    onClick={() =>
                      handleSlotSelect({
                        start: new Date(), // Use current date as an example
                        end: new Date(), // Or use the same start time if not needed

                      })
                    }
                    className="mt-4 HoverButton"
                    leftSection={<Icon path={mdiPlus} size={1} />}
                  >
                    Ajouter rendez-vous
                  </Button>
                </div>
       </Container>
         
        



        </Tabs.Panel>
        <Tabs.Panel value="2">
       
        
          <Container
          fluid
          w={"100%"}
          className="rounded-b-lg bg-[--content-background]"
          >
      <div
          style={{
          width: "100%",
          padding: 10,
          overflowY: "auto",
          }}
          >
          <div>
          {waitingRoomVisits.length === 0 ? (
          <Text c="dimmed" ta="center" mt="xl">Aucune visite en salle de présence</Text>
          ) : (
          <>
          <div className="mb-2 flex justify-end">
            <Group>
            <Menu shadow="md" width={200}>
              <Menu.Target>
                <Button

              leftSection={
                waitingRoomVisits.length
              }
                  bg={"#3799CE"}
                  classNames={classes}
                  radius="md"
                  rightSection={
                    <IconArrowRight
                      style={{ width: rem(18) }}
                    />
                  }
                >
                  NPD
                </Button>
              </Menu.Target>

              <Menu.Dropdown>
                <Tooltip
                  label="Nombre de patients diagnostiqués"
                  withArrow
                  className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                  position="top"
                >
                  <Menu.Label className="bg-[--content-background]">
                    diagnostiqués 2
                  </Menu.Label>
                </Tooltip>
                <div className={activeVisits.filter(visit => visit.resourceId === 1).length <=3 ?"w-[193px] overflow-hidden" : "h-[120px] w-[193px] overflow-hidden"}>

                <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
              <div className="pr-3">
            {/* Render the list of events */}
   <Menu.Divider />
            {activeVisits.length > 0 ? (
              activeVisits.filter(visit => visit.resourceId === 1).map((event, index) => {
                const counterId = dailyCounter + index;
                // Increment the counter for each event
                return (
                  <React.Fragment
                    key={`${event.id}-${counterId}`}
                  >
                    {activeIds.includes(Number(event.id)) && ( // Show only if the event is in active IDs
                      <Menu.Item
                      className={
                        event.id === eventIdCounter.toString() ? "hidden" : "py-1 px-0"
                      }
                        // onClick={() => {
                        //   setActiveIds(
                        //     activeIds.filter(
                        //       (id) => id !== Number(event.id),
                        //     ),
                        //   );
                        // }}
                      >

                          <Group pl={1}>
                         <Badge
                           size="sm"
                           variant="filled"
                           style={{
                             pointerEvents: "none",
                             padding: 0,
                             width: rem(20),
                             height: rem(20),
                             marginRight: "auto",
                           }}

                           bg={"#3799CE"}
                         >
                           {/* {event.id}  */}
                            {/* {activeVisits.length}  */}
                            {index + 1}
                         </Badge>
                         <Text size="xs" c="#3799CE" style={{alignItems: "var(--group-align, center)"}}>
                         {event.first_name?.slice(0, 8) || 'N/A'}&nbsp;
                         {event.last_name?.slice(0, 8) || 'N/A'}
                         </Text>

                         <IconWallpaper stroke={1.25} size={20} color="#3799CE" style={{ marginLeft: "auto"}}/>
                       </Group>
                      </Menu.Item>
                    )}
                  </React.Fragment>
                );
              })
            ) : (
              <Text>No results found</Text>
            )}
            <Menu.Divider />
            </div>
            </SimpleBar>
            </div>
              </Menu.Dropdown>
            </Menu>
            <Menu shadow="md" width={280} closeOnItemClick={false}>
      <Menu.Target>
        <Tooltip label="Filtrer">
          <Icon path={mdiFilterVariant} size={1} color="#3799ce" />
        </Tooltip>
      </Menu.Target>

      <Menu.Dropdown>
        <ScrollArea h={500} scrollbarSize={4} offsetScrollbars>
          {options.map((item) => (
            <Menu.Item
              key={item}
              onClick={() => toggleSelection(item)}
              leftSection={
                <div style={{ width: 20 }}>
                  {selected.includes(item) && (
                    <Icon path={mdiCheck} size={0.8} />
                  )}
                </div>
              }
              styles={{
                item: {
                  display: 'flex',
                  alignItems: 'center',
                },
              }}
            >
              {item}
            </Menu.Item>
          ))}
        </ScrollArea>
      </Menu.Dropdown>
           </Menu>
           </Group>
          </div>

    
  <Droppable droppableId="dnd-list" direction="vertical">
    {(provided) => (
      <div {...provided.droppableProps} ref={provided.innerRef}>
          {waitingRoomVisits.length > 0 ? (
            <div className="space-y-2 max-w-[570px]">
              {waitingRoomVisits.map((visit, index) => (
              <Draggable key={visit.id || `visit-${index}`} draggableId={(visit.id || `visit-${index}`).toString()} index={index}>
                {(provided) => (
                  <div
                    className={cx({
                      hidden: activeIds.includes(Number(visit.id)) || (visit.checkedListedattente ?? false),
                    })}
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                  >
                    <>
 {/* <div style={{ width: "100%", padding: 10, minHeight: "200px" }}>
               {waitingRoomVisits.length === 0 ? (
                 <Text c="dimmed" ta="center" mt="xl">
                   Aucune visite en salle de présence
                 </Text>
               ) : (
                 <div className="space-y-2">
                   {waitingRoomVisits.map((visit) => ( */}
                      <Card  key={visit.id}
                    className={`my-3 ${classes.card} ${

                      visit.type === "visit"
                        ? "border-l-4 border-l-[#34D1BF]"
                        : visit.type === "visitor-counter"
                          ? "border-l-4 border-l-[#F17105]"
                          : visit.type === "completed"
                            ? "border-l-4 border-l-[#3799CE]"
                            : visit.type === "diagnosis"
                              ? "border-l-4 border-l-[#ED0423]"
                              : "text-[var(--mantine-color-dark-0)]"
                    }`}
                    shadow="sm"
                    radius="md"
                    style={{
                      cursor: "grab",
                      borderLeftWidth: "2px",
                    }}
                    mt={3}
                  >
                    <Card.Section p="sm">
                      <Group gap="md" justify="space-between">
                        <Text size="xs"  component="span">
                          <Group gap="5px">
                          <Badge
                            size="sm"
                            variant="filled"
                            style={{
                              pointerEvents: "none",
                              padding: 0,
                              width: rem(20),
                              height: rem(20),
                            }}
                          >
                            {/* {event.id}  */}
                            {/* {counterId} */}
                            {index + 1}

                          </Badge>
                            <Avatar variant="light" bg="#E9ECEF" radius="sm" h={22} color={visit.color}>
                              <span style={{ fontSize: "12px", fontWeight: "600" }}>{(visit.sociale || "").slice(0, 5)}</span>
                              </Avatar>
                            <Text  size="xs" fw={700} >
                            {visit.last_name?.slice(0, 8) || 'N/A'}&nbsp;
                            {visit.first_name?.slice(0, 8) || 'N/A'}

                            </Text>
                            {/* <Text size="sm" c="dimmed">{visit.typeConsultation}</Text> */}
                          </Group>
                        </Text>

                          <Group gap="5px">
                             <Icon path={mdiAlarm} size={1}  color="#868e96"/>
                              <Group gap="4px" color="#868e96" >
                              <Text c="dimmed" size="xs">{formatTimeFromDate(visit.start)}</Text>
                              <svg stroke="currentColor" fill="#3799ce" strokeWidth="0" viewBox="0 0 24 24" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M4 6h2v12H4zm10 5H8v2h6v5l6-6-6-6z"></path></svg>
                              <Text c="dimmed" size="xs">{formatTimeFromDate(visit.end)}</Text>
                              </Group>
                          </Group>
                          <Group gap="5px">
                            <Icon path={mdiPhone} size={1}  color="#868e96" />
                            <Text c="dimmed" size="sm" >
                              {visit.phone_numbers}
                            </Text>
                          </Group>
                          <Group gap={"4px"}>
                            <Avatar  bg="#ff5722" radius="sm" h={22} pt={1}>
                              <Text fw={600} c={"white"}>FLT</Text>
                              </Avatar>
                           <Avatar bg="#3799ce" radius="sm" h={22} pt={1}>
                             <Text fw={600} c={"white"}>SLT</Text>
                           </Avatar>
                              <Icon path={mdiAccountArrowRight} size={1} color={"#3799ce"}/>
                              <span onClick={() => moveToActiveVisits(visit)}>

                              <Icon
                                path={mdiArrowUp}
                                size={1}
                                color={"#3799ce"}
                                style={{ cursor: 'pointer' }}

                              />
                              </span>
                               <Menu
                               withArrow position="left"

                              shadow="md"

                              width={280}
                              transitionProps={{
                                transition: "rotate-right",
                                duration: 150,
                              }}
                            >
                              <Menu.Target>
                                <ActionIcon variant="subtle" aria-label="Menu options">
                                  <Icon path={mdiDotsVertical} size={1} color="#6b7280" />
                                </ActionIcon>
                              </Menu.Target>

                              <Menu.Dropdown>
                                {!activeIds.includes(Number(visit.id)) && ( // Show "Active" only if not in active IDs
                                  <Menu.Item
                                    leftSection={
                                      <IconShieldCheck stroke={2}
                                        className="mt-1"
                                        color="green"
                                      />
                                    }
                                    onClick={() => {
                                      setActiveIds([...activeIds, Number(visit.id)]);
                                      toggleEvent(Number(visit.id));
                                    }}
                                  >
                                    Active
                                  </Menu.Item>
                                )}

                                <Menu.Item
                                  leftSection={
                                   <Icon path={mdiPencilBoxOutline}  size={0.65} className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Modifier l&apos;entrée
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiReply} size={0.65}  className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Annuler l&apos;entrer
                                </Menu.Item>
                                <Menu.Divider />
                                <Menu.Item
                                  leftSection={
                                    <Icon path={mdiFileDocument} size={0.65}  className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Fiche patient
                                </Menu.Item>
                                <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCalendarPlus} size={0.65}  className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Nouveau rendez-vous
                                </Menu.Item>
                                <Menu.Item
                                  leftSection={
                                    <Icon path={mdiTooth} size={0.65}  className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Afficher la dernière visite
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiFileDocumentCheck} size={0.65}  className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Ajouter résultat biologique
                                </Menu.Item>
                                 <Menu.Divider />

                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAccountAlert} size={0.65} className="mt-1" color="#3799CE" />
                                  }

                                >

                                  Alerts du patient
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAlertPlus} size={0.65}  className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Ajouter une alerte
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCommentPlus} size={0.65} className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Ajouter un rappel
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiShare} size={0.65} className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Créer ordre
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiShare} size={0.65} className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Créer DICOM ordre
                                </Menu.Item>
                                 <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiArrowDown} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  onClick={() => moveToWaitingRoom(visit)}
                                >
                                  Accés chez le médecin
                                </Menu.Item>
                                 <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCurrencyUsd} size={0.65} className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Réglement à l&apos;avance
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCurrencyUsd} size={0.65} className="mt-1" color="#3799CE" />
                                  }

                                >
                                 Régler un plan de traitement
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCurrencyUsd} size={0.65}className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Régler un plan de soins
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCurrencyUsd} size={0.65} className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Recettes
                                </Menu.Item>
                                  <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCashMultiple} size={0.65} className="mt-1" color="#3799CE" />
                                  }

                                >
                                  État financier
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCurrencyUsd} size={0.65}className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Réglement à l&apos;avance
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiArrowLeft} size={0.65} className="mt-1" color="#3799CE" />
                                  }

                                >
                                 Sortie du patient
                                </Menu.Item>

                              <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <IconTrash stroke={2} size={14} className="mt-1" color="red" />
                                  }
                                  onClick={openedVisitesActivesDelete}
                                >
                                  Supprimer
                                </Menu.Item>
                              </Menu.Dropdown>
                            </Menu>
                            </Group>
                          {/* <span onClick={openedVisitesActivesDelete} className="cursor-pointer">
                          <Icon path={mdiDeleteClockOutline} size={1}    className="Icon_Delete cursor-pointer" />
                         </span> */}


                      </Group>
                    </Card.Section>
                  </Card>
                   {/* ))}
                 </div>
               )}
             </div> */}


                  <Modal opened={VisitesActivesDeleteOpened} onClose={closeVisitesActivesDelete} withCloseButton={false}>
              <Alert variant="light" color="red" title="Avertissement" icon={icon}>
                  Êtes-vous sûr de vouloir supprimer l&apos;événement ?

                </Alert>
                <Group justify="space-between" mt="md" mb="xs">
                <Button
                variant="filled"
                size="xs"
                radius="lg"
                color="red"
                onClick={() => {
                  // Use the visitToDelete state
                  if (visitToDelete) {
                    const eventId = Number(visitToDelete.id);
                    if (!isNaN(eventId)) {
                      deleteVisits(eventId);
                      closeVisitesActivesDelete();
                      setVisitToDelete(null);
                    } else {
                      console.error("Invalid event ID:", visitToDelete.id);
                      notifications.show({
                        title: 'Error',
                        message: 'Could not delete event: Invalid ID',
                        color: 'red',
                      });
                    }
                  }
                }}
              >
                Oui
              </Button>
                  <Button
                    variant="filled"
                    size="xs"
                    radius="lg"
                    color="lime.4"
                    onClick={closeVisitesActivesDelete}
                  >
                    Non
                  </Button>
                </Group>
            </Modal>
                    </>
                  </div>
                )}
              </Draggable>
              ))}
            </div>
          ) : (
            <Text c="dimmed" ta="center" mt="xl">Aucune visite en salle de présence</Text>
          )}
        </div>
      )}
    </Droppable>

                      </>
                    )}
                  </div>
                  <Button
                    fullWidth
                    onClick={() =>
                      handleSlotSelect({
                        start: new Date(), // Use current date as an example
                        end: new Date(), // Or use the same start time if not needed

                      })
                    }
                    className="mt-4 HoverButton"
                    leftSection={<Icon path={mdiPlus} size={1} />}
                  >
                   Ajouter rendez-vous
                  </Button>
                </div>
           </Container>



        </Tabs.Panel>

    </Tabs>
    {/* Salle de présence    */}
    <div>
   <div className="rounded-t-lg bg-[#3799CE] p-3 text-[#ffffff] " >
     <Group justify="space-between">
     <span className="text-sm font-medium ">
          <span className="flex gap-2 text-sm font-medium ">
         <svg
          xmlns="http://www.w3.org/2000/svg"
          width="2em"
          height="1.5em"
          viewBox="0 0 20 20"
          >
          <path
          fill="none"
          stroke="currentColor"
          d="M18 3v2h2M1 19.5h7M7.5 14V6.5H6.328a3 3 0 0 0-2.906 2.255L1.5 16.25v.25h9V18c0 1.5 0 2.5.75 4c0 0 .75 1.5 1.75 1.5m5-14a4.5 4.5 0 1 1 0-9a4.5 4.5 0 0 1 0 9Zm-10.65-5s-1.6-1-1.6-2.25a1.747 1.747 0 1 1 3.496 0C9.246 3.5 7.65 4.5 7.65 4.5z"
          ></path>
          </svg>
              <Text size="md"> Salle de présence</Text>

          </span>

          </span>
           <Group justify="flex-end">
               <Badge size="lg" circle radius="sm" color="red"> 0 </Badge>
                 <Badge size="lg" circle radius="sm" color="green"> 0 </Badge>
      </Group>
      </Group>


    </div>
  <Container
          fluid
          w={"100%"}
          className="rounded-b-lg bg-[--content-background]"
          >
      <div
          style={{
          width: "100%",
          padding: 10,
          overflowY: "auto",
          }}
          >
          <div>
          {waitingRoomVisits.length === 0 ? (
          <Text c="dimmed" ta="center" mt="xl">Aucune visite en salle de présence</Text>
          ) : (
          <>
          <div className="mb-2 flex justify-end">
            <Group>
            <Menu shadow="md" width={200}>
              <Menu.Target>
                <Button

              leftSection={
                waitingRoomVisits.length
              }
                  bg={"#3799CE"}
                  classNames={classes}
                  radius="md"
                  rightSection={
                    <IconArrowRight
                      style={{ width: rem(18) }}
                    />
                  }
                >
                  NPD
                </Button>
              </Menu.Target>

              <Menu.Dropdown>
                <Tooltip
                  label="Nombre de patients diagnostiqués"
                  withArrow
                  className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
                  position="top"
                >
                  <Menu.Label className="bg-[--content-background]">
                    Historique journalier
                  </Menu.Label>
                </Tooltip>
                <div className={activeVisits.filter(visit => visit.resourceId === 1).length <=3 ?"w-[193px] overflow-hidden" : "h-[120px] w-[193px] overflow-hidden"}>

                <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
              <div className="pr-3">
            {/* Render the list of events */}
   <Menu.Divider />
            {activeVisits.length > 0 ? (
              activeVisits.filter(visit => visit.resourceId === 1).map((event, index) => {
                const counterId = dailyCounter + index;
                // Increment the counter for each event
                return (
                  <React.Fragment
                    key={`${event.id}-${counterId}`}
                  >
                    {activeIds.includes(Number(event.id)) && ( // Show only if the event is in active IDs
                      <Menu.Item
                      className={
                        event.id === eventIdCounter.toString() ? "hidden" : "py-1 px-0"
                      }
                        // onClick={() => {
                        //   setActiveIds(
                        //     activeIds.filter(
                        //       (id) => id !== Number(event.id),
                        //     ),
                        //   );
                        // }}
                      >

                          <Group pl={1}>
                         <Badge
                           size="sm"
                           variant="filled"
                           style={{
                             pointerEvents: "none",
                             padding: 0,
                             width: rem(20),
                             height: rem(20),
                             marginRight: "auto",
                           }}

                           bg={"#3799CE"}
                         >
                           {/* {event.id}  */}
                            {/* {activeVisits.length}  */}
                            {index + 1}
                         </Badge>
                         <Text size="xs" c="#3799CE" style={{alignItems: "var(--group-align, center)"}}>
                         {event.first_name?.slice(0, 8) || 'N/A'}&nbsp;
                         {event.last_name?.slice(0, 8) || 'N/A'}
                         </Text>

                         <IconWallpaper stroke={1.25} size={20} color="#3799CE" style={{ marginLeft: "auto"}}/>
                       </Group>
                      </Menu.Item>
                    )}
                  </React.Fragment>
                );
              })
            ) : (
              <Text>No results found</Text>
            )}
            <Menu.Divider />
            </div>
            </SimpleBar>
            </div>
              </Menu.Dropdown>
            </Menu>
            <Menu shadow="md" width={280} closeOnItemClick={false}>
      <Menu.Target>
        <Tooltip label="Filtrer">
          <Icon path={mdiFilterVariant} size={1} color="#3799ce" />
        </Tooltip>
      </Menu.Target>

      <Menu.Dropdown>
        <ScrollArea h={500} scrollbarSize={4} offsetScrollbars>
          {options.map((item) => (
            <Menu.Item
              key={item}
              onClick={() => toggleSelection(item)}
              leftSection={
                <div style={{ width: 20 }}>
                  {selected.includes(item) && (
                    <Icon path={mdiCheck} size={0.8} />
                  )}
                </div>
              }
              styles={{
                item: {
                  display: 'flex',
                  alignItems: 'center',
                },
              }}
            >
              {item}
            </Menu.Item>
          ))}
        </ScrollArea>
      </Menu.Dropdown>
           </Menu>
           </Group>
          </div>

    
  <Droppable droppableId="dnd-list" direction="vertical">
    {(provided) => (
      <div {...provided.droppableProps} ref={provided.innerRef}>
          {waitingRoomVisits.length > 0 ? (
            <div className="space-y-2 max-w-[570px]">
              {waitingRoomVisits.map((visit, index) => (
              <Draggable key={visit.id || `visit-${index}`} draggableId={(visit.id || `visit-${index}`).toString()} index={index}>
                {(provided) => (
                  <div
                    className={cx({
                      hidden: activeIds.includes(Number(visit.id)) || (visit.checkedListedattente ?? false),
                    })}
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                  >
                    <>

                      <Card  key={visit.id}
                    className={`my-3 ${classes.card} ${

                      visit.type === "visit"
                        ? "border-l-4 border-l-[#34D1BF]"
                        : visit.type === "visitor-counter"
                          ? "border-l-4 border-l-[#F17105]"
                          : visit.type === "completed"
                            ? "border-l-4 border-l-[#3799CE]"
                            : visit.type === "diagnosis"
                              ? "border-l-4 border-l-[#ED0423]"
                              : "text-[var(--mantine-color-dark-0)]"
                    }`}
                    shadow="sm"
                    radius="md"
                    style={{
                      cursor: "grab",
                      borderLeftWidth: "2px",
                    }}
                    mt={3}
                  >
                    <Card.Section p="sm">
                      <Group gap="md" justify="space-between">
                        <Text size="xs"  component="span">
                          <Group gap="5px">
                          <Badge
                            size="sm"
                            variant="filled"
                            style={{
                              pointerEvents: "none",
                              padding: 0,
                              width: rem(20),
                              height: rem(20),
                            }}
                          >
                            {/* {event.id}  */}
                            {/* {counterId} */}
                            {index + 1}

                          </Badge>
                            <Avatar variant="light" bg="#E9ECEF" radius="sm" h={22} color={visit.color}>
                              <span style={{ fontSize: "12px", fontWeight: "600" }}>{(visit.sociale || "").slice(0, 5)}</span>
                              </Avatar>
                            <Text  size="xs" fw={700} >
                            {visit.last_name?.slice(0, 8) || 'N/A'}&nbsp;
                            {visit.first_name?.slice(0, 8) || 'N/A'}

                            </Text>
                            {/* <Text size="sm" c="dimmed">{visit.typeConsultation}</Text> */}
                          </Group>
                        </Text>

                          <Group gap="5px">
                             <IconAlarm stroke={2}  size={12} strokeWidth={1.5} color="#868e96"/>
                              <Group gap="4px" color="#868e96" >
                              <Text c="dimmed" size="xs">{formatTimeFromDate(visit.start)}</Text>
                              <svg stroke="currentColor" fill="#3799ce" strokeWidth="0" viewBox="0 0 24 24" height="16" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M4 6h2v12H4zm10 5H8v2h6v5l6-6-6-6z"></path></svg>
                              <Text c="dimmed" size="xs">{formatTimeFromDate(visit.end)}</Text>
                              </Group>
                          </Group>
                          <Group gap="5px">
                            <IconPhoneCall stroke={1.5} size={12} strokeWidth={1.5} color="#868e96" />
                            <Text c="dimmed" size="sm" >
                              {visit.phone_numbers}
                            </Text>
                          </Group>
                          <Group gap={"4px"}>
                            <Avatar  bg="#ff5722" radius="sm" h={22} pt={1}>
                              <Text fw={600} c={"white"}>FLT</Text>
                              </Avatar>
                           <Avatar bg="#3799ce" radius="sm" h={22} pt={1}>
                             <Text fw={600} c={"white"}>SLT</Text>
                           </Avatar>
                              <Icon path={mdiAccountArrowRight} size={1} color={"#3799ce"}/>
                              <span onClick={() => moveToActiveVisits(visit)}>

                              <Icon
                                path={mdiArrowUp}
                                size={1}
                                color={"#3799ce"}
                                style={{ cursor: 'pointer' }}

                              />
                              </span>
                               <Menu
                               withArrow position="left"

                              shadow="md"

                              width={280}
                              transitionProps={{
                                transition: "rotate-right",
                                duration: 150,
                              }}
                            >
                              <Menu.Target>
                                <ActionIcon variant="subtle" aria-label="Menu options">
                                  <Icon path={mdiDotsVertical} size={1} color="#6b7280" />
                                </ActionIcon>
                              </Menu.Target>

                              <Menu.Dropdown>
                                {!activeIds.includes(Number(visit.id)) && ( // Show "Active" only if not in active IDs
                                  <Menu.Item
                                    leftSection={
                                      <IconShieldCheck stroke={2}
                                        className="mt-1"
                                        color="green"
                                      />
                                    }
                                    onClick={() => {
                                      setActiveIds([...activeIds, Number(visit.id)]);
                                      toggleEvent(Number(visit.id));
                                    }}
                                  >
                                    Active
                                  </Menu.Item>
                                )}

                                <Menu.Item
                                  leftSection={
                                   <Icon path={mdiPencilBoxOutline}  size={0.65} className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Modifier l&apos;entrée
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiReply} size={0.65}  className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Annuler l&apos;entrer
                                </Menu.Item>
                                <Menu.Divider />
                                <Menu.Item
                                  leftSection={
                                    <Icon path={mdiFileDocument} size={0.65}  className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Fiche patient
                                </Menu.Item>
                                <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCalendarPlus} size={0.65}  className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Nouveau rendez-vous
                                </Menu.Item>
                                <Menu.Item
                                  leftSection={
                                    <Icon path={mdiTooth} size={0.65}  className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Afficher la dernière visite
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiFileDocumentCheck} size={0.65}  className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Ajouter résultat biologique
                                </Menu.Item>
                                 <Menu.Divider />

                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAccountAlert} size={0.65} className="mt-1" color="#3799CE" />
                                  }

                                >

                                  Alerts du patient
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiAlertPlus} size={0.65}  className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Ajouter une alerte
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCommentPlus} size={0.65} className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Ajouter un rappel
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiShare} size={0.65} className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Créer ordre
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiShare} size={0.65} className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Créer DICOM ordre
                                </Menu.Item>
                                 <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiArrowDown} size={0.65} className="mt-1" color="#3799CE" />
                                  }
                                  onClick={() => moveToWaitingRoom(visit)}
                                >
                                  Accés chez le médecin
                                </Menu.Item>
                                 <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCurrencyUsd} size={0.65} className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Réglement à l&apos;avance
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCurrencyUsd} size={0.65} className="mt-1" color="#3799CE" />
                                  }

                                >
                                 Régler un plan de traitement
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCurrencyUsd} size={0.65}className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Régler un plan de soins
                                </Menu.Item>
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCurrencyUsd} size={0.65} className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Recettes
                                </Menu.Item>
                                  <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCashMultiple} size={0.65} className="mt-1" color="#3799CE" />
                                  }

                                >
                                  État financier
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiCurrencyUsd} size={0.65}className="mt-1" color="#3799CE" />
                                  }

                                >
                                  Réglement à l&apos;avance
                                </Menu.Item>
                                <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <Icon path={mdiArrowLeft} size={0.65} className="mt-1" color="#3799CE" />
                                  }

                                >
                                 Sortie du patient
                                </Menu.Item>

                              <Menu.Divider />
                                 <Menu.Item
                                  leftSection={
                                    <IconTrash stroke={2} size={14} className="mt-1" color="red" />
                                  }
                                  onClick={openedVisitesActivesDelete}
                                >
                                  Supprimer
                                </Menu.Item>
                              </Menu.Dropdown>
                            </Menu>
                            </Group>
                          {/* <span onClick={openedVisitesActivesDelete} className="cursor-pointer">
                          <Icon path={mdiDeleteClockOutline} size={1}    className="Icon_Delete cursor-pointer" />
                         </span> */}


                      </Group>
                    </Card.Section>
                  </Card>
                   {/* ))}
                 </div>
               )}
             </div> */}


                  <Modal opened={VisitesActivesDeleteOpened} onClose={closeVisitesActivesDelete} withCloseButton={false}>
              <Alert variant="light" color="red" title="Avertissement" icon={icon}>
                  Êtes-vous sûr de vouloir supprimer l&apos;événement ?

                </Alert>
                <Group justify="space-between" mt="md" mb="xs">
                <Button
                variant="filled"
                size="xs"
                radius="lg"
                color="red"
                onClick={() => {
                  // Use the event's actual ID, not counterId
                  const eventId = Number(visit.id);
                  if (!isNaN(eventId)) {
                    deleteVisits(eventId);
                    closeVisitesActivesDelete();
                  } else {
                    console.error("Invalid event ID:", visit.id);
                    notifications.show({
                      title: 'Error',
                      message: 'Could not delete event: Invalid ID',
                      color: 'red',
                    });
                  }
                }}
              >
                Oui
              </Button>
                  <Button
                    variant="filled"
                    size="xs"
                    radius="lg"
                    color="lime.4"
                    onClick={closeVisitesActivesDelete}
                  >
                    Non
                  </Button>
                </Group>
            </Modal>
                    </>
                  </div>
                )}
              </Draggable>
              ))}
            </div>
          ) : (
            <Text c="dimmed" ta="center" mt="xl">Aucune visites actives</Text>
          )}
        </div>
      )}
    </Droppable>
  
                      </>
                    )}
                  </div>
                  <Button
                    fullWidth
                    onClick={() =>
                      handleSlotSelect({
                        start: new Date(), // Use current date as an example
                        end: new Date(), // Or use the same start time if not needed

                      })
                    }
                    className="mt-4 HoverButton"
                    leftSection={<Icon path={mdiPlus} size={1} />}
                  >
                   Ajouter rendez-vous
                  </Button>
                </div>
           </Container>
                    </div>
  {/* Historique journalier */}
    <div>
   <div className="rounded-t-lg bg-[#3799CE] p-3 text-[#ffffff] " >
     <Group justify="space-between">
     <span className="text-sm font-medium capitalize">
          <span className="flex gap-2 text-sm font-medium capitalize">
          <Icon path={mdiLogout} size={1} />

 <Text size="md"> Historique journalier</Text>
          </span>

          </span>
           <Group justify="flex-end">
               <Badge size="lg" circle radius="sm" color="red"
                      onClick={(event) => {
                        event.preventDefault();
                        toggleSidebarAlert(); // Toggle sidebar visibility
                      }}
                      className=" cursor-pointer"
                    
               > 0 </Badge>
                 <Badge size="lg" circle radius="sm" color="indigo"> 0 </Badge>
      </Group>
      </Group>
    </div>
  <Container
          fluid
          w={"100%"}
          className="rounded-b-lg bg-[--content-background]"
          >
      <div
          style={{
          width: "100%",
          padding: 10,
          overflowY: "auto",
          }}
          >
          <div>
          {activeVisits.length === 0  ? (
          <Text>Loading events...</Text>
          ) : (
          <>
          <div className="mb-2 flex justify-between">
        <Tooltip
          label="Nombre de patients"
          withArrow
          position="bottom"
          className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
        >
          <Group className="bg-[--content-background]">
            NP:
            <Badge
              size="sm"
              variant="filled"
              style={{
                pointerEvents: "none",
                padding: 0,
                width: rem(20),
                height: rem(20),
              }}
            >
              {/* {activeVisits.length}  */}
              {activeVisits.filter(visit => isResourceId(visit.resourceId, 1)).length}
            </Badge>
          </Group>
        </Tooltip>
        <Tooltip
          label="Résultats du diagnostic"
          withArrow
          className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
          position="bottom"
        >
          <Group className="bg-[--content-background]">
            RDD:
            <Badge
              size="sm"
              variant="filled"
              style={{
                pointerEvents: "none",
                padding: 0,
                width: rem(20),
                height: rem(20),
              }}
            >

      {activeVisits
        .filter(event =>
          isResourceId(event.resourceId, 1) &&
          event.eventType === "visitor-counter"
        ).length}

            </Badge>
          </Group>
        </Tooltip>
        <Group>
        <Menu shadow="md" width={200}>
          <Menu.Target>
            <Button

          leftSection={
            activeVisits
            .filter(event => isResourceId(event.resourceId, 1))
            .filter(event => activeIds.includes(Number(event.id)))
            .length
          }
              bg={"#3799CE"}
              classNames={classes}
              radius="md"
              rightSection={
                <IconArrowRight
                  style={{ width: rem(18) }}
                />
              }
            >
              NPD
            </Button>
          </Menu.Target>

          <Menu.Dropdown >
            <Tooltip
              label="Nombre de patients diagnostiqués"
              withArrow
              className="bg-[var(--tooltip-bg)] text-[var(--text-daisy)]"
              position="top"
            >
              <Menu.Label className="bg-[--content-background]" >
                Historique journalier
              </Menu.Label>
            </Tooltip>
            <div className={activeVisits.filter(visit => visit.resourceId === 1).length <= 5 ?"w-[193px] overflow-hidden" : "h-[120px] w-[193px] overflow-hidden"}>
            <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
             <div className="pr-3" >

   <Menu.Divider />
            {activeVisits.length > 0 ? (
              // activeVisits.map((event, index) => {
              activeVisits.filter(visit => visit.resourceId === 1).map((event, index) => {
                const counterId = dailyCounter + index;
                // Increment the counter for each event
                return (
                  <React.Fragment
                    key={`${event.id}-${counterId}`}
                  >
                    {activeIds.includes(Number(event.id)) && ( // Show only if the event is in active IDs
                      <Menu.Item
                      className={
                        event.id === eventIdCounter.toString() ? "hidden" : "py-1 px-0"
                      }
                        onClick={() => {

                          setActiveIds(
                            activeIds.filter(
                              (id) => id !== Number(event.id),
                            ),
                          );
                        }}
                      >
                          <Group pl={1}>
                         <Badge
                           size="sm"
                           variant="filled"
                           style={{
                             pointerEvents: "none",
                             padding: 0,
                             width: rem(20),
                             height: rem(20),
                             marginRight: "auto",
                           }}
                           bg={"green"}
                         >
                           {/* {event.id} */}
                           {/* {activeVisits.length}  */}
                           {index + 1}
                         </Badge>
                         <Text size="xs" c="green" style={{alignItems: "var(--group-align, center)"}}>
                         {event.first_name?.slice(0, 8) || 'N/A'}&nbsp;
                         {event.last_name?.slice(0, 8) || 'N/A'}
                         </Text>
                         <IconArrowBackUpDouble stroke={1.5} size={20} color="#3799CE" style={{ marginLeft: "auto"}}/>
                       </Group>
                      </Menu.Item>
                    )}
                  </React.Fragment>
                );
              })
            ) : (
              <Text>No results found</Text>
            )}
     <Menu.Divider />
            </div>
          </SimpleBar>
      </div>
          </Menu.Dropdown>
        </Menu>
   <Menu shadow="md" width={280} closeOnItemClick={false}>
      <Menu.Target>
        <Tooltip label="Filtrer">
          <Icon path={mdiFilterVariant} size={1} color="#3799ce" />
        </Tooltip>
      </Menu.Target>

      <Menu.Dropdown>
        <ScrollArea h={500} scrollbarSize={4} offsetScrollbars>
          {options.map((item) => (
            <Menu.Item
              key={item}
              onClick={() => toggleSelection(item)}
              leftSection={
                <div style={{ width: 20 }}>
                  {selected.includes(item) && (
                    <Icon path={mdiCheck} size={0.8} />
                  )}
                </div>
              }
              styles={{
                item: {
                  display: 'flex',
                  alignItems: 'center',
                },
              }}
            >
              {item}
            </Menu.Item>
          ))}
        </ScrollArea>
      </Menu.Dropdown>
    </Menu>
        </Group>
      </div>
    
  <Droppable droppableId="dnd-list" direction="vertical">
    {(provided) => (
      <div {...provided.droppableProps} ref={provided.innerRef}>
          {historiquejournalier.length > 0 ? (
         
          <Droppable droppableId="dnd-list" direction="vertical">
            {(provided) => (
              <div className={historiquejournalier.filter(visit => visit.resourceId === 1).length <=3 ?" max-w-[570px] " : "h-[180px] max-w-[570px] "}>
               <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
              <div {...provided.droppableProps} ref={provided.innerRef}
               className="pr-3 ">
          {historiquejournalier.length > 0 ? (
             historiquejournalier.filter(visit => visit.resourceId === 1).map((visit, index) => (
              <Draggable
                key={`visit-${visit.id}-${index}`}
                draggableId={(visit.id || `visit-${index}`).toString()}
                index={index}
              >
                {(provided) => (
                  <div
                    className={cx({
                      hidden: activeIds.includes(Number(visit.id)) || (visit.checkedListedattente ?? false),
                    })}
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                  >
                    <>
                      <Card
                        className={`my-3 ${classes.card} ${
                          visit.type === "visit"
                            ? "border-l-4 border-l-[#34D1BF]"
                            : visit.type === "visitor-counter"
                            ? "border-l-4 border-l-[#F17105]"
                            : visit.type === "completed"
                            ? "border-l-4 border-l-[#3799CE]"
                            : visit.type === "diagnosis"
                            ? "border-l-4 border-l-[#ED0423]"
                            : "text-[var(--mantine-color-dark-0)]"
                        }`}
                        shadow="sm"
                        radius="md"
                        style={{
                          cursor: "grab",
                          borderLeftWidth: "2px",
                        }}
                        mt={3}
                      >
                        <Card.Section p="xs">
                          <Flex align="center" justify="space-between">
                            <Group gap="8px">
                              <Badge size="sm" variant="filled" style={{ pointerEvents: "none", padding: 0, width: 20, height: 20 }}>
                                {index + 1} {/* Display counter starting from 1 */}
                              </Badge>

                              <Avatar variant="light" bg="#E9ECEF" radius="sm" h={22} color={visit.color}>
                              <span style={{ fontSize: "12px", fontWeight: "600" }}>{(visit.sociale || "").slice(0, 5)}</span>
                              </Avatar>
                              {/* Display current time or duration */}
                              {activeEvent_Ids.has(Number(visit.id)) && (
                                <Text size="xs" c="green" fw={600}>
                                  {currentTimes.get(Number(visit.id)) || new Date().toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}
                                </Text>
                              )}
                              {eventDurations.has(Number(visit.id)) && (
                                <Text size="xs" c="blue" fw={600}>
                                  {eventDurations.get(Number(visit.id))}min
                                </Text>
                              )}
                               <Text tt="capitalize">{visit.last_name?.slice(0, 6) || 'N/A'} &nbsp;{visit.first_name?.slice(0, 8) || 'N/A'} </Text>
                            </Group>
                            {/* Right Section */}
                            
                            <Group gap="2px">
                               <Avatar  bg="#ff5722" radius="sm" h={22} pt={1}>
                              <Text fw={600} c={"white"}>FLT</Text>
                              </Avatar>


                              <Icon path={mdiTooth} size={1} color={"#d50000"} />

                           
                            </Group>
                          </Flex>
                        </Card.Section>
                      </Card>
                    </>
                  </div>
                )}
              </Draggable>
            ))
          ) : (
            <Text>No results found</Text>
          )}
                {provided.placeholder}
              </div>
              </SimpleBar>
              </div>
            )}
          </Droppable>
       
        ) : (
          <Text>No results found</Text>
        )}
        {provided.placeholder}
      </div>
    )}
  </Droppable>
  

                      </>
                    )}
                  </div>
                  <Button
                    fullWidth
                    onClick={() =>
                      handleSlotSelect({
                        start: new Date(), // Use current date as an example
                        end: new Date(), // Or use the same start time if not needed

                      })
                    }
                    className="mt-4 HoverButton"
                    leftSection={<Icon path={mdiPlus} size={1} />}
                  >
                    Ajouter rendez-vous
                  </Button>
                </div>
       </Container>
                    </div>
                    </Card>
        </div>
      </div>

    </div>
     {isSidebarAlert && (
   
        <Paper shadow="xs" p="4px" w={"19%"} h={'100vh'}>
    <ScrollArea h={400}>
                       <ScrollArea h={400}>
                   <Tree
                     nodes={mockTree.filter((n) => n.value.toLowerCase().includes(searchValue.toLowerCase()))}
                    onSelect={handleSidebarSelect}

                  /> 
                </ScrollArea>
   
                   </ScrollArea>
                   </Paper >
                  
      )}
    </div>
      {/* Patient Details Modal */}
      <Modal
        opened={patientDetailsOpened}
        onClose={closePatientDetailsModal}
        title="Détails du patient"
        size="lg"
      >
        {selectedPatient ? (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Text size="sm" fw={600}>Nom complet</Text>
                <Text size="sm">{selectedPatient.first_name} {selectedPatient.last_name}</Text>
              </div>
              <div>
                <Text size="sm" fw={600}>Type de consultation</Text>
                <Text size="sm">{selectedPatient.typeConsultation || 'N/A'}</Text>
              </div>
              <div>
                <Text size="sm" fw={600}>Téléphone</Text>
                <Text size="sm">{selectedPatient.phone_number || 'N/A'}</Text>
              </div>
              <div>
                <Text size="sm" fw={600}>Heure</Text>
                <Text size="sm">{selectedPatient.start} - {selectedPatient.end}</Text>
              </div>
              <div>
                <Text size="sm" fw={600}>Durée</Text>
                <Text size="sm">{selectedPatient.duration} minutes</Text>
              </div>
              <div>
                <Text size="sm" fw={600}>Docteur</Text>
                <Text size="sm">{selectedPatient.docteur || 'N/A'}</Text>
              </div>
            </div>
            {selectedPatient.desc && (
              <div>
                <Text size="sm" fw={600}>Description</Text>
                <Text size="sm">{selectedPatient.desc}</Text>
              </div>
            )}
            {selectedPatient.notes && (
              <div>
                <Text size="sm" fw={600}>Notes</Text>
                <Text size="sm">{selectedPatient.notes}</Text>
              </div>
            )}
            <Group justify="flex-end" mt="xl">
              <Button variant="outline" onClick={closePatientDetailsModal}>
                Fermer
              </Button>
              <Button
                onClick={() => {
                  if (selectedPatient) {
                    openEditPatient(selectedPatient);
                  }
                  closePatientDetailsModal();
                }}
              >
                Modifier
              </Button>
            </Group>
          </div>
        ) : (
          <div className="text-center p-4">
            <Text size="sm" c="dimmed">Aucune information patient disponible</Text>
          </div>
        )}
      </Modal>
 <Modal opened={VisitesActivesDeleteOpened} onClose={closeVisitesActivesDelete} withCloseButton={false}>
              <Alert variant="light" color="red" title="Avertissement" icon={icon}>
                  Êtes-vous sûr de vouloir supprimer l&apos;événement ?

                </Alert>
                <Group justify="space-between" mt="md" mb="xs">
                <Button
                variant="filled"
                size="xs"
                radius="lg"
                color="red"
                onClick={() => {
                  // Use the visitToDelete state
                  if (visitToDelete) {
                    const eventId = Number(visitToDelete.id);
                    if (!isNaN(eventId)) {
                      deleteVisits(eventId);
                      closeVisitesActivesDelete();
                      setVisitToDelete(null);
                    } else {
                      console.error("Invalid event ID:", visitToDelete.id);
                      notifications.show({
                        title: 'Error',
                        message: 'Could not delete event: Invalid ID',
                        color: 'red',
                      });
                    }
                  }
                }}
              >
                Oui
              </Button>
                  <Button
                    variant="filled"
                    size="xs"
                    radius="lg"
                    color="lime.4"
                    onClick={closeVisitesActivesDelete}
                  >
                    Non
                  </Button>
                </Group>
            </Modal>

            {/* Simple Working Edit Patient Modal */}
            <Modal
              opened={EditwaitingListOpened}
              onClose={closeEditwaitingList}
              title="Modifier le patient"
              size="lg"
              centered
            >
              <form onSubmit={patientForm.onSubmit(handleUpdatePatient)}>
                <Stack gap="md">
                  <Group grow>
                    <TextInput
                      label="Prénom"
                      placeholder="Prénom du patient"
                      {...patientForm.getInputProps('first_name')}
                    />
                    <TextInput
                      label="Nom"
                      placeholder="Nom du patient"
                      {...patientForm.getInputProps('last_name')}
                    />
                  </Group>

                  <Group grow>
                    <TextInput
                      label="Téléphone"
                      placeholder="Numéro de téléphone"
                      {...patientForm.getInputProps('phone_numbers')}
                    />
                    <NumberInput
                      label="Durée (minutes)"
                      placeholder="Durée de la consultation"
                      min={5}
                      max={240}
                      {...patientForm.getInputProps('duration')}
                    />
                  </Group>

                  <Textarea
                    label="Notes"
                    placeholder="Notes ou commentaires"
                    rows={3}
                    {...patientForm.getInputProps('notes')}
                  />

                  {/* Add to Waiting List options (based on AjouterUnRendezVous pattern) */}
                  <Divider my="md" />
                  <Text size="sm" fw={500} mb="xs">Options de déplacement</Text>

                  <Stack gap="xs">
                    <Switch
                      color="teal"
                      size="sm"
                      label="Ajouter au calendrier (garder en liste d'attente)"
                      description="Le patient sera ajouté au calendrier mais restera dans la liste d'attente"
                      {...patientForm.getInputProps('addToWaitingList', { type: 'checkbox' })}
                      thumbIcon={
                        patientForm.values.addToWaitingList ? (
                          <IconCheck size={12} color="var(--mantine-color-teal-6)" stroke={3} />
                        ) : null
                      }
                    />

                    <Switch
                      color="orange"
                      size="sm"
                      label="Déplacer vers la salle de présence"
                      description="Le patient sera activé et déplacé vers la salle de présence"
                      {...patientForm.getInputProps('moveToPresentation', { type: 'checkbox' })}
                      thumbIcon={
                        patientForm.values.moveToPresentation ? (
                          <IconCheck size={12} color="var(--mantine-color-orange-6)" stroke={3} />
                        ) : null
                      }
                    />
                  </Stack>

                  <Group justify="flex-end" mt="md">
                    <Button variant="outline" onClick={closeEditwaitingList}>
                      Annuler
                    </Button>
                    <Button type="submit" color="blue">
                      Enregistrer
                    </Button>
                  </Group>
                </Stack>
              </form>
            </Modal>

            {/* Patient Details Modal with Add Visit button */}
            <PatientDetailsModal
              opened={showViewModal}
              onClose={() => {
                setShowViewModal(false);
                setSelectedEvent(null);
              }}
              patient={selectedEvent || undefined}
              onAddVisit={(patient) => {
                console.log('Add visit for patient:', patient);
                if (patient && selectedEvent) {
                  // Use selectedEvent directly since it's already a WaitingListPatient
                  activateAppointment(selectedEvent);
                }
                setShowViewModal(false);
                setSelectedEvent(null);
              }}
            />

            {/* Enhanced Lunchtime Background Modal */}
            <LunchtimeBackgroundModal
              opened={lunchtimeModalOpened}
              onClose={closeLunchtimeModal}
              onSave={handleLunchtimeSave}
              currentDate={currentDate}
              selectedRoom={selectedLunchRoom}
              selectedDoctor={selectedLunchDoctor}
              existingEvents={events}
              staffOptions={realStaffOptions}
            />

    </>
     </DragDropContext>
  );
};

export default DayView;
