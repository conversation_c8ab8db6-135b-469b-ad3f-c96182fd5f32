"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_layout_navBarButton_Iconbar_SwitchColorModes_tsx"],{

/***/ "(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconCircleHalf2.mjs":
/*!*****************************************************************************!*\
  !*** ./node_modules/@tabler/icons-react/dist/esm/icons/IconCircleHalf2.mjs ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ IconCircleHalf2)\n/* harmony export */ });\n/* harmony import */ var _createReactComponent_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createReactComponent.mjs */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/createReactComponent.mjs\");\n/**\n * @license @tabler/icons-react v3.34.1 - MIT\n *\n * This source code is licensed under the MIT license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            \"d\": \"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0\",\n            \"key\": \"svg-0\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M12 3v18\",\n            \"key\": \"svg-1\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M12 14l7 -7\",\n            \"key\": \"svg-2\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M12 19l8.5 -8.5\",\n            \"key\": \"svg-3\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M12 9l4.5 -4.5\",\n            \"key\": \"svg-4\"\n        }\n    ]\n];\nconst IconCircleHalf2 = (0,_createReactComponent_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"outline\", \"circle-half-2\", \"CircleHalf2\", __iconNode);\n //# sourceMappingURL=IconCircleHalf2.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AdGFibGVyL2ljb25zLXJlYWN0L2Rpc3QvZXNtL2ljb25zL0ljb25DaXJjbGVIYWxmMi5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBR2EsbUJBQXVCO0lBQUM7UUFBQyxPQUFPO1FBQUE7WUFBQyxDQUFJO1lBQTZDLE9BQU0sT0FBTztRQUFDO0tBQUEsQ0FBRTtJQUFBO1FBQUM7UUFBTyxDQUFDO1lBQUEsR0FBSTtZQUFXLE9BQU0sT0FBTztRQUFDO0tBQUU7SUFBQTtRQUFDLE1BQU87UUFBQTtZQUFDLEtBQUksQ0FBYztZQUFBLE9BQU07UUFBUTtLQUFBLENBQUU7SUFBQTtRQUFDLE1BQU87UUFBQTtZQUFDLEtBQUksaUJBQWtCO1lBQUEsTUFBTTtRQUFRO0tBQUEsQ0FBRTtJQUFBO1FBQUMsTUFBTztRQUFBO1lBQUMsS0FBSSxnQkFBaUI7WUFBQSxNQUFNLFFBQU87UUFBQSxDQUFDO0tBQUM7Q0FBQTtBQUU5UixDQUFNLG9CQUFrQix5RUFBcUIsU0FBVyxtQkFBaUIsZUFBZSxDQUFVIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNvcnJ5XFxEZXNrdG9wXFxzcmNcXGljb25zXFxJY29uQ2lyY2xlSGFsZjIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZVJlYWN0Q29tcG9uZW50IGZyb20gJy4uL2NyZWF0ZVJlYWN0Q29tcG9uZW50JztcbmltcG9ydCB7IEljb25Ob2RlIH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5leHBvcnQgY29uc3QgX19pY29uTm9kZTogSWNvbk5vZGUgPSBbW1wicGF0aFwiLHtcImRcIjpcIk0xMiAxMm0tOSAwYTkgOSAwIDEgMCAxOCAwYTkgOSAwIDEgMCAtMTggMFwiLFwia2V5XCI6XCJzdmctMFwifV0sW1wicGF0aFwiLHtcImRcIjpcIk0xMiAzdjE4XCIsXCJrZXlcIjpcInN2Zy0xXCJ9XSxbXCJwYXRoXCIse1wiZFwiOlwiTTEyIDE0bDcgLTdcIixcImtleVwiOlwic3ZnLTJcIn1dLFtcInBhdGhcIix7XCJkXCI6XCJNMTIgMTlsOC41IC04LjVcIixcImtleVwiOlwic3ZnLTNcIn1dLFtcInBhdGhcIix7XCJkXCI6XCJNMTIgOWw0LjUgLTQuNVwiLFwia2V5XCI6XCJzdmctNFwifV1dXG5cbmNvbnN0IEljb25DaXJjbGVIYWxmMiA9IGNyZWF0ZVJlYWN0Q29tcG9uZW50KCdvdXRsaW5lJywgJ2NpcmNsZS1oYWxmLTInLCAnQ2lyY2xlSGFsZjInLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IGRlZmF1bHQgSWNvbkNpcmNsZUhhbGYyOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconCircleHalf2.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconMoonStars.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/@tabler/icons-react/dist/esm/icons/IconMoonStars.mjs ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ IconMoonStars)\n/* harmony export */ });\n/* harmony import */ var _createReactComponent_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createReactComponent.mjs */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/createReactComponent.mjs\");\n/**\n * @license @tabler/icons-react v3.34.1 - MIT\n *\n * This source code is licensed under the MIT license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            \"d\": \"M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z\",\n            \"key\": \"svg-0\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M17 4a2 2 0 0 0 2 2a2 2 0 0 0 -2 2a2 2 0 0 0 -2 -2a2 2 0 0 0 2 -2\",\n            \"key\": \"svg-1\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M19 11h2m-1 -1v2\",\n            \"key\": \"svg-2\"\n        }\n    ]\n];\nconst IconMoonStars = (0,_createReactComponent_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"outline\", \"moon-stars\", \"MoonStars\", __iconNode);\n //# sourceMappingURL=IconMoonStars.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AdGFibGVyL2ljb25zLXJlYWN0L2Rpc3QvZXNtL2ljb25zL0ljb25Nb29uU3RhcnMubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUdPLE1BQU0sVUFBdUI7SUFBQztRQUFDO1FBQU87WUFBQyxLQUFJLGdGQUFpRjtZQUFBLE9BQU0sQ0FBTztRQUFBLENBQUM7S0FBRTtJQUFBO1FBQUMsTUFBTztRQUFBO1lBQUMsR0FBSTtZQUFvRSxDQUFNO1FBQVE7S0FBQTtJQUFFO1FBQUM7UUFBTyxDQUFDO1lBQUEsS0FBSSxDQUFtQjtZQUFBLE1BQU0sUUFBTztRQUFBLENBQUM7S0FBQztDQUFBO0FBRW5TLENBQU0sa0JBQWdCLHlFQUFxQixTQUFXLGdCQUFjLGFBQWEsQ0FBVSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzb3JyeVxcRGVza3RvcFxcc3JjXFxpY29uc1xcSWNvbk1vb25TdGFycy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlUmVhY3RDb21wb25lbnQgZnJvbSAnLi4vY3JlYXRlUmVhY3RDb21wb25lbnQnO1xuaW1wb3J0IHsgSWNvbk5vZGUgfSBmcm9tICcuLi90eXBlcyc7XG5cbmV4cG9ydCBjb25zdCBfX2ljb25Ob2RlOiBJY29uTm9kZSA9IFtbXCJwYXRoXCIse1wiZFwiOlwiTTEyIDNjLjEzMiAwIC4yNjMgMCAuMzkzIDBhNy41IDcuNSAwIDAgMCA3LjkyIDEyLjQ0NmE5IDkgMCAxIDEgLTguMzEzIC0xMi40NTR6XCIsXCJrZXlcIjpcInN2Zy0wXCJ9XSxbXCJwYXRoXCIse1wiZFwiOlwiTTE3IDRhMiAyIDAgMCAwIDIgMmEyIDIgMCAwIDAgLTIgMmEyIDIgMCAwIDAgLTIgLTJhMiAyIDAgMCAwIDIgLTJcIixcImtleVwiOlwic3ZnLTFcIn1dLFtcInBhdGhcIix7XCJkXCI6XCJNMTkgMTFoMm0tMSAtMXYyXCIsXCJrZXlcIjpcInN2Zy0yXCJ9XV1cblxuY29uc3QgSWNvbk1vb25TdGFycyA9IGNyZWF0ZVJlYWN0Q29tcG9uZW50KCdvdXRsaW5lJywgJ21vb24tc3RhcnMnLCAnTW9vblN0YXJzJywgX19pY29uTm9kZSk7XG5cbmV4cG9ydCBkZWZhdWx0IEljb25Nb29uU3RhcnM7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconMoonStars.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSunHigh.mjs":
/*!*************************************************************************!*\
  !*** ./node_modules/@tabler/icons-react/dist/esm/icons/IconSunHigh.mjs ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ IconSunHigh)\n/* harmony export */ });\n/* harmony import */ var _createReactComponent_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createReactComponent.mjs */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/createReactComponent.mjs\");\n/**\n * @license @tabler/icons-react v3.34.1 - MIT\n *\n * This source code is licensed under the MIT license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            \"d\": \"M14.828 14.828a4 4 0 1 0 -5.656 -5.656a4 4 0 0 0 5.656 5.656z\",\n            \"key\": \"svg-0\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M6.343 17.657l-1.414 1.414\",\n            \"key\": \"svg-1\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M6.343 6.343l-1.414 -1.414\",\n            \"key\": \"svg-2\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M17.657 6.343l1.414 -1.414\",\n            \"key\": \"svg-3\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M17.657 17.657l1.414 1.414\",\n            \"key\": \"svg-4\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M4 12h-2\",\n            \"key\": \"svg-5\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M12 4v-2\",\n            \"key\": \"svg-6\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M20 12h2\",\n            \"key\": \"svg-7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M12 20v2\",\n            \"key\": \"svg-8\"\n        }\n    ]\n];\nconst IconSunHigh = (0,_createReactComponent_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"outline\", \"sun-high\", \"SunHigh\", __iconNode);\n //# sourceMappingURL=IconSunHigh.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSunHigh.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/layout/navBarButton/Iconbar/SwitchColorModes.tsx":
/*!**************************************************************!*\
  !*** ./src/layout/navBarButton/Iconbar/SwitchColorModes.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _i18n_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ~/i18n/client */ \"(app-pages-browser)/./src/i18n/client.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/use-mantine-color-scheme/use-mantine-color-scheme.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Menu/Menu.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tooltip/Tooltip.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIcon.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCircleHalf2_IconMoonStars_IconSunHigh_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=IconCircleHalf2,IconMoonStars,IconSunHigh!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconCircleHalf2.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCircleHalf2_IconMoonStars_IconSunHigh_tabler_icons_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=IconCircleHalf2,IconMoonStars,IconSunHigh!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconMoonStars.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCircleHalf2_IconMoonStars_IconSunHigh_tabler_icons_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=IconCircleHalf2,IconMoonStars,IconSunHigh!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSunHigh.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst ICON_SIZE = 20;\nconst SwitchColorModes = ()=>{\n    _s();\n    const { colorScheme, setColorScheme } = (0,_mantine_core__WEBPACK_IMPORTED_MODULE_3__.useMantineColorScheme)();\n    const { t } = (0,_i18n_client__WEBPACK_IMPORTED_MODULE_1__.useTranslation)('menu');\n    const [isChanging, setIsChanging] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const handleColorSchemeChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"SwitchColorModes.useCallback[handleColorSchemeChange]\": async (newScheme)=>{\n            if (isChanging) return; // Prevent multiple rapid clicks\n            setIsChanging(true);\n            try {\n                setColorScheme(newScheme);\n                // Save to localStorage for persistence\n                localStorage.setItem('theme', newScheme);\n            } finally{\n                // Reset after a short delay\n                setTimeout({\n                    \"SwitchColorModes.useCallback[handleColorSchemeChange]\": ()=>setIsChanging(false)\n                }[\"SwitchColorModes.useCallback[handleColorSchemeChange]\"], 300);\n            }\n        }\n    }[\"SwitchColorModes.useCallback[handleColorSchemeChange]\"], [\n        setColorScheme,\n        isChanging\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Menu, {\n            shadow: \"lg\",\n            width: 200,\n            zIndex: 1000010,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Menu.Target, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                        label: t(\"Switch-color-modes\"),\n                        withArrow: true,\n                        style: {\n                            color: \"var(--mantine-color-text)\"\n                        },\n                        className: \"h-10   navBarButtonicon\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.ActionIcon, {\n                            //variant=\"light\"\n                            className: \"h-10 bg-[var(--bg-SwitchColor)]  navBarButtonicon\",\n                            style: {\n                                color: \" light-dark(var(--mantine-color-gray-6), var(--mantine-color-dark-0))\"\n                            },\n                            children: colorScheme === \"auto\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCircleHalf2_IconMoonStars_IconSunHigh_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                size: ICON_SIZE,\n                                className: \" navBarButtonicon \"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\testBN\\\\projects\\\\f\\\\src\\\\layout\\\\navBarButton\\\\Iconbar\\\\SwitchColorModes.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 17\n                            }, undefined) : colorScheme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCircleHalf2_IconMoonStars_IconSunHigh_tabler_icons_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                size: ICON_SIZE,\n                                className: \" navBarButtonicon \"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\testBN\\\\projects\\\\f\\\\src\\\\layout\\\\navBarButton\\\\Iconbar\\\\SwitchColorModes.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 17\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCircleHalf2_IconMoonStars_IconSunHigh_tabler_icons_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                size: ICON_SIZE,\n                                className: \" navBarButtonicon \"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\testBN\\\\projects\\\\f\\\\src\\\\layout\\\\navBarButton\\\\Iconbar\\\\SwitchColorModes.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\testBN\\\\projects\\\\f\\\\src\\\\layout\\\\navBarButton\\\\Iconbar\\\\SwitchColorModes.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\testBN\\\\projects\\\\f\\\\src\\\\layout\\\\navBarButton\\\\Iconbar\\\\SwitchColorModes.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\testBN\\\\projects\\\\f\\\\src\\\\layout\\\\navBarButton\\\\Iconbar\\\\SwitchColorModes.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Menu.Dropdown, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Menu.Label, {\n                            tt: \"uppercase\",\n                            ta: \"center\",\n                            fw: 600,\n                            children: t('color-modes')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\testBN\\\\projects\\\\f\\\\src\\\\layout\\\\navBarButton\\\\Iconbar\\\\SwitchColorModes.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Menu.Item, {\n                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCircleHalf2_IconMoonStars_IconSunHigh_tabler_icons_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\testBN\\\\projects\\\\f\\\\src\\\\layout\\\\navBarButton\\\\Iconbar\\\\SwitchColorModes.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 26\n                            }, void 0),\n                            onClick: ()=>handleColorSchemeChange(\"light\"),\n                            children: t('Light-modes')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\testBN\\\\projects\\\\f\\\\src\\\\layout\\\\navBarButton\\\\Iconbar\\\\SwitchColorModes.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Menu.Item, {\n                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCircleHalf2_IconMoonStars_IconSunHigh_tabler_icons_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\testBN\\\\projects\\\\f\\\\src\\\\layout\\\\navBarButton\\\\Iconbar\\\\SwitchColorModes.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 26\n                            }, void 0),\n                            onClick: ()=>handleColorSchemeChange(\"dark\"),\n                            children: t('Dark-modes')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\testBN\\\\projects\\\\f\\\\src\\\\layout\\\\navBarButton\\\\Iconbar\\\\SwitchColorModes.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Menu.Item, {\n                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCircleHalf2_IconMoonStars_IconSunHigh_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\testBN\\\\projects\\\\f\\\\src\\\\layout\\\\navBarButton\\\\Iconbar\\\\SwitchColorModes.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 26\n                            }, void 0),\n                            onClick: ()=>handleColorSchemeChange(\"auto\"),\n                            children: \"Auto\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\testBN\\\\projects\\\\f\\\\src\\\\layout\\\\navBarButton\\\\Iconbar\\\\SwitchColorModes.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\testBN\\\\projects\\\\f\\\\src\\\\layout\\\\navBarButton\\\\Iconbar\\\\SwitchColorModes.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\testBN\\\\projects\\\\f\\\\src\\\\layout\\\\navBarButton\\\\Iconbar\\\\SwitchColorModes.tsx\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(SwitchColorModes, \"XmcA0dGGnQsoam8oJaTouyg8xLI=\", false, function() {\n    return [\n        _mantine_core__WEBPACK_IMPORTED_MODULE_3__.useMantineColorScheme,\n        _i18n_client__WEBPACK_IMPORTED_MODULE_1__.useTranslation\n    ];\n});\n_c = SwitchColorModes;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SwitchColorModes);\nvar _c;\n$RefreshReg$(_c, \"SwitchColorModes\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/layout/navBarButton/Iconbar/SwitchColorModes.tsx\n"));

/***/ })

}]);