"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/klona";
exports.ids = ["vendor-chunks/klona"];
exports.modules = {

/***/ "(ssr)/./node_modules/klona/full/index.mjs":
/*!*******************************************!*\
  !*** ./node_modules/klona/full/index.mjs ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   klona: () => (/* binding */ klona)\n/* harmony export */ });\nfunction set(obj, key, val) {\n\tif (typeof val.value === 'object') val.value = klona(val.value);\n\tif (!val.enumerable || val.get || val.set || !val.configurable || !val.writable || key === '__proto__') {\n\t\tObject.defineProperty(obj, key, val);\n\t} else obj[key] = val.value;\n}\n\nfunction klona(x) {\n\tif (typeof x !== 'object') return x;\n\n\tvar i=0, k, list, tmp, str=Object.prototype.toString.call(x);\n\n\tif (str === '[object Object]') {\n\t\ttmp = Object.create(x.__proto__ || null);\n\t} else if (str === '[object Array]') {\n\t\ttmp = Array(x.length);\n\t} else if (str === '[object Set]') {\n\t\ttmp = new Set;\n\t\tx.forEach(function (val) {\n\t\t\ttmp.add(klona(val));\n\t\t});\n\t} else if (str === '[object Map]') {\n\t\ttmp = new Map;\n\t\tx.forEach(function (val, key) {\n\t\t\ttmp.set(klona(key), klona(val));\n\t\t});\n\t} else if (str === '[object Date]') {\n\t\ttmp = new Date(+x);\n\t} else if (str === '[object RegExp]') {\n\t\ttmp = new RegExp(x.source, x.flags);\n\t} else if (str === '[object DataView]') {\n\t\ttmp = new x.constructor( klona(x.buffer) );\n\t} else if (str === '[object ArrayBuffer]') {\n\t\ttmp = x.slice(0);\n\t} else if (str.slice(-6) === 'Array]') {\n\t\t// ArrayBuffer.isView(x)\n\t\t// ~> `new` bcuz `Buffer.slice` => ref\n\t\ttmp = new x.constructor(x);\n\t}\n\n\tif (tmp) {\n\t\tfor (list=Object.getOwnPropertySymbols(x); i < list.length; i++) {\n\t\t\tset(tmp, list[i], Object.getOwnPropertyDescriptor(x, list[i]));\n\t\t}\n\n\t\tfor (i=0, list=Object.getOwnPropertyNames(x); i < list.length; i++) {\n\t\t\tif (Object.hasOwnProperty.call(tmp, k=list[i]) && tmp[k] === x[k]) continue;\n\t\t\tset(tmp, k, Object.getOwnPropertyDescriptor(x, k));\n\t\t}\n\t}\n\n\treturn tmp || x;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/klona/full/index.mjs\n");

/***/ })

};
;