"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_i18n_locales_fr_Corbeille_researchdoctor_json";
exports.ids = ["_rsc_src_i18n_locales_fr_Corbeille_researchdoctor_json"];
exports.modules = {

/***/ "(rsc)/./src/i18n/locales/fr/Corbeille/researchdoctor.json":
/*!***********************************************************!*\
  !*** ./src/i18n/locales/fr/Corbeille/researchdoctor.json ***!
  \***********************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"researchdoctor":"researchdoctor FR","conditions":"Recherche de Médecin généraliste","filters":"Filtres","sort-by":"Trier par","Ascending-price":"Prix croissant","Descending-price":"Prix décroissant","Availabl":"Disponible au plus tôt","results-found":"résultats trouvés","View-profile":"Voir le profil","specialite-title-form-l":"Je cherche","specialite-title-form-r":"je cherche un medecin","placeHolder-specialite":"Choisir une spécialité","placeHolder-city":"Sélectionner la ville","placeHolder-region":"Sélectionnez la région","specialite-form-submit":"Trouver","Civility":"Civilité","Mon":"Mon","Mrs":"Mme.","I-research":"Je recherche","A":"À","Display":"Choisir une ville","DisplayVille":"Spécialité ","Find":"Trouver","appointment":"Prendre rendez-vous","Next-availability":"Prochaine disponibilité","label-first-name":"Prénom*","label-last-name":"Nom *","label-email-star":"Email address *","label-phone":"Téléphone portable *","label-message-star":"En soumettant ce formulaire, j\'ai lu et j\'accepte la","Privacy-Policy":"politique de confidentialité","label-Send":" Envoyer","Prendre":"Prendre rendez-vous","Précédente":"Précédente","Suivant":"Suivant","Submit":"Soumettre","Choose":"Choisissez la valeur de la consultation","consultation":"Consultation de suivi","Urgence":"Urgence","When-would":"Quand souhaitez vous consulter ?","You-can-choose":"Vous pouvez choisir jusqu à 3 créneaux horaires. Vous serez contacté par un membre de notre équipe pour valider le plus  adéquat.","Choose-a-date":"Choisissez une date","Choose-a-time-on":"Choisissez une heure le","Morning":"Matin","Afternoon":"Après-midi","Summary-of-your-request":"Récapitulatif de votre demande","Reason-for-consultation":"Motif de la consultation","Price":"Prix","Your-availability":"Vos disponibilités","Identify-yourself":"Identifiez-vous","Please-enter-your-identity-below":"Merci de renseigner votre identité ci-dessous","Your-choice":"Votre choix","Add-another-slot":"Ajouter autre un créneau","About":"A propos","Availability":"Disponibilités","Services":"Prestations de service","First-consultation":"Première consultation","Emergency":"Urgence","These-prices":"Ces tarifs vous sont communiqués à titre indicatif. Ils peuvent varier en fonction des soins réelement effectués en cabinet.","Map":"Carte","Address":"Adresse","comments":"commentaires"}');

/***/ })

};
;