"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_i18n_locales_fr_Corbeille_blog_json";
exports.ids = ["_rsc_src_i18n_locales_fr_Corbeille_blog_json"];
exports.modules = {

/***/ "(rsc)/./src/i18n/locales/fr/Corbeille/blog.json":
/*!*************************************************!*\
  !*** ./src/i18n/locales/fr/Corbeille/blog.json ***!
  \*************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"blog":"blog FR","page-title":"Blog","app-description":"La plateforme santé de MedecinSvp est une interface média électronique permettant de publier des informations de santé précises et une passerelle électronique vers tous les services proposés par la plateforme.","keywords":"Site Web du ministère de la Santé, actualités sur la santé, annonces du ministère de la Santé, événements de santé, statistiques du ministère de la Santé, services du ministère de la Santé, ministère de la Santé, centre de santé, nombre de blessures, examens, santé"}');

/***/ })

};
;