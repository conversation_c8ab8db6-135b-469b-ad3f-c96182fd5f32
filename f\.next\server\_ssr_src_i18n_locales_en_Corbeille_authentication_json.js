"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_i18n_locales_en_Corbeille_authentication_json";
exports.ids = ["_ssr_src_i18n_locales_en_Corbeille_authentication_json"];
exports.modules = {

/***/ "(ssr)/./src/i18n/locales/en/Corbeille/authentication.json":
/*!***********************************************************!*\
  !*** ./src/i18n/locales/en/Corbeille/authentication.json ***!
  \***********************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"page-title":"Login","Signup-title":"Signup","description":"Make an appointment or make a teleconsultation easily with your doctor, a specialist or a practitioner near you.","Go-to-Site":"Go to Site","Register-easily":"“ Register easily in a few clicks to appear on our site which lists the doctors of Morocco. ”","Project-Manager":"Project Manager","text-p":"Patients will find you more easily!","text-p1":"By registering on our site, and by properly filling out your profile, you will gain new patients who are looking for a doctor on the web.","text-p2":"Working hours","text-p3":"Displaying working hours gives patients a clearer idea of your consultation schedule.","text-p4":"A site perfectly adapted to mobile","text-p5":"Our site is designed to facilitate the mobile user experience. It is simple, fast to facilitate the task for anyone who would seek to find a doctor and contact him.","text-heder":"Happy to see you again","text-heder-sub":"Log in to manage your account.","e-mail":"Your email","label-password":"Password","Forgot-your-password":"Forgot your password?","label-Login":"Login","Do-not-have-an-accoun-yet":"Do not have an account yet?","register-here":"register here","Caractères-requis":"8+ Characters required","Former":"Former","submit":"submit","next":"Next","Are-you-on-this-account":"Are you on this account?","Not-Found":"Not Found","Welcome-to-MedecienSvp":"Welcome to MedecienSvp","form-to-get-started":"Fill out the form to get started.","The-good-choice":"The good choice","Doctors-6":"Doctors 600 Dhs Monthly","Doctors-12":"Doctors 4000 Dhs Each year","Clinical-6":"Clinic 1200 Dhs Monthly","Clinical-12":"Clinic 7000 Dhs Each year","Pharmacy-6":"Pharmacy 500 Dhs Monthly","Pharmacy-12":"Pharmacy 3000 Dhs Each year","label-name":"Name *","label-First-name":"First name*","placeholder-email":"Enter Your Email","label-confirm-password":"Confirm Password","Mon":"sir","Mrs":"Mrs","label-address":"Address *","label-contact-number":"Contact Number","placeholder-phone":"Cell number","label-postcode":"Postcode","label-email-required":"Email Address (required)","step4-p":" We are very happy to have you among us.","step4-p1":"We wish you success in your work.","step4-p2":"A confirmation email has just been sent to you. Please activate your account by clicking on the confirmation link in the email. Warning: If you do not receive an email in your inbox, please check your unwanted messages (SPAM).","authentication":"authentication en","signup-title":"Signup","reset-password-title":"reset-password","visiteur-signup-title":"visitor sign up","app-description":"Make an appointment or make a teleconsultation easily with your doctor, a specialist or a practitioner near you.","Trusted":"Trusted by over 37k customer","Remember-me":"Remember me","Entrez-votre":"Enter your email address below and we\'ll get you back on track.","Return-connection":"Return connection","Enter-your-email-address":"Enter your email address","customer":"Trusted by over 37k customer","Welcome":"Welcome to MedecienSvp","form-to-get":"Fill out the form to get started.","welcome-among-us":"welcome among us","please-include":"Please include a valid email address so we can get back to you","Please-include-a-valid":"Please include a valid email address so we can get back to you","characters":"8+ characters required"}');

/***/ })

};
;