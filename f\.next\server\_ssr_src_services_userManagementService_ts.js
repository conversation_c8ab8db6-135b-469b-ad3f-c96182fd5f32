"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_services_userManagementService_ts";
exports.ids = ["_ssr_src_services_userManagementService_ts"];
exports.modules = {

/***/ "(ssr)/./src/services/userManagementService.ts":
/*!***********************************************!*\
  !*** ./src/services/userManagementService.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _utils_mockDataStorage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ~/utils/mockDataStorage */ \"(ssr)/./src/utils/mockDataStorage.ts\");\n/* harmony import */ var _utils_statusConverter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ~/utils/statusConverter */ \"(ssr)/./src/utils/statusConverter.ts\");\n\n\n\n// Mock data for user accounts\nconst mockUserAccounts = [\n    {\n        id: '1',\n        email: '<EMAIL>',\n        first_name: 'John',\n        last_name: 'Smith',\n        user_type: 'doctor',\n        status: 'active',\n        created_at: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(),\n        created_by: 'self',\n        phone_number: '+****************'\n    },\n    {\n        id: '3',\n        email: '<EMAIL>',\n        first_name: 'Robert',\n        last_name: 'Johnson',\n        user_type: 'patient',\n        status: 'active',\n        created_at: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),\n        created_by: '1',\n        phone_number: '+****************'\n    }\n];\n// Mock data for subscription packages\nconst mockSubscriptionPackages = [\n    {\n        id: '1',\n        name: 'Basic',\n        max_patients: 50,\n        max_assistants: 1,\n        max_staff: 1,\n        price_monthly: 99,\n        price_yearly: 999,\n        features: [\n            '50 Patient Accounts',\n            '1 Assistant Account',\n            '1 Staff Account',\n            'Basic Appointment Scheduling',\n            'Patient Records Management'\n        ],\n        is_current: false\n    },\n    {\n        id: '2',\n        name: 'Professional',\n        max_patients: 200,\n        max_assistants: 3,\n        max_staff: 3,\n        price_monthly: 199,\n        price_yearly: 1999,\n        features: [\n            '200 Patient Accounts',\n            '3 Assistant Accounts',\n            '3 Staff Accounts',\n            'Advanced Appointment Scheduling',\n            'Patient Records Management',\n            'Billing Integration',\n            'Email Notifications'\n        ],\n        is_current: true\n    },\n    {\n        id: '3',\n        name: 'Enterprise',\n        max_patients: 500,\n        max_assistants: 10,\n        max_staff: 10,\n        price_monthly: 399,\n        price_yearly: 3999,\n        features: [\n            '500 Patient Accounts',\n            '10 Assistant Accounts',\n            '10 Staff Accounts',\n            'Advanced Appointment Scheduling',\n            'Patient Records Management',\n            'Billing Integration',\n            'Email & SMS Notifications',\n            'Custom Branding',\n            'Priority Support'\n        ],\n        is_current: false\n    }\n];\nconst userManagementService = {\n    // Function to clear cache for a specific doctor\n    clearDoctorCache (doctorId) {\n        const doctorSpecificCacheKey = `${_utils_mockDataStorage__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.REAL_USER_ACCOUNTS}_${doctorId}`;\n        const doctorSpecificTimestampKey = `${_utils_mockDataStorage__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.CACHE_TIMESTAMP_USER_ACCOUNTS}_${doctorId}`;\n        if (false) {}\n    },\n    async getUserAccounts () {\n        console.log('Fetching user accounts from API');\n        // Get the current doctor's ID from localStorage or other auth storage\n        const doctorId =  false ? 0 : null;\n        console.log('Current doctor ID:', doctorId);\n        if (!doctorId) {\n            console.warn('No doctor ID found in localStorage');\n            return [];\n        }\n        // Use doctor-specific cache keys\n        const doctorSpecificCacheKey = `${_utils_mockDataStorage__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.REAL_USER_ACCOUNTS}_${doctorId}`;\n        const doctorSpecificTimestampKey = `${_utils_mockDataStorage__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.CACHE_TIMESTAMP_USER_ACCOUNTS}_${doctorId}`;\n        // Check if we have valid cached data for this specific doctor\n        const cachedUsers = (0,_utils_mockDataStorage__WEBPACK_IMPORTED_MODULE_1__.getFromCache)(doctorSpecificCacheKey, doctorSpecificTimestampKey);\n        if (cachedUsers) {\n            console.log('Using cached user accounts data for doctor:', doctorId, cachedUsers);\n            return cachedUsers;\n        }\n        try {\n            // Fetch both assistants and staff for the logged-in doctor\n            let allUsers = [];\n            // First, fetch assistants\n            try {\n                console.log('Fetching assistants from API');\n                console.log('Current doctor ID for assistants query:', doctorId);\n                // Log the request URL for debugging\n                const assistantsUrl = `/api/auth/assistants/?assigned_doctor=${doctorId}`;\n                console.log('Assistants request URL:', assistantsUrl);\n                const assistantsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('/api/auth/assistants/', {\n                    params: {\n                        assigned_doctor: doctorId\n                    }\n                });\n                // Log the response status and data\n                console.log('API response status for assistants:', assistantsResponse.status);\n                console.log('API response data for assistants:', assistantsResponse.data);\n                // Handle paginated response structure\n                const assistantsData = assistantsResponse.data.results || assistantsResponse.data;\n                console.log('Number of assistants returned:', Array.isArray(assistantsData) ? assistantsData.length : 'Not an array');\n                // Transform the assistants response\n                const assistants = assistantsData.map((assistant)=>{\n                    // Use the status converter utility\n                    const status = (0,_utils_statusConverter__WEBPACK_IMPORTED_MODULE_2__.backendToFrontendStatus)(assistant.is_active, assistant.is_pending);\n                    // Log the status conversion for debugging\n                    console.log(`Assistant ${assistant.email} status conversion:`, {\n                        backend: {\n                            is_active: assistant.is_active,\n                            is_pending: assistant.is_pending\n                        },\n                        frontend: status\n                    });\n                    // Log the image fields for debugging\n                    console.log(`Assistant ${assistant.email} image fields:`, {\n                        profile_image: assistant.profile_image,\n                        profile_image_url: assistant.profile_image_url,\n                        profile_image_medium: assistant.profile_image_medium,\n                        profile_image_large: assistant.profile_image_large\n                    });\n                    // Determine the best image URL to use\n                    let imageUrl = assistant.profile_image_url;\n                    if (!imageUrl && assistant.profile_image) {\n                        // If profile_image is a string (URL), use it\n                        if (typeof assistant.profile_image === 'string') {\n                            imageUrl = assistant.profile_image;\n                        } else if (assistant.profile_image && typeof assistant.profile_image === 'object' && 'url' in assistant.profile_image) {\n                            imageUrl = assistant.profile_image.url;\n                        }\n                    }\n                    // Fallback to medium or large image if available\n                    if (!imageUrl) {\n                        imageUrl = assistant.profile_image_medium || assistant.profile_image_large;\n                    }\n                    console.log(`Final image URL for assistant ${assistant.email}:`, imageUrl);\n                    return {\n                        id: assistant.id || assistant.uuid,\n                        email: assistant.email,\n                        first_name: assistant.first_name,\n                        last_name: assistant.last_name,\n                        user_type: 'assistant',\n                        status: status,\n                        created_at: assistant.created_at || assistant.date_joined,\n                        created_by: assistant.assigned_doctor_email || 'system',\n                        phone_number: assistant.phone_number || '',\n                        profile_image_url: imageUrl\n                    };\n                });\n                allUsers = [\n                    ...allUsers,\n                    ...assistants\n                ];\n            } catch (assistantsError) {\n                console.error('Error fetching assistants from API:', assistantsError);\n            }\n            // Then, fetch staff members\n            try {\n                console.log('Fetching staff from API');\n                // Use the correct endpoint for staff members\n                const staffResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('/api/auth/staff/', {\n                    params: {\n                        assigned_doctor: doctorId\n                    }\n                });\n                console.log('API response for staff:', staffResponse.data);\n                // Handle paginated response structure\n                const staffData = staffResponse.data.results || staffResponse.data;\n                // Transform the staff response\n                const staffMembers = staffData.map((staff)=>{\n                    // Use the status converter utility\n                    const status = (0,_utils_statusConverter__WEBPACK_IMPORTED_MODULE_2__.backendToFrontendStatus)(staff.is_active, staff.is_pending);\n                    // Log the status conversion for debugging\n                    console.log(`Staff ${staff.email} status conversion:`, {\n                        backend: {\n                            is_active: staff.is_active,\n                            is_pending: staff.is_pending\n                        },\n                        frontend: status\n                    });\n                    return {\n                        id: staff.id || staff.uuid,\n                        email: staff.email,\n                        first_name: staff.first_name,\n                        last_name: staff.last_name,\n                        user_type: 'staff',\n                        status: status,\n                        created_at: staff.created_at || staff.date_joined,\n                        created_by: staff.assigned_doctor_email || 'system',\n                        phone_number: staff.phone_number || '',\n                        profile_image_url: staff.profile_image_url || staff.profile_image || undefined\n                    };\n                });\n                allUsers = [\n                    ...allUsers,\n                    ...staffMembers\n                ];\n            } catch (staffError) {\n                console.error('Error fetching staff from API:', staffError);\n            }\n            // If we got users from either endpoint, cache and return them\n            if (allUsers.length > 0) {\n                console.log('Successfully fetched users:', allUsers);\n                // Cache the data with doctor-specific keys\n                (0,_utils_mockDataStorage__WEBPACK_IMPORTED_MODULE_1__.storeInCache)(doctorSpecificCacheKey, doctorSpecificTimestampKey, allUsers);\n                return allUsers;\n            }\n            // If both specific endpoints failed, use mock data instead of trying an endpoint that doesn't exist\n            console.log('Using mock data for users instead of API call');\n            // Create mock data based on user type\n            const mockUsers = mockUserAccounts.filter((user)=>user.user_type === 'assistant' || user.user_type === 'staff');\n            console.log('Mock users data:', mockUsers);\n            // Return mock data in the same format as the API would\n            const response = {\n                data: mockUsers.map((user)=>({\n                        id: user.id,\n                        uuid: user.id,\n                        email: user.email,\n                        first_name: user.first_name,\n                        last_name: user.last_name,\n                        user_type: user.user_type,\n                        is_active: user.status === 'active',\n                        is_pending: user.status === 'pending',\n                        created_at: user.created_at,\n                        date_joined: user.created_at,\n                        created_by: user.created_by,\n                        phone_number: user.phone_number,\n                        profile_image_url: user.profile_image_url,\n                        profile_image: user.profile_image_url\n                    }))\n            };\n            console.log('Mock API response for users:', response.data);\n            // Filter for assistants and staff\n            const users = response.data.filter((user)=>user.user_type === 'assistant' || user.user_type === 'staff').map((user)=>{\n                // Use the status converter utility\n                const status = (0,_utils_statusConverter__WEBPACK_IMPORTED_MODULE_2__.backendToFrontendStatus)(user.is_active, user.is_pending);\n                // Log the status conversion for debugging\n                console.log(`User ${user.email} status conversion:`, {\n                    backend: {\n                        is_active: user.is_active,\n                        is_pending: user.is_pending\n                    },\n                    frontend: status\n                });\n                return {\n                    id: user.id || user.uuid || `temp_${Date.now()}_${Math.random()}`,\n                    email: user.email,\n                    first_name: user.first_name,\n                    last_name: user.last_name,\n                    user_type: user.user_type || 'assistant',\n                    status: status,\n                    created_at: user.created_at || user.date_joined || new Date().toISOString(),\n                    created_by: user.created_by || 'system',\n                    phone_number: user.phone_number || '',\n                    profile_image_url: user.profile_image_url || user.profile_image || undefined\n                };\n            });\n            // Cache the data with doctor-specific keys\n            (0,_utils_mockDataStorage__WEBPACK_IMPORTED_MODULE_1__.storeInCache)(doctorSpecificCacheKey, doctorSpecificTimestampKey, users);\n            return users;\n        } catch (error) {\n            console.error('Error fetching users from API:', error);\n            console.log('Falling back to mock data');\n            // Fallback to mock data for development/testing\n            if (false) {}\n            // If all API calls fail, return an empty array\n            return [];\n        }\n    },\n    async createUserAccount (data) {\n        // Log data without exposing sensitive information\n        if (data instanceof FormData) {\n            console.log('Creating user account with FormData containing fields:', Array.from(data.keys()));\n        } else {\n            console.log('Creating user account with data:', {\n                ...data,\n                password: data.password ? '[REDACTED]' : undefined,\n                confirm_password: data.confirm_password ? '[REDACTED]' : undefined\n            });\n        }\n        try {\n            // Check subscription limits first\n            const currentSubscription = await this.getCurrentSubscription();\n            const currentUsers = await this.getUserAccounts();\n            const assistantCount = currentUsers.filter((user)=>user.user_type === 'assistant').length;\n            const patientCount = currentUsers.filter((user)=>user.user_type === 'patient').length;\n            const staffCount = currentUsers.filter((user)=>user.user_type === 'staff').length;\n            // Extract user type from data\n            let userType = 'assistant';\n            if (data instanceof FormData) {\n                // Get user_type from FormData\n                const formDataUserType = data.get('user_type');\n                if (formDataUserType && typeof formDataUserType === 'string') {\n                    userType = formDataUserType;\n                }\n            } else {\n                // Get user_type from UserAccount object\n                userType = data.user_type || 'assistant';\n            }\n            // Check subscription limits based on user type\n            if (userType === 'assistant' && assistantCount >= currentSubscription.max_assistants) {\n                throw new Error(`You have reached the maximum number of assistants (${currentSubscription.max_assistants}) for your subscription package. You need to upgrade your package to add more assistants. Note that deleting assistants will not allow you to create new ones until your package is renewed.`);\n            }\n            if (userType === 'patient' && patientCount >= currentSubscription.max_patients) {\n                throw new Error(`You have reached the maximum number of patients (${currentSubscription.max_patients}) for your subscription package. You need to upgrade your package to add more patients.`);\n            }\n            if (userType === 'staff' && staffCount >= currentSubscription.max_staff) {\n                throw new Error(`You have reached the maximum number of staff (${currentSubscription.max_staff}) for your subscription package. You need to upgrade your package to add more staff. Note that deleting staff will not allow you to create new ones until your package is renewed.`);\n            }\n            // If data is FormData, we'll send it directly to the API\n            if (data instanceof FormData) {\n                // Set require_password_change flag to false\n                if (data.has('require_password_change')) {\n                    data.delete('require_password_change');\n                }\n                data.append('require_password_change', 'false');\n                // Add the current doctor's ID for assistants\n                const formDataUserType = data.get('user_type');\n                if (formDataUserType === 'assistant') {\n                    const doctorId = localStorage.getItem('userId');\n                    if (doctorId) {\n                        console.log('Adding assigned_doctor to FormData:', doctorId);\n                        data.append('assigned_doctor', doctorId);\n                    }\n                }\n                // Handle status conversion for FormData\n                if (data.has('status')) {\n                    const status = data.get('status');\n                    // Use the status converter utility\n                    const { is_active, is_pending } = (0,_utils_statusConverter__WEBPACK_IMPORTED_MODULE_2__.frontendToBackendStatus)(status);\n                    // Log the status conversion for debugging\n                    console.log(`FormData status conversion:`, {\n                        frontend: status,\n                        backend: {\n                            is_active,\n                            is_pending\n                        }\n                    });\n                    // Add the converted status to the FormData\n                    data.append('is_active', is_active.toString());\n                    data.append('is_pending', is_pending.toString());\n                // Remove status field as backend doesn't have it\n                // Note: FormData doesn't have a delete method in all browsers, so we can't remove it\n                }\n            } else {\n                // Prepare the data for API call from UserAccount object\n                const userData = {\n                    email: data.email,\n                    password: data.password,\n                    password2: data.password,\n                    first_name: data.first_name,\n                    last_name: data.last_name,\n                    user_type: data.user_type,\n                    phone_number: data.phone_number || '',\n                    // Set a flag to NOT require password change on first login\n                    require_password_change: false\n                };\n                // Add the current doctor's ID for assistants\n                if (data.user_type === 'assistant') {\n                    const doctorId = localStorage.getItem('userId');\n                    if (doctorId) {\n                        console.log('Adding assigned_doctor to userData:', doctorId);\n                        userData.assigned_doctor = doctorId;\n                    }\n                }\n                // Handle status conversion\n                if (data.status) {\n                    // Use the status converter utility\n                    const { is_active, is_pending } = (0,_utils_statusConverter__WEBPACK_IMPORTED_MODULE_2__.frontendToBackendStatus)(data.status);\n                    // Log the status conversion for debugging\n                    console.log(`JSON status conversion:`, {\n                        frontend: data.status,\n                        backend: {\n                            is_active,\n                            is_pending\n                        }\n                    });\n                    // Add the converted status to the userData\n                    userData.is_active = is_active;\n                    userData.is_pending = is_pending;\n                } else {\n                    // Default to pending\n                    const { is_active, is_pending } = (0,_utils_statusConverter__WEBPACK_IMPORTED_MODULE_2__.frontendToBackendStatus)('pending');\n                    userData.is_active = is_active;\n                    userData.is_pending = is_pending;\n                }\n                // Replace data with userData for the rest of the function\n                data = userData;\n            }\n            // For development/testing, use mock data if API call fails\n            try {\n                // Call the API to create the user\n                let response;\n                if (data instanceof FormData) {\n                    // Get user_type from FormData\n                    const formDataUserType = data.get('user_type');\n                    // Add password confirmation\n                    const password = data.get('password');\n                    if (password && !data.has('password2')) {\n                        data.append('password2', password);\n                    }\n                    // Add is_active and is_pending based on status if provided\n                    if (data.has('status')) {\n                        const status = data.get('status');\n                        if (status === 'active') {\n                            data.append('is_active', 'true');\n                            data.append('is_pending', 'false');\n                        } else if (status === 'inactive') {\n                            data.append('is_active', 'false');\n                            data.append('is_pending', 'false');\n                        } else if (status === 'pending') {\n                            data.append('is_active', 'false');\n                            data.append('is_pending', 'true');\n                        }\n                    } else {\n                        // Default to pending\n                        data.append('is_active', 'false');\n                        data.append('is_pending', 'true');\n                    }\n                    // Use the appropriate endpoint based on user type\n                    try {\n                        if (formDataUserType === 'assistant') {\n                            console.log('Creating assistant with FormData');\n                            response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post('/api/auth/assistants/create/', data, {\n                                headers: {\n                                    'Content-Type': 'multipart/form-data'\n                                }\n                            });\n                        } else if (formDataUserType === 'staff') {\n                            console.log('Creating staff with FormData');\n                            // Add the current doctor's ID for staff members too\n                            const doctorId = localStorage.getItem('userId');\n                            if (doctorId && !data.has('assigned_doctor')) {\n                                console.log('Adding assigned_doctor to FormData for staff:', doctorId);\n                                data.append('assigned_doctor', doctorId);\n                            }\n                            // Use the staff-specific endpoint\n                            console.log('Creating staff member with assigned doctor:', data.get('assigned_doctor'));\n                            response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post('/api/auth/staff/create/', data, {\n                                headers: {\n                                    'Content-Type': 'multipart/form-data'\n                                }\n                            });\n                        } else {\n                            console.log('Creating user with FormData');\n                            response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post('/api/auth/register/', data, {\n                                headers: {\n                                    'Content-Type': 'multipart/form-data'\n                                }\n                            });\n                        }\n                    } catch (error) {\n                        console.error('Error creating user:', error);\n                        throw error;\n                    }\n                } else {\n                    // Regular JSON data\n                    // Add password confirmation and status fields\n                    const userData = {\n                        ...data,\n                        password2: data.password\n                    };\n                    // Convert status to is_active and is_pending\n                    if (data.status) {\n                        if (data.status === 'active') {\n                            userData.is_active = true;\n                            userData.is_pending = false;\n                        } else if (data.status === 'inactive') {\n                            userData.is_active = false;\n                            userData.is_pending = false;\n                        } else if (data.status === 'pending') {\n                            userData.is_active = false;\n                            userData.is_pending = true;\n                        }\n                    } else {\n                        // Default to pending\n                        userData.is_active = false;\n                        userData.is_pending = true;\n                    }\n                    // Use the appropriate endpoint based on user type\n                    try {\n                        if (data.user_type === 'assistant') {\n                            console.log('Creating assistant with JSON data');\n                            response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post('/api/auth/assistants/create/', userData);\n                        } else if (data.user_type === 'staff') {\n                            console.log('Creating staff with JSON data');\n                            // Add the current doctor's ID for staff members too\n                            const doctorId = localStorage.getItem('userId');\n                            if (doctorId && !userData.assigned_doctor) {\n                                console.log('Adding assigned_doctor to userData for staff:', doctorId);\n                                userData.assigned_doctor = doctorId;\n                            }\n                            // Use the staff-specific endpoint\n                            console.log('Creating staff member with assigned doctor:', userData.assigned_doctor);\n                            response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post('/api/auth/staff/create/', userData);\n                        } else {\n                            console.log('Creating user with JSON data');\n                            response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post('/api/auth/register/', userData);\n                        }\n                    } catch (error) {\n                        console.error('Error creating user:', error);\n                        throw error;\n                    }\n                }\n                console.log('API response for user creation:', response.data);\n                // Use the status converter utility\n                const status = (0,_utils_statusConverter__WEBPACK_IMPORTED_MODULE_2__.backendToFrontendStatus)(response.data.is_active, response.data.is_pending);\n                // Log the status conversion for debugging\n                console.log(`New user ${response.data.email} status conversion:`, {\n                    backend: {\n                        is_active: response.data.is_active,\n                        is_pending: response.data.is_pending\n                    },\n                    frontend: status\n                });\n                // Transform the API response to match our UserAccount interface\n                const newUser = {\n                    id: response.data.id || response.data.uuid,\n                    email: response.data.email,\n                    first_name: response.data.first_name,\n                    last_name: response.data.last_name,\n                    phone_number: response.data.phone_number || '',\n                    user_type: response.data.user_type || (data instanceof FormData ? data.get('user_type') : data.user_type),\n                    status: status,\n                    created_at: response.data.created_at || response.data.date_joined || new Date().toISOString(),\n                    created_by: response.data.created_by || response.data.assigned_doctor_email || 'system',\n                    profile_image_url: response.data.profile_image_url || response.data.profile_image || null\n                };\n                // Log the profile image URL for debugging\n                console.log('Profile image URL for new user:', {\n                    profile_image_url: response.data.profile_image_url,\n                    profile_image: response.data.profile_image,\n                    final_url: newUser.profile_image_url,\n                    is_null: newUser.profile_image_url === null,\n                    is_undefined: newUser.profile_image_url === undefined,\n                    is_string_null: newUser.profile_image_url === 'null',\n                    is_string_undefined: newUser.profile_image_url === 'undefined',\n                    type: typeof newUser.profile_image_url\n                });\n                // Fix profile_image_url if it's a string 'null' or 'undefined'\n                if (newUser.profile_image_url === 'null' || newUser.profile_image_url === 'undefined') {\n                    console.log('Fixing profile_image_url that is a string \"null\" or \"undefined\"');\n                    newUser.profile_image_url = undefined;\n                }\n                // Update the cache with the new user\n                try {\n                    const cachedUsers = (0,_utils_mockDataStorage__WEBPACK_IMPORTED_MODULE_1__.getFromCache)(_utils_mockDataStorage__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.REAL_USER_ACCOUNTS, _utils_mockDataStorage__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.CACHE_TIMESTAMP_USER_ACCOUNTS);\n                    if (cachedUsers) {\n                        // Add the new user to the cached users\n                        const updatedUsers = [\n                            ...cachedUsers,\n                            newUser\n                        ];\n                        // Update the cache\n                        (0,_utils_mockDataStorage__WEBPACK_IMPORTED_MODULE_1__.storeInCache)(_utils_mockDataStorage__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.REAL_USER_ACCOUNTS, _utils_mockDataStorage__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.CACHE_TIMESTAMP_USER_ACCOUNTS, updatedUsers);\n                        console.log('Updated cache with new user');\n                    }\n                } catch (cacheError) {\n                    console.error('Error updating cache with new user:', cacheError);\n                }\n                return newUser;\n            } catch (apiError) {\n                console.error('API error creating user:', apiError);\n                // Fallback to mock data for development\n                if (false) {}\n                throw apiError;\n            }\n        } catch (error) {\n            console.error('Error creating user account:', error);\n            throw error;\n        }\n    },\n    async updateUserAccount (id, data) {\n        console.log(`=== UPDATING USER ACCOUNT ===`);\n        console.log(`User ID: ${id}`);\n        console.log(`Input data:`, JSON.stringify(data, null, 2));\n        // Check if we're using mock data first\n        if (false) {}\n        // If not using mock data, proceed with API calls\n        try {\n            let apiData;\n            // Check if data is FormData or regular object\n            if (data instanceof FormData) {\n                console.log('Data is FormData, using as is with some modifications');\n                apiData = data;\n                // Handle status conversion for FormData\n                const status = data.get('status');\n                if (status) {\n                    console.log(`Status conversion for FormData: \"${status}\" -> is_active/is_pending flags`);\n                    // Normalize status value to handle string case variations\n                    let normalizedStatus;\n                    if (typeof status === 'string') {\n                        const statusLower = status.toLowerCase();\n                        if (statusLower === 'active') {\n                            normalizedStatus = 'active';\n                        } else if (statusLower === 'pending') {\n                            normalizedStatus = 'pending';\n                        } else if (statusLower === 'inactive') {\n                            normalizedStatus = 'inactive';\n                        } else {\n                            console.warn(`Unknown status value: \"${status}\", defaulting to \"inactive\"`);\n                            normalizedStatus = 'inactive';\n                        }\n                    } else {\n                        console.warn(`Status is not a string: ${status}, defaulting to \"inactive\"`);\n                        normalizedStatus = 'inactive';\n                    }\n                    console.log(`Normalized status: \"${status}\" -> \"${normalizedStatus}\"`);\n                    // Use the status converter utility\n                    const { is_active, is_pending } = (0,_utils_statusConverter__WEBPACK_IMPORTED_MODULE_2__.frontendToBackendStatus)(normalizedStatus);\n                    // Log the status conversion for debugging\n                    console.log(`FormData update status conversion:`, {\n                        frontend: status,\n                        normalized: normalizedStatus,\n                        backend: {\n                            is_active,\n                            is_pending\n                        }\n                    });\n                    // Add the converted status to the FormData\n                    apiData.append('is_active', is_active.toString());\n                    apiData.append('is_pending', is_pending.toString());\n                    console.log(`Setting is_active=${is_active}, is_pending=${is_pending}`);\n                // Note: We can't delete fields from FormData, but the backend should ignore the status field\n                }\n                // If password is provided, add password confirmation and require password change\n                const password = data.get('password');\n                if (password && typeof password === 'string' && password.length > 0) {\n                    console.log('Password provided in FormData, adding confirmation and require_password_change flag');\n                    apiData.append('password2', password);\n                    apiData.append('require_password_change', 'false');\n                    // Log password details for debugging (without revealing the actual password)\n                    console.log('Password details:', {\n                        length: password.length,\n                        has_password2: apiData.has('password2'),\n                        has_require_password_change: apiData.has('require_password_change')\n                    });\n                }\n                console.log('FormData ready for submission with fields:', Array.from(apiData.keys()));\n            } else {\n                // Regular JSON object\n                apiData = {\n                    ...data\n                };\n                console.log('Initial API data (before conversion):', JSON.stringify(apiData, null, 2));\n                // Handle status conversion\n                if (apiData.status) {\n                    console.log(`Status conversion: \"${apiData.status}\" -> is_active/is_pending flags`);\n                    // Normalize status value to handle string case variations\n                    let normalizedStatus;\n                    if (typeof apiData.status === 'string') {\n                        const statusLower = apiData.status.toLowerCase();\n                        if (statusLower === 'active') {\n                            normalizedStatus = 'active';\n                        } else if (statusLower === 'pending') {\n                            normalizedStatus = 'pending';\n                        } else if (statusLower === 'inactive') {\n                            normalizedStatus = 'inactive';\n                        } else {\n                            console.warn(`Unknown status value: \"${apiData.status}\", defaulting to \"inactive\"`);\n                            normalizedStatus = 'inactive';\n                        }\n                    } else {\n                        console.warn(`Status is not a string: ${apiData.status}, defaulting to \"inactive\"`);\n                        normalizedStatus = 'inactive';\n                    }\n                    console.log(`Normalized status: \"${apiData.status}\" -> \"${normalizedStatus}\"`);\n                    // Use the status converter utility\n                    const { is_active, is_pending } = (0,_utils_statusConverter__WEBPACK_IMPORTED_MODULE_2__.frontendToBackendStatus)(normalizedStatus);\n                    // Log the status conversion for debugging\n                    console.log(`JSON update status conversion:`, {\n                        frontend: apiData.status,\n                        normalized: normalizedStatus,\n                        backend: {\n                            is_active,\n                            is_pending\n                        }\n                    });\n                    // Add the converted status to the apiData\n                    apiData.is_active = is_active;\n                    apiData.is_pending = is_pending;\n                    console.log(`Setting is_active=${is_active}, is_pending=${is_pending}`);\n                    // Remove status from the API data since backend doesn't have this field\n                    delete apiData.status;\n                    console.log('Status field removed from API data');\n                    console.log('Status conversion complete:', {\n                        is_active: apiData.is_active,\n                        is_pending: apiData.is_pending\n                    });\n                } else {\n                    console.log('No status field found in input data, skipping conversion');\n                }\n                // If password is provided, add password confirmation and require password change\n                if (apiData.password) {\n                    console.log('Password provided, adding confirmation and require_password_change flag');\n                    apiData.password2 = apiData.password; // Add confirmation password\n                    apiData.require_password_change = false; // Do not require password change on next login\n                    // Log password details for debugging (without revealing the actual password)\n                    console.log('Password details:', {\n                        length: typeof apiData.password === 'string' ? apiData.password.length : 'unknown',\n                        has_password2: !!apiData.password2,\n                        require_password_change: apiData.require_password_change\n                    });\n                } else {\n                    console.log('No password provided in update data');\n                }\n                console.log('Final API data ready for submission:', {\n                    ...apiData,\n                    password: apiData.password ? '[REDACTED]' : undefined,\n                    password2: apiData.password2 ? '[REDACTED]' : undefined\n                });\n            }\n            // Try to update the user via the API\n            let response;\n            // Set up headers for FormData if needed\n            const headers = {};\n            if (!(data instanceof FormData)) {\n                headers['Content-Type'] = 'application/json';\n            }\n            try {\n                // Determine the appropriate endpoint based on user_type\n                const userType = apiData instanceof FormData ? apiData.get('user_type') : apiData.user_type;\n                console.log(`User type for update: ${userType}`);\n                // Make sure user_type is included in the data\n                if (apiData instanceof FormData) {\n                    if (!apiData.has('user_type') && userType) {\n                        apiData.append('user_type', userType);\n                        console.log(`Added user_type=${userType} to FormData`);\n                    }\n                } else if (!apiData.user_type && userType) {\n                    apiData.user_type = userType;\n                    console.log(`Added user_type=${userType} to JSON data`);\n                }\n                if (userType === 'assistant') {\n                    // Try the assistants endpoint first for assistant users\n                    console.log(`ATTEMPT 1: Sending PATCH request to /api/auth/assistants/${id}/`);\n                    try {\n                        // Make sure assigned_doctor is included for assistants\n                        const doctorId = localStorage.getItem('userId');\n                        if (doctorId) {\n                            if (apiData instanceof FormData) {\n                                if (!apiData.has('assigned_doctor')) {\n                                    apiData.append('assigned_doctor', doctorId);\n                                    console.log(`Added assigned_doctor=${doctorId} to FormData for assistant update`);\n                                }\n                            } else {\n                                if (!apiData.assigned_doctor) {\n                                    apiData.assigned_doctor = doctorId;\n                                    console.log(`Added assigned_doctor=${doctorId} to JSON data for assistant update`);\n                                }\n                            }\n                        }\n                        // Add password2 if password is provided\n                        if (apiData instanceof FormData) {\n                            const password = apiData.get('password');\n                            if (password && !apiData.has('password2')) {\n                                apiData.append('password2', password);\n                                console.log('Added password2 to FormData for assistant update');\n                            }\n                            // Log all form data fields for debugging\n                            console.log('FormData fields for assistant update:', Array.from(apiData.entries()).map(([key])=>key));\n                            // Check if profile_image is present\n                            if (apiData.has('profile_image')) {\n                                const profileImage = apiData.get('profile_image');\n                                console.log('Profile image is present in FormData:', profileImage instanceof File ? `File: ${profileImage.name}, size: ${profileImage.size}` : `Type: ${typeof profileImage}`);\n                            } else {\n                                console.log('No profile_image in FormData');\n                            }\n                        } else if (apiData.password && !apiData.password2) {\n                            apiData.password2 = apiData.password;\n                            console.log('Added password2 to JSON data for assistant update');\n                            // Log JSON data for debugging\n                            console.log('JSON data for assistant update:', {\n                                ...apiData,\n                                password: apiData.password ? '[REDACTED]' : undefined,\n                                password2: apiData.password2 ? '[REDACTED]' : undefined\n                            });\n                        }\n                        // FormData content type is handled automatically by axios\n                        response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].patch(`/api/auth/assistants/${id}/`, apiData, {\n                            headers\n                        });\n                        console.log('ATTEMPT 1 SUCCESSFUL: Response received from assistants endpoint');\n                        // Log the response data for debugging\n                        console.log('Response data:', {\n                            ...response.data,\n                            profile_image: response.data.profile_image ? 'Present' : 'Not present',\n                            profile_image_url: response.data.profile_image_url\n                        });\n                    } catch (error) {\n                        const assistantError = error;\n                        console.log(`Assistant endpoint failed: ${assistantError.message || 'Unknown error'}`);\n                        console.log('Error details:', assistantError.response?.data || 'No response data');\n                        // Try the users endpoint as fallback\n                        console.log(`Trying users endpoint for assistant`);\n                        response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].patch(`/api/auth/users/${id}/`, apiData, {\n                            headers\n                        });\n                        console.log('SUCCESSFUL: Assistant updated via users endpoint');\n                    }\n                } else if (userType === 'staff') {\n                    // Try the staff endpoint first for staff users\n                    console.log(`ATTEMPT 1: Sending PATCH request to /api/auth/staff/${id}/`);\n                    try {\n                        response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].patch(`/api/auth/staff/${id}/`, apiData, {\n                            headers\n                        });\n                        console.log('ATTEMPT 1 SUCCESSFUL: Response received from staff endpoint');\n                    } catch (error) {\n                        const staffError = error;\n                        console.log(`Staff endpoint failed: ${staffError.message || 'Unknown error'}`);\n                        console.log('Error details:', staffError.response?.data || 'No response data');\n                        // Try the users endpoint as fallback\n                        console.log(`Trying users endpoint for staff`);\n                        response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].patch(`/api/auth/users/${id}/`, apiData, {\n                            headers\n                        });\n                        console.log('SUCCESSFUL: Staff updated via users endpoint');\n                    }\n                } else {\n                    // For other user types, try the users endpoint first\n                    console.log(`ATTEMPT 1: Sending PATCH request to /api/auth/users/${id}/`);\n                    response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].patch(`/api/auth/users/${id}/`, apiData, {\n                        headers\n                    });\n                    console.log('ATTEMPT 1 SUCCESSFUL: Response received from users endpoint');\n                }\n            } catch (error) {\n                const firstError = error;\n                console.log(`ATTEMPT 1 FAILED: Error from endpoint:`, firstError.message);\n                console.log('Error details:', firstError.response?.data || 'No response data');\n                try {\n                    // Try the general users endpoint as fallback\n                    console.log(`ATTEMPT 2: Sending PATCH request to /api/auth/users/${id}/`);\n                    response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].patch(`/api/auth/users/${id}/`, apiData, {\n                        headers\n                    });\n                    console.log('ATTEMPT 2 SUCCESSFUL: Response received from users endpoint');\n                } catch (error) {\n                    const secondError = error;\n                    console.log(`ATTEMPT 2 FAILED: Error from users endpoint:`, secondError.message);\n                    console.log('Error details:', secondError.response?.data || 'No response data');\n                    // Try the user endpoint\n                    console.log(`ATTEMPT 3: Sending PATCH request to /api/auth/user/${id}/`);\n                    try {\n                        response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].patch(`/api/auth/user/${id}/`, apiData, {\n                            headers\n                        });\n                        console.log('ATTEMPT 3 SUCCESSFUL: Response received from user endpoint');\n                    } catch (error) {\n                        const thirdError = error;\n                        console.log(`ATTEMPT 3 FAILED: Error from user endpoint:`, thirdError.message);\n                        console.log('Error details:', thirdError.response?.data || 'No response data');\n                        // Try the me endpoint as a last resort\n                        console.log(`ATTEMPT 4: Sending PATCH request to /api/auth/me/`);\n                        try {\n                            response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].patch(`/api/auth/me/`, apiData, {\n                                headers\n                            });\n                            console.log('ATTEMPT 4 SUCCESSFUL: Response received from me endpoint');\n                        } catch (error) {\n                            const fourthError = error;\n                            console.log(`ATTEMPT 4 FAILED: Error from me endpoint:`, fourthError.message);\n                            console.log('Error details:', fourthError.response?.data || 'No response data');\n                            // If all attempts failed, rethrow the original error\n                            throw firstError;\n                        }\n                    }\n                }\n            }\n            console.log('=== API RESPONSE RECEIVED ===');\n            console.log('Raw response data:', JSON.stringify(response.data, null, 2));\n            // Log the raw is_active and is_pending values for debugging\n            console.log('Raw is_active value:', response.data.is_active);\n            console.log('Raw is_active type:', typeof response.data.is_active);\n            console.log('Raw is_pending value:', response.data.is_pending);\n            console.log('Raw is_pending type:', typeof response.data.is_pending);\n            // For staff users, force is_active to be a boolean\n            let is_active = response.data.is_active;\n            let is_pending = response.data.is_pending;\n            if (response.data.user_type === 'staff') {\n                // Convert is_active to boolean if it's a string\n                if (typeof is_active === 'string') {\n                    is_active = is_active.toLowerCase() === 'true';\n                    console.log('Converted is_active string to boolean:', is_active);\n                }\n                // Convert is_pending to boolean if it's a string\n                if (typeof is_pending === 'string') {\n                    is_pending = is_pending.toLowerCase() === 'true';\n                    console.log('Converted is_pending string to boolean:', is_pending);\n                }\n                // Force is_active to be true if status was set to 'active'\n                if (apiData instanceof FormData) {\n                    const status = apiData.get('status');\n                    if (status === 'active') {\n                        is_active = true;\n                        is_pending = false;\n                        console.log('Forced is_active=true, is_pending=false based on FormData status=active');\n                    }\n                } else if (apiData.status === 'active') {\n                    is_active = true;\n                    is_pending = false;\n                    console.log('Forced is_active=true, is_pending=false based on JSON status=active');\n                }\n            }\n            // Check if the response already includes a status field\n            let userStatus;\n            if (response.data.status) {\n                userStatus = response.data.status;\n                console.log(`Using status directly from response: ${userStatus}`);\n            } else {\n                // Use the status converter utility with the potentially fixed values\n                userStatus = (0,_utils_statusConverter__WEBPACK_IMPORTED_MODULE_2__.backendToFrontendStatus)(is_active, is_pending);\n                console.log(`Converted status from is_active/is_pending: ${userStatus}`);\n            }\n            // Log the status conversion for debugging\n            console.log(`Updated user ${response.data.email} status conversion:`, {\n                backend: {\n                    original: {\n                        is_active: response.data.is_active,\n                        is_pending: response.data.is_pending\n                    },\n                    fixed: {\n                        is_active,\n                        is_pending\n                    }\n                },\n                response_status: response.data.status,\n                frontend: userStatus\n            });\n            // Create the updated user object\n            const updatedUser = {\n                id: response.data.id || response.data.uuid,\n                email: response.data.email,\n                first_name: response.data.first_name,\n                last_name: response.data.last_name,\n                phone_number: response.data.phone_number || '',\n                user_type: response.data.user_type || 'assistant',\n                status: userStatus,\n                // Add the corrected is_active and is_pending values\n                is_active: is_active,\n                is_pending: is_pending,\n                created_at: response.data.created_at || response.data.date_joined || new Date().toISOString(),\n                created_by: response.data.created_by || response.data.assigned_doctor_email || 'system',\n                profile_image_url: response.data.profile_image_url || response.data.profile_image || undefined\n            };\n            // Log the profile image URL for debugging\n            console.log('Profile image URL from response:', {\n                profile_image_url: response.data.profile_image_url,\n                profile_image: response.data.profile_image,\n                final_url: updatedUser.profile_image_url,\n                is_null: updatedUser.profile_image_url === null,\n                is_undefined: updatedUser.profile_image_url === undefined,\n                is_string_null: updatedUser.profile_image_url === 'null',\n                is_string_undefined: updatedUser.profile_image_url === 'undefined',\n                type: typeof updatedUser.profile_image_url\n            });\n            // Fix profile_image_url if it's a string 'null' or 'undefined'\n            if (updatedUser.profile_image_url === 'null' || updatedUser.profile_image_url === 'undefined') {\n                console.log('Fixing profile_image_url that is a string \"null\" or \"undefined\"');\n                updatedUser.profile_image_url = undefined;\n            }\n            console.log('Transformed user object:', updatedUser);\n            console.log('=== UPDATE SUCCESSFUL ===');\n            // Update the cache with the updated user\n            try {\n                const cachedUsers = (0,_utils_mockDataStorage__WEBPACK_IMPORTED_MODULE_1__.getFromCache)(_utils_mockDataStorage__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.REAL_USER_ACCOUNTS, _utils_mockDataStorage__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.CACHE_TIMESTAMP_USER_ACCOUNTS);\n                if (cachedUsers) {\n                    // Replace the user in the cached users\n                    const updatedUsers = cachedUsers.map((user)=>user.id === updatedUser.id ? updatedUser : user);\n                    // Update the cache\n                    (0,_utils_mockDataStorage__WEBPACK_IMPORTED_MODULE_1__.storeInCache)(_utils_mockDataStorage__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.REAL_USER_ACCOUNTS, _utils_mockDataStorage__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.CACHE_TIMESTAMP_USER_ACCOUNTS, updatedUsers);\n                    console.log('Updated cache with updated user');\n                }\n            } catch (cacheError) {\n                console.error('Error updating cache with updated user:', cacheError);\n            }\n            return updatedUser;\n        } catch (error) {\n            const apiError = error;\n            console.error('=== ERROR UPDATING USER ACCOUNT ===');\n            console.error('Error message:', apiError.message);\n            if (apiError.response) {\n                console.error('Response status:', apiError.response.status);\n                console.error('Response data:', apiError.response.data);\n                // Log detailed error information for debugging\n                if (apiError.response.data && typeof apiError.response.data === 'object') {\n                    Object.entries(apiError.response.data).forEach(([key, value])=>{\n                        console.error(`Field '${key}' error:`, value);\n                    });\n                }\n                // Check for specific error types\n                if (apiError.response.status === 400) {\n                    console.error('Validation error detected. Check field values.');\n                    // If there's a password error, log it specifically\n                    if (apiError.response.data && apiError.response.data.password) {\n                        console.error('Password error:', apiError.response.data.password);\n                    }\n                    // If there's an email error, log it specifically\n                    if (apiError.response.data && apiError.response.data.email) {\n                        console.error('Email error:', apiError.response.data.email);\n                    }\n                }\n            }\n            throw error;\n        }\n    },\n    async deleteUserAccount (id, userType) {\n        console.log(`=== DELETING USER ACCOUNT ===`);\n        console.log(`User ID: ${id}`);\n        console.log(`User Type (if provided): ${userType || 'not specified'}`);\n        // Check if we're using mock data first\n        if (false) {}\n        // If not using mock data, proceed with API calls\n        try {\n            // Try multiple endpoints based on user type\n            let success = false;\n            try {\n                // Try the appropriate endpoint based on user type\n                if (userType === 'assistant') {\n                    console.log(`Sending DELETE request to /api/auth/assistants/${id}/`);\n                    try {\n                        await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(`/api/auth/assistants/${id}/`);\n                        console.log('SUCCESSFUL: User deleted via assistants endpoint');\n                        success = true;\n                    } catch (error) {\n                        const assistantError = error;\n                        console.log(`Assistant endpoint failed: ${assistantError.message || 'Unknown error'}`);\n                        console.log('Error details:', assistantError.response?.data || 'No response data');\n                        // Check if it's a 404 error (assistant not found)\n                        if (assistantError.response?.status === 404) {\n                            console.log('Assistant not found, trying users endpoint');\n                            await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(`/api/auth/users/${id}/`);\n                            console.log('SUCCESSFUL: Assistant deleted via users endpoint');\n                            success = true;\n                        } else {\n                            // For other errors, rethrow\n                            throw assistantError;\n                        }\n                    }\n                } else if (userType === 'staff') {\n                    console.log(`Sending DELETE request to /api/auth/staff/${id}/`);\n                    // Make sure the staff endpoint exists and is correctly implemented\n                    try {\n                        await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(`/api/auth/staff/${id}/`);\n                        console.log('SUCCESSFUL: User deleted via staff endpoint');\n                        success = true;\n                    } catch (error) {\n                        const staffError = error;\n                        console.log(`Staff endpoint failed: ${staffError.message || 'Unknown error'}`);\n                        console.log('Error details:', staffError.response?.data || 'No response data');\n                        // Check if it's a 404 error (staff not found)\n                        if (staffError.response?.status === 404) {\n                            console.log('Staff not found, trying users endpoint');\n                            try {\n                                await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(`/api/auth/users/${id}/`);\n                                console.log('SUCCESSFUL: Staff deleted via users endpoint');\n                                success = true;\n                            } catch (error) {\n                                const userError = error;\n                                console.log(`Users endpoint failed: ${userError.message || 'Unknown error'}`);\n                                console.log('Error details:', userError.response?.data || 'No response data');\n                                throw error;\n                            }\n                        } else {\n                            // For other errors, rethrow\n                            throw staffError;\n                        }\n                    }\n                } else {\n                    // For other user types or if type is not specified, try the general endpoint\n                    console.log(`Sending DELETE request to /api/auth/users/${id}/`);\n                    await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(`/api/auth/users/${id}/`);\n                    console.log('SUCCESSFUL: User deleted via users endpoint');\n                    success = true;\n                }\n            } catch (error) {\n                const apiError = error;\n                console.log(`DELETE FAILED: Error:`, apiError.message);\n                console.log('Error details:', apiError.response?.data || 'No response data');\n                // Try a fallback approach with the me endpoint\n                try {\n                    console.log(`FALLBACK: Sending DELETE request to /api/auth/me/`);\n                    await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(`/api/auth/me/`);\n                    console.log('FALLBACK SUCCESSFUL: User deleted via me endpoint');\n                    success = true;\n                } catch (error) {\n                    const fallbackError = error;\n                    console.log(`FALLBACK FAILED: Error:`, fallbackError.message);\n                    console.log('Error details:', fallbackError.response?.data || 'No response data');\n                    // If all attempts failed, throw the original error\n                    throw error;\n                }\n            }\n            // If all attempts succeeded, update the cache\n            if (success) {\n                try {\n                    const cachedUsers = (0,_utils_mockDataStorage__WEBPACK_IMPORTED_MODULE_1__.getFromCache)(_utils_mockDataStorage__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.REAL_USER_ACCOUNTS, _utils_mockDataStorage__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.CACHE_TIMESTAMP_USER_ACCOUNTS);\n                    if (cachedUsers) {\n                        // Remove the user from the cached users\n                        const updatedUsers = cachedUsers.filter((user)=>user.id !== id);\n                        // Update the cache\n                        (0,_utils_mockDataStorage__WEBPACK_IMPORTED_MODULE_1__.storeInCache)(_utils_mockDataStorage__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.REAL_USER_ACCOUNTS, _utils_mockDataStorage__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.CACHE_TIMESTAMP_USER_ACCOUNTS, updatedUsers);\n                        console.log('Updated cache after user deletion');\n                    }\n                } catch (cacheError) {\n                    console.error('Error updating cache after user deletion:', cacheError);\n                }\n            }\n            console.log('=== DELETE SUCCESSFUL ===');\n            return success;\n        } catch (error) {\n            const apiError = error;\n            console.error('=== ERROR DELETING USER ACCOUNT ===');\n            console.error('Error message:', apiError.message);\n            if (apiError.response) {\n                console.error('Response status:', apiError.response.status);\n                console.error('Response data:', apiError.response.data);\n            }\n            throw error;\n        }\n    },\n    async getSubscriptionPackages () {\n        console.log('Mock getSubscriptionPackages called');\n        // Simulate API delay\n        await new Promise((resolve)=>setTimeout(resolve, 500));\n        // Get stored packages\n        const storedPackages = (0,_utils_mockDataStorage__WEBPACK_IMPORTED_MODULE_1__.getStoredData)('mock_subscription_packages', mockSubscriptionPackages);\n        console.log('Retrieved subscription packages from storage:', storedPackages);\n        return storedPackages;\n    },\n    async getCurrentSubscription () {\n        console.log('Mock getCurrentSubscription called');\n        // Simulate API delay\n        await new Promise((resolve)=>setTimeout(resolve, 500));\n        // Get stored packages\n        const storedPackages = (0,_utils_mockDataStorage__WEBPACK_IMPORTED_MODULE_1__.getStoredData)('mock_subscription_packages', mockSubscriptionPackages);\n        const currentPackage = storedPackages.find((pkg)=>pkg.is_current);\n        console.log('Retrieved current subscription from storage:', currentPackage);\n        return currentPackage || storedPackages[0];\n    },\n    async updateSubscription (packageId) {\n        console.log(`Mock updateSubscription called with packageId: ${packageId}`);\n        // Simulate API delay\n        await new Promise((resolve)=>setTimeout(resolve, 500));\n        // Get stored packages\n        const storedPackages = (0,_utils_mockDataStorage__WEBPACK_IMPORTED_MODULE_1__.getStoredData)('mock_subscription_packages', mockSubscriptionPackages);\n        // Reset all packages to not current\n        storedPackages.forEach((pkg)=>pkg.is_current = false);\n        // Find the package to update\n        const packageIndex = storedPackages.findIndex((pkg)=>pkg.id === packageId);\n        if (packageIndex === -1) throw new Error('Subscription package not found');\n        // Set the selected package as current\n        storedPackages[packageIndex].is_current = true;\n        // Update stored packages\n        (0,_utils_mockDataStorage__WEBPACK_IMPORTED_MODULE_1__.storeData)('mock_subscription_packages', storedPackages);\n        console.log('Subscription updated and stored successfully');\n        return storedPackages[packageIndex];\n    },\n    async getUserCounts () {\n        console.log('Fetching user counts');\n        try {\n            // Calculate counts from user accounts\n            const users = await this.getUserAccounts();\n            const assistantCount = users.filter((user)=>user.user_type === 'assistant').length;\n            const patientCount = users.filter((user)=>user.user_type === 'patient').length;\n            const staffCount = users.filter((user)=>user.user_type === 'staff').length;\n            console.log('User counts calculated:', {\n                assistants: assistantCount,\n                patients: patientCount,\n                staff: staffCount\n            });\n            return {\n                assistants: assistantCount,\n                patients: patientCount,\n                staff: staffCount\n            };\n        } catch (error) {\n            console.error('Error calculating user counts:', error);\n            // If all else fails, return default counts\n            return {\n                assistants: 0,\n                patients: 0,\n                staff: 0\n            };\n        }\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (userManagementService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/userManagementService.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/mockDataStorage.ts":
/*!**************************************!*\
  !*** ./src/utils/mockDataStorage.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STORAGE_KEYS: () => (/* binding */ STORAGE_KEYS),\n/* harmony export */   clearCache: () => (/* binding */ clearCache),\n/* harmony export */   clearStoredData: () => (/* binding */ clearStoredData),\n/* harmony export */   getFromCache: () => (/* binding */ getFromCache),\n/* harmony export */   getStoredData: () => (/* binding */ getStoredData),\n/* harmony export */   isCacheValid: () => (/* binding */ isCacheValid),\n/* harmony export */   storeData: () => (/* binding */ storeData),\n/* harmony export */   storeInCache: () => (/* binding */ storeInCache)\n/* harmony export */ });\n/**\n * Utility for persistent data storage using localStorage\n * This is used for both mock data and real data caching\n */ // Define storage keys\nconst STORAGE_KEYS = {\n    // Mock data keys\n    ASSISTANT_ACCOUNTS: 'mock_assistant_accounts',\n    SUBSCRIPTION_PACKAGES: 'mock_subscription_packages',\n    USER_COUNTS: 'mock_user_counts',\n    // Real data cache keys\n    REAL_USER_ACCOUNTS: 'real_user_accounts',\n    REAL_SUBSCRIPTION_PACKAGES: 'real_subscription_packages',\n    REAL_USER_COUNTS: 'real_user_counts',\n    REAL_CURRENT_SUBSCRIPTION: 'real_current_subscription',\n    // Cache timestamp keys\n    CACHE_TIMESTAMP_USER_ACCOUNTS: 'cache_timestamp_user_accounts',\n    CACHE_TIMESTAMP_SUBSCRIPTION_PACKAGES: 'cache_timestamp_subscription_packages',\n    CACHE_TIMESTAMP_USER_COUNTS: 'cache_timestamp_user_counts',\n    CACHE_TIMESTAMP_CURRENT_SUBSCRIPTION: 'cache_timestamp_current_subscription'\n};\n/**\n * Get data from localStorage with fallback to default value\n */ function getStoredData(key, defaultValue) {\n    if (true) {\n        return defaultValue;\n    }\n    try {\n        const storedData = localStorage.getItem(key);\n        if (!storedData) {\n            return defaultValue;\n        }\n        return JSON.parse(storedData);\n    } catch (error) {\n        console.error(`Error retrieving data from localStorage for key ${key}:`, error);\n        return defaultValue;\n    }\n}\n/**\n * Store data in localStorage\n */ function storeData(key, data) {\n    if (true) {\n        return;\n    }\n    try {\n        localStorage.setItem(key, JSON.stringify(data));\n    } catch (error) {\n        console.error(`Error storing data in localStorage for key ${key}:`, error);\n    }\n}\n/**\n * Clear stored data for a specific key\n */ function clearStoredData(key) {\n    if (true) {\n        return;\n    }\n    try {\n        localStorage.removeItem(key);\n    } catch (error) {\n        console.error(`Error clearing data from localStorage for key ${key}:`, error);\n    }\n}\n/**\n * Check if cached data is still valid (less than 5 minutes old)\n */ function isCacheValid(timestampKey, maxAgeMinutes = 5) {\n    if (true) {\n        return false;\n    }\n    try {\n        const timestamp = localStorage.getItem(timestampKey);\n        if (!timestamp) {\n            return false;\n        }\n        const storedTime = parseInt(timestamp, 10);\n        const currentTime = Date.now();\n        const ageInMinutes = (currentTime - storedTime) / (1000 * 60);\n        return ageInMinutes < maxAgeMinutes;\n    } catch (error) {\n        console.error(`Error checking cache validity for key ${timestampKey}:`, error);\n        return false;\n    }\n}\n/**\n * Store data in cache with timestamp\n */ function storeInCache(dataKey, timestampKey, data) {\n    if (true) {\n        return;\n    }\n    try {\n        // Store the data\n        localStorage.setItem(dataKey, JSON.stringify(data));\n        // Store the timestamp\n        localStorage.setItem(timestampKey, Date.now().toString());\n    } catch (error) {\n        console.error(`Error storing data in cache for key ${dataKey}:`, error);\n    }\n}\n/**\n * Get data from cache if valid, otherwise return null\n */ function getFromCache(dataKey, timestampKey, maxAgeMinutes = 5) {\n    if (true) {\n        return null;\n    }\n    try {\n        // Check if cache is valid\n        if (!isCacheValid(timestampKey, maxAgeMinutes)) {\n            return null;\n        }\n        // Get the data\n        const data = localStorage.getItem(dataKey);\n        if (!data) {\n            return null;\n        }\n        return JSON.parse(data);\n    } catch (error) {\n        console.error(`Error retrieving data from cache for key ${dataKey}:`, error);\n        return null;\n    }\n}\n/**\n * Clear all cache data\n */ function clearCache() {\n    if (true) {\n        return;\n    }\n    try {\n        // Clear all real data cache keys\n        Object.keys(STORAGE_KEYS).forEach((key)=>{\n            if (key.startsWith('REAL_') || key.startsWith('CACHE_TIMESTAMP_')) {\n                localStorage.removeItem(STORAGE_KEYS[key]);\n            }\n        });\n    } catch (error) {\n        console.error('Error clearing cache:', error);\n    }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/mockDataStorage.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/statusConverter.ts":
/*!**************************************!*\
  !*** ./src/utils/statusConverter.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   backendToFrontendStatus: () => (/* binding */ backendToFrontendStatus),\n/* harmony export */   frontendToBackendStatus: () => (/* binding */ frontendToBackendStatus)\n/* harmony export */ });\n/**\n * Utility functions for converting between backend and frontend status formats\n */ /**\n * Convert backend status flags to frontend status string\n *\n * @param is_active - Backend is_active flag\n * @param is_pending - Backend is_pending flag\n * @returns Frontend status string: 'active', 'inactive', or 'pending'\n */ function backendToFrontendStatus(is_active, is_pending) {\n    // Handle different types of values\n    let isActive;\n    let isPending;\n    // Handle is_active\n    if (is_active === undefined || is_active === null) {\n        isActive = false;\n    } else if (typeof is_active === 'boolean') {\n        isActive = is_active;\n    } else if (typeof is_active === 'string') {\n        // Convert string to boolean\n        isActive = is_active.toLowerCase() === 'true';\n    } else {\n        console.warn(`Unexpected is_active type: ${typeof is_active}, value: ${is_active}`);\n        isActive = false;\n    }\n    // Handle is_pending\n    if (is_pending === undefined || is_pending === null) {\n        isPending = false;\n    } else if (typeof is_pending === 'boolean') {\n        isPending = is_pending;\n    } else if (typeof is_pending === 'string') {\n        // Convert string to boolean\n        isPending = is_pending.toLowerCase() === 'true';\n    } else {\n        console.warn(`Unexpected is_pending type: ${typeof is_pending}, value: ${is_pending}`);\n        isPending = false;\n    }\n    // Log input values for debugging\n    console.log('backendToFrontendStatus input:', {\n        is_active,\n        is_pending,\n        is_active_type: typeof is_active,\n        is_pending_type: typeof is_pending,\n        is_active_after_handling: isActive,\n        is_pending_after_handling: isPending\n    });\n    // Convert to frontend status\n    let result;\n    if (isPending) {\n        result = 'pending';\n    } else if (isActive) {\n        result = 'active';\n    } else {\n        result = 'inactive';\n    }\n    // Log the result\n    console.log('backendToFrontendStatus result:', result);\n    return result;\n}\n/**\n * Convert frontend status string to backend status flags\n *\n * @param status - Frontend status string: 'active', 'inactive', or 'pending'\n * @returns Object with backend status flags: { is_active, is_pending }\n */ function frontendToBackendStatus(status) {\n    // Log input value for debugging\n    console.log('frontendToBackendStatus input:', {\n        status,\n        status_type: typeof status\n    });\n    let result;\n    switch(status){\n        case 'active':\n            result = {\n                is_active: true,\n                is_pending: false\n            };\n            break;\n        case 'pending':\n            result = {\n                is_active: false,\n                is_pending: true\n            };\n            break;\n        case 'inactive':\n        default:\n            result = {\n                is_active: false,\n                is_pending: false\n            };\n            break;\n    }\n    // Log the result\n    console.log('frontendToBackendStatus result:', result);\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/statusConverter.ts\n");

/***/ })

};
;