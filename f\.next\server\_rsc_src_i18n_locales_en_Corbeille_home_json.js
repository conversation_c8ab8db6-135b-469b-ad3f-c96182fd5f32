"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_i18n_locales_en_Corbeille_home_json";
exports.ids = ["_rsc_src_i18n_locales_en_Corbeille_home_json"];
exports.modules = {

/***/ "(rsc)/./src/i18n/locales/en/Corbeille/home.json":
/*!*************************************************!*\
  !*** ./src/i18n/locales/en/Corbeille/home.json ***!
  \*************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"metadata":{"Title":"Home | Find your doctor and make an appointment online","Description":"Medecinsvp is the first platform that takes into account your availability to organize an appointment with a health professional in less than an hour.","Description-Twitter":"With MedecinSvp, find a doctor, specialist or dentist and make an appointment online 100% free, fast and efficient.","Keywords":"Medecinsvp, online appointment, find a doctor, appointment, appointment, doctor, Morocco, Medecinsvp.com, General medicine, Morocco"},"typed-title":"We organize your appointments with health professionals.","tabs-Doctor":"Doctor","tabs-pharmacy":"Pharmacy","tabs-hospital":"Hospital","textLines-1":"Doctor please...","textLines-2":"Speciality...","textLines-3":"Consultation....","specialite-title-form-l":"Look for","specialite-title-form-r":"I am looking for a doctor","specialite-form-placeHolder":"doctor or specialty","specialite-form-placeHolders":"in the region or in the city","specialite-form-submit":"Find","pharmacy-title-form-l":"Look for","pharmacy-title-form-r":"Find a pharmacist","pharmacy-form-placeHolder":"in the district or in the city","pharmacy-form-placeHolders":"Al-Harasah pharmacy or ...","pharmacy-form-submit":"Find","hospital-title-form-l":"I\'m looking for a hospital","hospital-title-form-r":"public or private hospital","hospital-form-placeHolder":"in the region or city,","hospital-form-placeHolders":"Ibn Sina Hospital, Rabat","hospital-form-submit":"Find","collapseHero-title":"Our most sought-after specialties","General-Doctor":"General Doctor","collapseHero-speciality":"Dentist","collapseHero-pédiatre":"Pediatrician","collapseHero-nephrologue":"Nephrologist","collapseHero-orthodentiste":"Get orthodontic","collapseHero-neurologue":"Neurologist","collapseHero-psychiatrist":"Psychiatrist","collapseHero-gynecology":"Gynécologie","collapseHero-internal-medicine":"Internal Medicine","collapseHero-oncologist":"Oncologist","collapseHero-radiologist":"Radiologist","collapseHero-resuscitation-anesthesia":"Resuscitation Anesthesia","collapseHero-stomatology":"Stomatology","collapseHero-traumatology":"Traumatology","collapseHero-urology":"Urology","collapseHero-other":"Other","card-grid-title":"Discover online appointment booking ","card-grid-title-one":"Easily manage your availability","card-grid-title-two":"Reduce missed appointments","card-grid-title-three":"Provide an exceptional experience for all your patients","card-grid-title-General":"General medicine consultation","card-grid-title-General-description":"Pediatric consultation","title-G":"From","card-grid-title-Massages":"Massages and physiotherapy","card-grid-title-Massages-description":"Massages and physiotherapy","card-grid-title-Gynecological":"Gynecological consultation,obstetrics","card-grid-title-Gynecological-description":"Dental care","card-grid-title-doctors-specialists":"The best doctors and specialists","card-grid-title-doctors-specialists-description":"Identify doctors through thumbnails","card-grid-title-P":"A ","SliderSwiper.title":"Need a quick appointment?","SliderSwiper.title_description":"The professionals of the Medecinsvp network are passionate, dedicated and at your service","CarouselComment":{"title-h2":"The best doctors this month","title_p":"in the best medical specialties...","blockquote":"Thank you to everyone who trusted and supported by voting by clicking on the stars...","title_h4":"happy doctor","title_h3":"The number of people who believe in their doctors","Next":"Next","Previous":"Previous"},"testimonials":{"title_h3":"4 reasons to make your medical appointments with Medecinsvp","title_hh3":"Time saving","title_p":"The hours of waiting are over. You are received at the agreed time.","title_p1":"We verify the identity of all our healthcare professionals.","title_h5":"Security","title_h4":"Assistance","title_p2":"You are no longer alone. Doctor Svp organizes your appointment. ","title_h6":"Economy ","title_p5":"0 shipping costs. Make an appointment without moving. ","title_p6":"How it works "},"Stats":{"title":"Join with us","title_h3":"If you are a healthcare professional","title_p":"By offering your services and availability on the app, I gain visibility and increase my patient base by at least 30%.","title_h4":"Share your thoughts"},"CountUp":{"title_1":"Doctors ","title_2":"Hospital","title_3":"Pharmacy"},"Section":{"title_h2":"Protection of personal data.","title_p":"MedecinSVP is 100% compliant with regard to the protection of personal health data and uses the most secure technological standards. GDPR, HIPAA compliant and authorized by the CNDP."},"DarkModeIci":{"title_h2":"You want to know more? go here","title_p":"You want to know more?","Vaici":"go here"},"appointment-button":"Make an appointment","doctors-monthly-h2":"Vous pouvez choisir le type de spécialisation"}');

/***/ })

};
;