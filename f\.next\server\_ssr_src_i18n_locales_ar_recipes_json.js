"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_i18n_locales_ar_recipes_json";
exports.ids = ["_ssr_src_i18n_locales_ar_recipes_json"];
exports.modules = {

/***/ "(ssr)/./src/i18n/locales/ar/recipes.json":
/*!******************************************!*\
  !*** ./src/i18n/locales/ar/recipes.json ***!
  \******************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"metadata":{"Title":"الصفحة الرئيسية | ابحث عن طبيبك وحدد موعدًا عبر الإنترنت ","Description":"Medecinsvp هي المنصة الأولى التي تأخذ في الاعتبار مدى تواجدك لتنظيم موعد مع أخصائي صحي في أقل من ساعة.","Description-Twitter":"مع MedecinSvp، ابحث عن طبيب أو أخصائي أو طبيب أسنان وحدد موعدًا عبر الإنترنت مجانًا وسريع وفعال بنسبة 100%.","Keywords":"Medecinsvp, موعد عبر الإنترنت, العثور على طبيب, موعد, موعد, طبيب, المغرب, Medecinsvp.com, الطب العام, المغرب"}}');

/***/ })

};
;