"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/simplebar-core";
exports.ids = ["vendor-chunks/simplebar-core"];
exports.modules = {

/***/ "(ssr)/./node_modules/simplebar-core/dist/index.mjs":
/*!****************************************************!*\
  !*** ./node_modules/simplebar-core/dist/index.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleBarCore)\n/* harmony export */ });\n/* harmony import */ var lodash_es_debounce_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash-es/debounce.js */ \"(ssr)/./node_modules/lodash-es/debounce.js\");\n/* harmony import */ var lodash_es_throttle_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lodash-es/throttle.js */ \"(ssr)/./node_modules/lodash-es/throttle.js\");\n/**\n * simplebar-core - v1.3.2\n * Scrollbars, simpler.\n * https://grsmto.github.io/simplebar/\n *\n * Made by Adrien Denat from a fork by Jonathan Nicol\n * Under MIT License\n */\n\n\n\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\n\nfunction getElementWindow$1(element) {\n    if (!element ||\n        !element.ownerDocument ||\n        !element.ownerDocument.defaultView) {\n        return window;\n    }\n    return element.ownerDocument.defaultView;\n}\nfunction getElementDocument$1(element) {\n    if (!element || !element.ownerDocument) {\n        return document;\n    }\n    return element.ownerDocument;\n}\n// Helper function to retrieve options from element attributes\nvar getOptions$1 = function (obj) {\n    var initialObj = {};\n    var options = Array.prototype.reduce.call(obj, function (acc, attribute) {\n        var option = attribute.name.match(/data-simplebar-(.+)/);\n        if (option) {\n            var key = option[1].replace(/\\W+(.)/g, function (_, chr) { return chr.toUpperCase(); });\n            switch (attribute.value) {\n                case 'true':\n                    acc[key] = true;\n                    break;\n                case 'false':\n                    acc[key] = false;\n                    break;\n                case undefined:\n                    acc[key] = true;\n                    break;\n                default:\n                    acc[key] = attribute.value;\n            }\n        }\n        return acc;\n    }, initialObj);\n    return options;\n};\nfunction addClasses$1(el, classes) {\n    var _a;\n    if (!el)\n        return;\n    (_a = el.classList).add.apply(_a, classes.split(' '));\n}\nfunction removeClasses$1(el, classes) {\n    if (!el)\n        return;\n    classes.split(' ').forEach(function (className) {\n        el.classList.remove(className);\n    });\n}\nfunction classNamesToQuery$1(classNames) {\n    return \".\".concat(classNames.split(' ').join('.'));\n}\nvar canUseDOM = !!(typeof window !== 'undefined' &&\n    window.document &&\n    window.document.createElement);\n\nvar helpers = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    addClasses: addClasses$1,\n    canUseDOM: canUseDOM,\n    classNamesToQuery: classNamesToQuery$1,\n    getElementDocument: getElementDocument$1,\n    getElementWindow: getElementWindow$1,\n    getOptions: getOptions$1,\n    removeClasses: removeClasses$1\n});\n\nvar cachedScrollbarWidth = null;\nvar cachedDevicePixelRatio = null;\nif (canUseDOM) {\n    window.addEventListener('resize', function () {\n        if (cachedDevicePixelRatio !== window.devicePixelRatio) {\n            cachedDevicePixelRatio = window.devicePixelRatio;\n            cachedScrollbarWidth = null;\n        }\n    });\n}\nfunction scrollbarWidth() {\n    if (cachedScrollbarWidth === null) {\n        if (typeof document === 'undefined') {\n            cachedScrollbarWidth = 0;\n            return cachedScrollbarWidth;\n        }\n        var body = document.body;\n        var box = document.createElement('div');\n        box.classList.add('simplebar-hide-scrollbar');\n        body.appendChild(box);\n        var width = box.getBoundingClientRect().right;\n        body.removeChild(box);\n        cachedScrollbarWidth = width;\n    }\n    return cachedScrollbarWidth;\n}\n\nvar getElementWindow = getElementWindow$1, getElementDocument = getElementDocument$1, getOptions = getOptions$1, addClasses = addClasses$1, removeClasses = removeClasses$1, classNamesToQuery = classNamesToQuery$1;\nvar SimpleBarCore = /** @class */ (function () {\n    function SimpleBarCore(element, options) {\n        if (options === void 0) { options = {}; }\n        var _this = this;\n        this.removePreventClickId = null;\n        this.minScrollbarWidth = 20;\n        this.stopScrollDelay = 175;\n        this.isScrolling = false;\n        this.isMouseEntering = false;\n        this.isDragging = false;\n        this.scrollXTicking = false;\n        this.scrollYTicking = false;\n        this.wrapperEl = null;\n        this.contentWrapperEl = null;\n        this.contentEl = null;\n        this.offsetEl = null;\n        this.maskEl = null;\n        this.placeholderEl = null;\n        this.heightAutoObserverWrapperEl = null;\n        this.heightAutoObserverEl = null;\n        this.rtlHelpers = null;\n        this.scrollbarWidth = 0;\n        this.resizeObserver = null;\n        this.mutationObserver = null;\n        this.elStyles = null;\n        this.isRtl = null;\n        this.mouseX = 0;\n        this.mouseY = 0;\n        this.onMouseMove = function () { };\n        this.onWindowResize = function () { };\n        this.onStopScrolling = function () { };\n        this.onMouseEntered = function () { };\n        /**\n         * On scroll event handling\n         */\n        this.onScroll = function () {\n            var elWindow = getElementWindow(_this.el);\n            if (!_this.scrollXTicking) {\n                elWindow.requestAnimationFrame(_this.scrollX);\n                _this.scrollXTicking = true;\n            }\n            if (!_this.scrollYTicking) {\n                elWindow.requestAnimationFrame(_this.scrollY);\n                _this.scrollYTicking = true;\n            }\n            if (!_this.isScrolling) {\n                _this.isScrolling = true;\n                addClasses(_this.el, _this.classNames.scrolling);\n            }\n            _this.showScrollbar('x');\n            _this.showScrollbar('y');\n            _this.onStopScrolling();\n        };\n        this.scrollX = function () {\n            if (_this.axis.x.isOverflowing) {\n                _this.positionScrollbar('x');\n            }\n            _this.scrollXTicking = false;\n        };\n        this.scrollY = function () {\n            if (_this.axis.y.isOverflowing) {\n                _this.positionScrollbar('y');\n            }\n            _this.scrollYTicking = false;\n        };\n        this._onStopScrolling = function () {\n            removeClasses(_this.el, _this.classNames.scrolling);\n            if (_this.options.autoHide) {\n                _this.hideScrollbar('x');\n                _this.hideScrollbar('y');\n            }\n            _this.isScrolling = false;\n        };\n        this.onMouseEnter = function () {\n            if (!_this.isMouseEntering) {\n                addClasses(_this.el, _this.classNames.mouseEntered);\n                _this.showScrollbar('x');\n                _this.showScrollbar('y');\n                _this.isMouseEntering = true;\n            }\n            _this.onMouseEntered();\n        };\n        this._onMouseEntered = function () {\n            removeClasses(_this.el, _this.classNames.mouseEntered);\n            if (_this.options.autoHide) {\n                _this.hideScrollbar('x');\n                _this.hideScrollbar('y');\n            }\n            _this.isMouseEntering = false;\n        };\n        this._onMouseMove = function (e) {\n            _this.mouseX = e.clientX;\n            _this.mouseY = e.clientY;\n            if (_this.axis.x.isOverflowing || _this.axis.x.forceVisible) {\n                _this.onMouseMoveForAxis('x');\n            }\n            if (_this.axis.y.isOverflowing || _this.axis.y.forceVisible) {\n                _this.onMouseMoveForAxis('y');\n            }\n        };\n        this.onMouseLeave = function () {\n            _this.onMouseMove.cancel();\n            if (_this.axis.x.isOverflowing || _this.axis.x.forceVisible) {\n                _this.onMouseLeaveForAxis('x');\n            }\n            if (_this.axis.y.isOverflowing || _this.axis.y.forceVisible) {\n                _this.onMouseLeaveForAxis('y');\n            }\n            _this.mouseX = -1;\n            _this.mouseY = -1;\n        };\n        this._onWindowResize = function () {\n            // Recalculate scrollbarWidth in case it's a zoom\n            _this.scrollbarWidth = _this.getScrollbarWidth();\n            _this.hideNativeScrollbar();\n        };\n        this.onPointerEvent = function (e) {\n            if (!_this.axis.x.track.el ||\n                !_this.axis.y.track.el ||\n                !_this.axis.x.scrollbar.el ||\n                !_this.axis.y.scrollbar.el)\n                return;\n            var isWithinTrackXBounds, isWithinTrackYBounds;\n            _this.axis.x.track.rect = _this.axis.x.track.el.getBoundingClientRect();\n            _this.axis.y.track.rect = _this.axis.y.track.el.getBoundingClientRect();\n            if (_this.axis.x.isOverflowing || _this.axis.x.forceVisible) {\n                isWithinTrackXBounds = _this.isWithinBounds(_this.axis.x.track.rect);\n            }\n            if (_this.axis.y.isOverflowing || _this.axis.y.forceVisible) {\n                isWithinTrackYBounds = _this.isWithinBounds(_this.axis.y.track.rect);\n            }\n            // If any pointer event is called on the scrollbar\n            if (isWithinTrackXBounds || isWithinTrackYBounds) {\n                // Prevent event leaking\n                e.stopPropagation();\n                if (e.type === 'pointerdown' && e.pointerType !== 'touch') {\n                    if (isWithinTrackXBounds) {\n                        _this.axis.x.scrollbar.rect =\n                            _this.axis.x.scrollbar.el.getBoundingClientRect();\n                        if (_this.isWithinBounds(_this.axis.x.scrollbar.rect)) {\n                            _this.onDragStart(e, 'x');\n                        }\n                        else {\n                            _this.onTrackClick(e, 'x');\n                        }\n                    }\n                    if (isWithinTrackYBounds) {\n                        _this.axis.y.scrollbar.rect =\n                            _this.axis.y.scrollbar.el.getBoundingClientRect();\n                        if (_this.isWithinBounds(_this.axis.y.scrollbar.rect)) {\n                            _this.onDragStart(e, 'y');\n                        }\n                        else {\n                            _this.onTrackClick(e, 'y');\n                        }\n                    }\n                }\n            }\n        };\n        /**\n         * Drag scrollbar handle\n         */\n        this.drag = function (e) {\n            var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;\n            if (!_this.draggedAxis || !_this.contentWrapperEl)\n                return;\n            var eventOffset;\n            var track = _this.axis[_this.draggedAxis].track;\n            var trackSize = (_b = (_a = track.rect) === null || _a === void 0 ? void 0 : _a[_this.axis[_this.draggedAxis].sizeAttr]) !== null && _b !== void 0 ? _b : 0;\n            var scrollbar = _this.axis[_this.draggedAxis].scrollbar;\n            var contentSize = (_d = (_c = _this.contentWrapperEl) === null || _c === void 0 ? void 0 : _c[_this.axis[_this.draggedAxis].scrollSizeAttr]) !== null && _d !== void 0 ? _d : 0;\n            var hostSize = parseInt((_f = (_e = _this.elStyles) === null || _e === void 0 ? void 0 : _e[_this.axis[_this.draggedAxis].sizeAttr]) !== null && _f !== void 0 ? _f : '0px', 10);\n            e.preventDefault();\n            e.stopPropagation();\n            if (_this.draggedAxis === 'y') {\n                eventOffset = e.pageY;\n            }\n            else {\n                eventOffset = e.pageX;\n            }\n            // Calculate how far the user's mouse is from the top/left of the scrollbar (minus the dragOffset).\n            var dragPos = eventOffset -\n                ((_h = (_g = track.rect) === null || _g === void 0 ? void 0 : _g[_this.axis[_this.draggedAxis].offsetAttr]) !== null && _h !== void 0 ? _h : 0) -\n                _this.axis[_this.draggedAxis].dragOffset;\n            dragPos =\n                _this.draggedAxis === 'x' && _this.isRtl\n                    ? ((_k = (_j = track.rect) === null || _j === void 0 ? void 0 : _j[_this.axis[_this.draggedAxis].sizeAttr]) !== null && _k !== void 0 ? _k : 0) -\n                        scrollbar.size -\n                        dragPos\n                    : dragPos;\n            // Convert the mouse position into a percentage of the scrollbar height/width.\n            var dragPerc = dragPos / (trackSize - scrollbar.size);\n            // Scroll the content by the same percentage.\n            var scrollPos = dragPerc * (contentSize - hostSize);\n            // Fix browsers inconsistency on RTL\n            if (_this.draggedAxis === 'x' && _this.isRtl) {\n                scrollPos = ((_l = SimpleBarCore.getRtlHelpers()) === null || _l === void 0 ? void 0 : _l.isScrollingToNegative)\n                    ? -scrollPos\n                    : scrollPos;\n            }\n            _this.contentWrapperEl[_this.axis[_this.draggedAxis].scrollOffsetAttr] =\n                scrollPos;\n        };\n        /**\n         * End scroll handle drag\n         */\n        this.onEndDrag = function (e) {\n            _this.isDragging = false;\n            var elDocument = getElementDocument(_this.el);\n            var elWindow = getElementWindow(_this.el);\n            e.preventDefault();\n            e.stopPropagation();\n            removeClasses(_this.el, _this.classNames.dragging);\n            _this.onStopScrolling();\n            elDocument.removeEventListener('mousemove', _this.drag, true);\n            elDocument.removeEventListener('mouseup', _this.onEndDrag, true);\n            _this.removePreventClickId = elWindow.setTimeout(function () {\n                // Remove these asynchronously so we still suppress click events\n                // generated simultaneously with mouseup.\n                elDocument.removeEventListener('click', _this.preventClick, true);\n                elDocument.removeEventListener('dblclick', _this.preventClick, true);\n                _this.removePreventClickId = null;\n            });\n        };\n        /**\n         * Handler to ignore click events during drag\n         */\n        this.preventClick = function (e) {\n            e.preventDefault();\n            e.stopPropagation();\n        };\n        this.el = element;\n        this.options = __assign(__assign({}, SimpleBarCore.defaultOptions), options);\n        this.classNames = __assign(__assign({}, SimpleBarCore.defaultOptions.classNames), options.classNames);\n        this.axis = {\n            x: {\n                scrollOffsetAttr: 'scrollLeft',\n                sizeAttr: 'width',\n                scrollSizeAttr: 'scrollWidth',\n                offsetSizeAttr: 'offsetWidth',\n                offsetAttr: 'left',\n                overflowAttr: 'overflowX',\n                dragOffset: 0,\n                isOverflowing: true,\n                forceVisible: false,\n                track: { size: null, el: null, rect: null, isVisible: false },\n                scrollbar: { size: null, el: null, rect: null, isVisible: false }\n            },\n            y: {\n                scrollOffsetAttr: 'scrollTop',\n                sizeAttr: 'height',\n                scrollSizeAttr: 'scrollHeight',\n                offsetSizeAttr: 'offsetHeight',\n                offsetAttr: 'top',\n                overflowAttr: 'overflowY',\n                dragOffset: 0,\n                isOverflowing: true,\n                forceVisible: false,\n                track: { size: null, el: null, rect: null, isVisible: false },\n                scrollbar: { size: null, el: null, rect: null, isVisible: false }\n            }\n        };\n        if (typeof this.el !== 'object' || !this.el.nodeName) {\n            throw new Error(\"Argument passed to SimpleBar must be an HTML element instead of \".concat(this.el));\n        }\n        this.onMouseMove = (0,lodash_es_throttle_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this._onMouseMove, 64);\n        this.onWindowResize = (0,lodash_es_debounce_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(this._onWindowResize, 64, { leading: true });\n        this.onStopScrolling = (0,lodash_es_debounce_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(this._onStopScrolling, this.stopScrollDelay);\n        this.onMouseEntered = (0,lodash_es_debounce_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(this._onMouseEntered, this.stopScrollDelay);\n        this.init();\n    }\n    /**\n     * Helper to fix browsers inconsistency on RTL:\n     *  - Firefox inverts the scrollbar initial position\n     *  - IE11 inverts both scrollbar position and scrolling offset\n     * Directly inspired by @KingSora's OverlayScrollbars https://github.com/KingSora/OverlayScrollbars/blob/master/js/OverlayScrollbars.js#L1634\n     */\n    SimpleBarCore.getRtlHelpers = function () {\n        if (SimpleBarCore.rtlHelpers) {\n            return SimpleBarCore.rtlHelpers;\n        }\n        var dummyDiv = document.createElement('div');\n        dummyDiv.innerHTML =\n            '<div class=\"simplebar-dummy-scrollbar-size\"><div></div></div>';\n        var scrollbarDummyEl = dummyDiv.firstElementChild;\n        var dummyChild = scrollbarDummyEl === null || scrollbarDummyEl === void 0 ? void 0 : scrollbarDummyEl.firstElementChild;\n        if (!dummyChild)\n            return null;\n        document.body.appendChild(scrollbarDummyEl);\n        scrollbarDummyEl.scrollLeft = 0;\n        var dummyContainerOffset = SimpleBarCore.getOffset(scrollbarDummyEl);\n        var dummyChildOffset = SimpleBarCore.getOffset(dummyChild);\n        scrollbarDummyEl.scrollLeft = -999;\n        var dummyChildOffsetAfterScroll = SimpleBarCore.getOffset(dummyChild);\n        document.body.removeChild(scrollbarDummyEl);\n        SimpleBarCore.rtlHelpers = {\n            // determines if the scrolling is responding with negative values\n            isScrollOriginAtZero: dummyContainerOffset.left !== dummyChildOffset.left,\n            // determines if the origin scrollbar position is inverted or not (positioned on left or right)\n            isScrollingToNegative: dummyChildOffset.left !== dummyChildOffsetAfterScroll.left\n        };\n        return SimpleBarCore.rtlHelpers;\n    };\n    SimpleBarCore.prototype.getScrollbarWidth = function () {\n        // Try/catch for FF 56 throwing on undefined computedStyles\n        try {\n            // Detect browsers supporting CSS scrollbar styling and do not calculate\n            if ((this.contentWrapperEl &&\n                getComputedStyle(this.contentWrapperEl, '::-webkit-scrollbar')\n                    .display === 'none') ||\n                'scrollbarWidth' in document.documentElement.style ||\n                '-ms-overflow-style' in document.documentElement.style) {\n                return 0;\n            }\n            else {\n                return scrollbarWidth();\n            }\n        }\n        catch (e) {\n            return scrollbarWidth();\n        }\n    };\n    SimpleBarCore.getOffset = function (el) {\n        var rect = el.getBoundingClientRect();\n        var elDocument = getElementDocument(el);\n        var elWindow = getElementWindow(el);\n        return {\n            top: rect.top +\n                (elWindow.pageYOffset || elDocument.documentElement.scrollTop),\n            left: rect.left +\n                (elWindow.pageXOffset || elDocument.documentElement.scrollLeft)\n        };\n    };\n    SimpleBarCore.prototype.init = function () {\n        // We stop here on server-side\n        if (canUseDOM) {\n            this.initDOM();\n            this.rtlHelpers = SimpleBarCore.getRtlHelpers();\n            this.scrollbarWidth = this.getScrollbarWidth();\n            this.recalculate();\n            this.initListeners();\n        }\n    };\n    SimpleBarCore.prototype.initDOM = function () {\n        var _a, _b;\n        // assume that element has his DOM already initiated\n        this.wrapperEl = this.el.querySelector(classNamesToQuery(this.classNames.wrapper));\n        this.contentWrapperEl =\n            this.options.scrollableNode ||\n                this.el.querySelector(classNamesToQuery(this.classNames.contentWrapper));\n        this.contentEl =\n            this.options.contentNode ||\n                this.el.querySelector(classNamesToQuery(this.classNames.contentEl));\n        this.offsetEl = this.el.querySelector(classNamesToQuery(this.classNames.offset));\n        this.maskEl = this.el.querySelector(classNamesToQuery(this.classNames.mask));\n        this.placeholderEl = this.findChild(this.wrapperEl, classNamesToQuery(this.classNames.placeholder));\n        this.heightAutoObserverWrapperEl = this.el.querySelector(classNamesToQuery(this.classNames.heightAutoObserverWrapperEl));\n        this.heightAutoObserverEl = this.el.querySelector(classNamesToQuery(this.classNames.heightAutoObserverEl));\n        this.axis.x.track.el = this.findChild(this.el, \"\".concat(classNamesToQuery(this.classNames.track)).concat(classNamesToQuery(this.classNames.horizontal)));\n        this.axis.y.track.el = this.findChild(this.el, \"\".concat(classNamesToQuery(this.classNames.track)).concat(classNamesToQuery(this.classNames.vertical)));\n        this.axis.x.scrollbar.el =\n            ((_a = this.axis.x.track.el) === null || _a === void 0 ? void 0 : _a.querySelector(classNamesToQuery(this.classNames.scrollbar))) || null;\n        this.axis.y.scrollbar.el =\n            ((_b = this.axis.y.track.el) === null || _b === void 0 ? void 0 : _b.querySelector(classNamesToQuery(this.classNames.scrollbar))) || null;\n        if (!this.options.autoHide) {\n            addClasses(this.axis.x.scrollbar.el, this.classNames.visible);\n            addClasses(this.axis.y.scrollbar.el, this.classNames.visible);\n        }\n    };\n    SimpleBarCore.prototype.initListeners = function () {\n        var _this = this;\n        var _a;\n        var elWindow = getElementWindow(this.el);\n        // Event listeners\n        this.el.addEventListener('mouseenter', this.onMouseEnter);\n        this.el.addEventListener('pointerdown', this.onPointerEvent, true);\n        this.el.addEventListener('mousemove', this.onMouseMove);\n        this.el.addEventListener('mouseleave', this.onMouseLeave);\n        (_a = this.contentWrapperEl) === null || _a === void 0 ? void 0 : _a.addEventListener('scroll', this.onScroll);\n        // Browser zoom triggers a window resize\n        elWindow.addEventListener('resize', this.onWindowResize);\n        if (!this.contentEl)\n            return;\n        if (window.ResizeObserver) {\n            // Hack for https://github.com/WICG/ResizeObserver/issues/38\n            var resizeObserverStarted_1 = false;\n            var resizeObserver = elWindow.ResizeObserver || ResizeObserver;\n            this.resizeObserver = new resizeObserver(function () {\n                if (!resizeObserverStarted_1)\n                    return;\n                elWindow.requestAnimationFrame(function () {\n                    _this.recalculate();\n                });\n            });\n            this.resizeObserver.observe(this.el);\n            this.resizeObserver.observe(this.contentEl);\n            elWindow.requestAnimationFrame(function () {\n                resizeObserverStarted_1 = true;\n            });\n        }\n        // This is required to detect horizontal scroll. Vertical scroll only needs the resizeObserver.\n        this.mutationObserver = new elWindow.MutationObserver(function () {\n            elWindow.requestAnimationFrame(function () {\n                _this.recalculate();\n            });\n        });\n        this.mutationObserver.observe(this.contentEl, {\n            childList: true,\n            subtree: true,\n            characterData: true\n        });\n    };\n    SimpleBarCore.prototype.recalculate = function () {\n        if (!this.heightAutoObserverEl ||\n            !this.contentEl ||\n            !this.contentWrapperEl ||\n            !this.wrapperEl ||\n            !this.placeholderEl)\n            return;\n        var elWindow = getElementWindow(this.el);\n        this.elStyles = elWindow.getComputedStyle(this.el);\n        this.isRtl = this.elStyles.direction === 'rtl';\n        var contentElOffsetWidth = this.contentEl.offsetWidth;\n        var isHeightAuto = this.heightAutoObserverEl.offsetHeight <= 1;\n        var isWidthAuto = this.heightAutoObserverEl.offsetWidth <= 1 || contentElOffsetWidth > 0;\n        var contentWrapperElOffsetWidth = this.contentWrapperEl.offsetWidth;\n        var elOverflowX = this.elStyles.overflowX;\n        var elOverflowY = this.elStyles.overflowY;\n        this.contentEl.style.padding = \"\".concat(this.elStyles.paddingTop, \" \").concat(this.elStyles.paddingRight, \" \").concat(this.elStyles.paddingBottom, \" \").concat(this.elStyles.paddingLeft);\n        this.wrapperEl.style.margin = \"-\".concat(this.elStyles.paddingTop, \" -\").concat(this.elStyles.paddingRight, \" -\").concat(this.elStyles.paddingBottom, \" -\").concat(this.elStyles.paddingLeft);\n        var contentElScrollHeight = this.contentEl.scrollHeight;\n        var contentElScrollWidth = this.contentEl.scrollWidth;\n        this.contentWrapperEl.style.height = isHeightAuto ? 'auto' : '100%';\n        // Determine placeholder size\n        this.placeholderEl.style.width = isWidthAuto\n            ? \"\".concat(contentElOffsetWidth || contentElScrollWidth, \"px\")\n            : 'auto';\n        this.placeholderEl.style.height = \"\".concat(contentElScrollHeight, \"px\");\n        var contentWrapperElOffsetHeight = this.contentWrapperEl.offsetHeight;\n        this.axis.x.isOverflowing =\n            contentElOffsetWidth !== 0 && contentElScrollWidth > contentElOffsetWidth;\n        this.axis.y.isOverflowing =\n            contentElScrollHeight > contentWrapperElOffsetHeight;\n        // Set isOverflowing to false if user explicitely set hidden overflow\n        this.axis.x.isOverflowing =\n            elOverflowX === 'hidden' ? false : this.axis.x.isOverflowing;\n        this.axis.y.isOverflowing =\n            elOverflowY === 'hidden' ? false : this.axis.y.isOverflowing;\n        this.axis.x.forceVisible =\n            this.options.forceVisible === 'x' || this.options.forceVisible === true;\n        this.axis.y.forceVisible =\n            this.options.forceVisible === 'y' || this.options.forceVisible === true;\n        this.hideNativeScrollbar();\n        // Set isOverflowing to false if scrollbar is not necessary (content is shorter than offset)\n        var offsetForXScrollbar = this.axis.x.isOverflowing\n            ? this.scrollbarWidth\n            : 0;\n        var offsetForYScrollbar = this.axis.y.isOverflowing\n            ? this.scrollbarWidth\n            : 0;\n        this.axis.x.isOverflowing =\n            this.axis.x.isOverflowing &&\n                contentElScrollWidth > contentWrapperElOffsetWidth - offsetForYScrollbar;\n        this.axis.y.isOverflowing =\n            this.axis.y.isOverflowing &&\n                contentElScrollHeight >\n                    contentWrapperElOffsetHeight - offsetForXScrollbar;\n        this.axis.x.scrollbar.size = this.getScrollbarSize('x');\n        this.axis.y.scrollbar.size = this.getScrollbarSize('y');\n        if (this.axis.x.scrollbar.el)\n            this.axis.x.scrollbar.el.style.width = \"\".concat(this.axis.x.scrollbar.size, \"px\");\n        if (this.axis.y.scrollbar.el)\n            this.axis.y.scrollbar.el.style.height = \"\".concat(this.axis.y.scrollbar.size, \"px\");\n        this.positionScrollbar('x');\n        this.positionScrollbar('y');\n        this.toggleTrackVisibility('x');\n        this.toggleTrackVisibility('y');\n    };\n    /**\n     * Calculate scrollbar size\n     */\n    SimpleBarCore.prototype.getScrollbarSize = function (axis) {\n        var _a, _b;\n        if (axis === void 0) { axis = 'y'; }\n        if (!this.axis[axis].isOverflowing || !this.contentEl) {\n            return 0;\n        }\n        var contentSize = this.contentEl[this.axis[axis].scrollSizeAttr];\n        var trackSize = (_b = (_a = this.axis[axis].track.el) === null || _a === void 0 ? void 0 : _a[this.axis[axis].offsetSizeAttr]) !== null && _b !== void 0 ? _b : 0;\n        var scrollbarRatio = trackSize / contentSize;\n        var scrollbarSize;\n        // Calculate new height/position of drag handle.\n        scrollbarSize = Math.max(~~(scrollbarRatio * trackSize), this.options.scrollbarMinSize);\n        if (this.options.scrollbarMaxSize) {\n            scrollbarSize = Math.min(scrollbarSize, this.options.scrollbarMaxSize);\n        }\n        return scrollbarSize;\n    };\n    SimpleBarCore.prototype.positionScrollbar = function (axis) {\n        var _a, _b, _c;\n        if (axis === void 0) { axis = 'y'; }\n        var scrollbar = this.axis[axis].scrollbar;\n        if (!this.axis[axis].isOverflowing ||\n            !this.contentWrapperEl ||\n            !scrollbar.el ||\n            !this.elStyles) {\n            return;\n        }\n        var contentSize = this.contentWrapperEl[this.axis[axis].scrollSizeAttr];\n        var trackSize = ((_a = this.axis[axis].track.el) === null || _a === void 0 ? void 0 : _a[this.axis[axis].offsetSizeAttr]) || 0;\n        var hostSize = parseInt(this.elStyles[this.axis[axis].sizeAttr], 10);\n        var scrollOffset = this.contentWrapperEl[this.axis[axis].scrollOffsetAttr];\n        scrollOffset =\n            axis === 'x' &&\n                this.isRtl &&\n                ((_b = SimpleBarCore.getRtlHelpers()) === null || _b === void 0 ? void 0 : _b.isScrollOriginAtZero)\n                ? -scrollOffset\n                : scrollOffset;\n        if (axis === 'x' && this.isRtl) {\n            scrollOffset = ((_c = SimpleBarCore.getRtlHelpers()) === null || _c === void 0 ? void 0 : _c.isScrollingToNegative)\n                ? scrollOffset\n                : -scrollOffset;\n        }\n        var scrollPourcent = scrollOffset / (contentSize - hostSize);\n        var handleOffset = ~~((trackSize - scrollbar.size) * scrollPourcent);\n        handleOffset =\n            axis === 'x' && this.isRtl\n                ? -handleOffset + (trackSize - scrollbar.size)\n                : handleOffset;\n        scrollbar.el.style.transform =\n            axis === 'x'\n                ? \"translate3d(\".concat(handleOffset, \"px, 0, 0)\")\n                : \"translate3d(0, \".concat(handleOffset, \"px, 0)\");\n    };\n    SimpleBarCore.prototype.toggleTrackVisibility = function (axis) {\n        if (axis === void 0) { axis = 'y'; }\n        var track = this.axis[axis].track.el;\n        var scrollbar = this.axis[axis].scrollbar.el;\n        if (!track || !scrollbar || !this.contentWrapperEl)\n            return;\n        if (this.axis[axis].isOverflowing || this.axis[axis].forceVisible) {\n            track.style.visibility = 'visible';\n            this.contentWrapperEl.style[this.axis[axis].overflowAttr] = 'scroll';\n            this.el.classList.add(\"\".concat(this.classNames.scrollable, \"-\").concat(axis));\n        }\n        else {\n            track.style.visibility = 'hidden';\n            this.contentWrapperEl.style[this.axis[axis].overflowAttr] = 'hidden';\n            this.el.classList.remove(\"\".concat(this.classNames.scrollable, \"-\").concat(axis));\n        }\n        // Even if forceVisible is enabled, scrollbar itself should be hidden\n        if (this.axis[axis].isOverflowing) {\n            scrollbar.style.display = 'block';\n        }\n        else {\n            scrollbar.style.display = 'none';\n        }\n    };\n    SimpleBarCore.prototype.showScrollbar = function (axis) {\n        if (axis === void 0) { axis = 'y'; }\n        if (this.axis[axis].isOverflowing && !this.axis[axis].scrollbar.isVisible) {\n            addClasses(this.axis[axis].scrollbar.el, this.classNames.visible);\n            this.axis[axis].scrollbar.isVisible = true;\n        }\n    };\n    SimpleBarCore.prototype.hideScrollbar = function (axis) {\n        if (axis === void 0) { axis = 'y'; }\n        if (this.isDragging)\n            return;\n        if (this.axis[axis].isOverflowing && this.axis[axis].scrollbar.isVisible) {\n            removeClasses(this.axis[axis].scrollbar.el, this.classNames.visible);\n            this.axis[axis].scrollbar.isVisible = false;\n        }\n    };\n    SimpleBarCore.prototype.hideNativeScrollbar = function () {\n        if (!this.offsetEl)\n            return;\n        this.offsetEl.style[this.isRtl ? 'left' : 'right'] =\n            this.axis.y.isOverflowing || this.axis.y.forceVisible\n                ? \"-\".concat(this.scrollbarWidth, \"px\")\n                : '0px';\n        this.offsetEl.style.bottom =\n            this.axis.x.isOverflowing || this.axis.x.forceVisible\n                ? \"-\".concat(this.scrollbarWidth, \"px\")\n                : '0px';\n    };\n    SimpleBarCore.prototype.onMouseMoveForAxis = function (axis) {\n        if (axis === void 0) { axis = 'y'; }\n        var currentAxis = this.axis[axis];\n        if (!currentAxis.track.el || !currentAxis.scrollbar.el)\n            return;\n        currentAxis.track.rect = currentAxis.track.el.getBoundingClientRect();\n        currentAxis.scrollbar.rect =\n            currentAxis.scrollbar.el.getBoundingClientRect();\n        if (this.isWithinBounds(currentAxis.track.rect)) {\n            this.showScrollbar(axis);\n            addClasses(currentAxis.track.el, this.classNames.hover);\n            if (this.isWithinBounds(currentAxis.scrollbar.rect)) {\n                addClasses(currentAxis.scrollbar.el, this.classNames.hover);\n            }\n            else {\n                removeClasses(currentAxis.scrollbar.el, this.classNames.hover);\n            }\n        }\n        else {\n            removeClasses(currentAxis.track.el, this.classNames.hover);\n            if (this.options.autoHide) {\n                this.hideScrollbar(axis);\n            }\n        }\n    };\n    SimpleBarCore.prototype.onMouseLeaveForAxis = function (axis) {\n        if (axis === void 0) { axis = 'y'; }\n        removeClasses(this.axis[axis].track.el, this.classNames.hover);\n        removeClasses(this.axis[axis].scrollbar.el, this.classNames.hover);\n        if (this.options.autoHide) {\n            this.hideScrollbar(axis);\n        }\n    };\n    /**\n     * on scrollbar handle drag movement starts\n     */\n    SimpleBarCore.prototype.onDragStart = function (e, axis) {\n        var _a;\n        if (axis === void 0) { axis = 'y'; }\n        this.isDragging = true;\n        var elDocument = getElementDocument(this.el);\n        var elWindow = getElementWindow(this.el);\n        var scrollbar = this.axis[axis].scrollbar;\n        // Measure how far the user's mouse is from the top of the scrollbar drag handle.\n        var eventOffset = axis === 'y' ? e.pageY : e.pageX;\n        this.axis[axis].dragOffset =\n            eventOffset - (((_a = scrollbar.rect) === null || _a === void 0 ? void 0 : _a[this.axis[axis].offsetAttr]) || 0);\n        this.draggedAxis = axis;\n        addClasses(this.el, this.classNames.dragging);\n        elDocument.addEventListener('mousemove', this.drag, true);\n        elDocument.addEventListener('mouseup', this.onEndDrag, true);\n        if (this.removePreventClickId === null) {\n            elDocument.addEventListener('click', this.preventClick, true);\n            elDocument.addEventListener('dblclick', this.preventClick, true);\n        }\n        else {\n            elWindow.clearTimeout(this.removePreventClickId);\n            this.removePreventClickId = null;\n        }\n    };\n    SimpleBarCore.prototype.onTrackClick = function (e, axis) {\n        var _this = this;\n        var _a, _b, _c, _d;\n        if (axis === void 0) { axis = 'y'; }\n        var currentAxis = this.axis[axis];\n        if (!this.options.clickOnTrack ||\n            !currentAxis.scrollbar.el ||\n            !this.contentWrapperEl)\n            return;\n        // Preventing the event's default to trigger click underneath\n        e.preventDefault();\n        var elWindow = getElementWindow(this.el);\n        this.axis[axis].scrollbar.rect =\n            currentAxis.scrollbar.el.getBoundingClientRect();\n        var scrollbar = this.axis[axis].scrollbar;\n        var scrollbarOffset = (_b = (_a = scrollbar.rect) === null || _a === void 0 ? void 0 : _a[this.axis[axis].offsetAttr]) !== null && _b !== void 0 ? _b : 0;\n        var hostSize = parseInt((_d = (_c = this.elStyles) === null || _c === void 0 ? void 0 : _c[this.axis[axis].sizeAttr]) !== null && _d !== void 0 ? _d : '0px', 10);\n        var scrolled = this.contentWrapperEl[this.axis[axis].scrollOffsetAttr];\n        var t = axis === 'y'\n            ? this.mouseY - scrollbarOffset\n            : this.mouseX - scrollbarOffset;\n        var dir = t < 0 ? -1 : 1;\n        var scrollSize = dir === -1 ? scrolled - hostSize : scrolled + hostSize;\n        var speed = 40;\n        var scrollTo = function () {\n            if (!_this.contentWrapperEl)\n                return;\n            if (dir === -1) {\n                if (scrolled > scrollSize) {\n                    scrolled -= speed;\n                    _this.contentWrapperEl[_this.axis[axis].scrollOffsetAttr] = scrolled;\n                    elWindow.requestAnimationFrame(scrollTo);\n                }\n            }\n            else {\n                if (scrolled < scrollSize) {\n                    scrolled += speed;\n                    _this.contentWrapperEl[_this.axis[axis].scrollOffsetAttr] = scrolled;\n                    elWindow.requestAnimationFrame(scrollTo);\n                }\n            }\n        };\n        scrollTo();\n    };\n    /**\n     * Getter for content element\n     */\n    SimpleBarCore.prototype.getContentElement = function () {\n        return this.contentEl;\n    };\n    /**\n     * Getter for original scrolling element\n     */\n    SimpleBarCore.prototype.getScrollElement = function () {\n        return this.contentWrapperEl;\n    };\n    SimpleBarCore.prototype.removeListeners = function () {\n        var elWindow = getElementWindow(this.el);\n        // Event listeners\n        this.el.removeEventListener('mouseenter', this.onMouseEnter);\n        this.el.removeEventListener('pointerdown', this.onPointerEvent, true);\n        this.el.removeEventListener('mousemove', this.onMouseMove);\n        this.el.removeEventListener('mouseleave', this.onMouseLeave);\n        if (this.contentWrapperEl) {\n            this.contentWrapperEl.removeEventListener('scroll', this.onScroll);\n        }\n        elWindow.removeEventListener('resize', this.onWindowResize);\n        if (this.mutationObserver) {\n            this.mutationObserver.disconnect();\n        }\n        if (this.resizeObserver) {\n            this.resizeObserver.disconnect();\n        }\n        // Cancel all debounced functions\n        this.onMouseMove.cancel();\n        this.onWindowResize.cancel();\n        this.onStopScrolling.cancel();\n        this.onMouseEntered.cancel();\n    };\n    /**\n     * Remove all listeners from DOM nodes\n     */\n    SimpleBarCore.prototype.unMount = function () {\n        this.removeListeners();\n    };\n    /**\n     * Check if mouse is within bounds\n     */\n    SimpleBarCore.prototype.isWithinBounds = function (bbox) {\n        return (this.mouseX >= bbox.left &&\n            this.mouseX <= bbox.left + bbox.width &&\n            this.mouseY >= bbox.top &&\n            this.mouseY <= bbox.top + bbox.height);\n    };\n    /**\n     * Find element children matches query\n     */\n    SimpleBarCore.prototype.findChild = function (el, query) {\n        var matches = el.matches ||\n            el.webkitMatchesSelector ||\n            el.mozMatchesSelector ||\n            el.msMatchesSelector;\n        return Array.prototype.filter.call(el.children, function (child) {\n            return matches.call(child, query);\n        })[0];\n    };\n    SimpleBarCore.rtlHelpers = null;\n    SimpleBarCore.defaultOptions = {\n        forceVisible: false,\n        clickOnTrack: true,\n        scrollbarMinSize: 25,\n        scrollbarMaxSize: 0,\n        ariaLabel: 'scrollable content',\n        tabIndex: 0,\n        classNames: {\n            contentEl: 'simplebar-content',\n            contentWrapper: 'simplebar-content-wrapper',\n            offset: 'simplebar-offset',\n            mask: 'simplebar-mask',\n            wrapper: 'simplebar-wrapper',\n            placeholder: 'simplebar-placeholder',\n            scrollbar: 'simplebar-scrollbar',\n            track: 'simplebar-track',\n            heightAutoObserverWrapperEl: 'simplebar-height-auto-observer-wrapper',\n            heightAutoObserverEl: 'simplebar-height-auto-observer',\n            visible: 'simplebar-visible',\n            horizontal: 'simplebar-horizontal',\n            vertical: 'simplebar-vertical',\n            hover: 'simplebar-hover',\n            dragging: 'simplebar-dragging',\n            scrolling: 'simplebar-scrolling',\n            scrollable: 'simplebar-scrollable',\n            mouseEntered: 'simplebar-mouse-entered'\n        },\n        scrollableNode: null,\n        contentNode: null,\n        autoHide: true\n    };\n    /**\n     * Static functions\n     */\n    SimpleBarCore.getOptions = getOptions;\n    SimpleBarCore.helpers = helpers;\n    return SimpleBarCore;\n}());\n\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/simplebar-core/dist/index.mjs\n");

/***/ })

};
;