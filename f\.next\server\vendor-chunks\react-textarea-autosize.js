"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-textarea-autosize";
exports.ids = ["vendor-chunks/react-textarea-autosize"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-textarea-autosize/dist/react-textarea-autosize.development.esm.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/react-textarea-autosize/dist/react-textarea-autosize.development.esm.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ index)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutPropertiesLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var use_latest__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! use-latest */ \"(ssr)/./node_modules/use-latest/dist/use-latest.esm.js\");\n/* harmony import */ var use_composed_ref__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! use-composed-ref */ \"(ssr)/./node_modules/use-composed-ref/dist/use-composed-ref.esm.js\");\n\n\n\n\n\n\nvar isBrowser = typeof document !== 'undefined';\n\nvar HIDDEN_TEXTAREA_STYLE = {\n  'min-height': '0',\n  'max-height': 'none',\n  height: '0',\n  visibility: 'hidden',\n  overflow: 'hidden',\n  position: 'absolute',\n  'z-index': '-1000',\n  top: '0',\n  right: '0',\n  display: 'block'\n};\nvar forceHiddenStyles = function forceHiddenStyles(node) {\n  Object.keys(HIDDEN_TEXTAREA_STYLE).forEach(function (key) {\n    node.style.setProperty(key, HIDDEN_TEXTAREA_STYLE[key], 'important');\n  });\n};\nvar forceHiddenStyles$1 = forceHiddenStyles;\n\nvar hiddenTextarea = null;\nvar getHeight = function getHeight(node, sizingData) {\n  var height = node.scrollHeight;\n  if (sizingData.sizingStyle.boxSizing === 'border-box') {\n    // border-box: add border, since height = content + padding + border\n    return height + sizingData.borderSize;\n  }\n\n  // remove padding, since height = content\n  return height - sizingData.paddingSize;\n};\nfunction calculateNodeHeight(sizingData, value, minRows, maxRows) {\n  if (minRows === void 0) {\n    minRows = 1;\n  }\n  if (maxRows === void 0) {\n    maxRows = Infinity;\n  }\n  if (!hiddenTextarea) {\n    hiddenTextarea = document.createElement('textarea');\n    hiddenTextarea.setAttribute('tabindex', '-1');\n    hiddenTextarea.setAttribute('aria-hidden', 'true');\n    forceHiddenStyles$1(hiddenTextarea);\n  }\n  if (hiddenTextarea.parentNode === null) {\n    document.body.appendChild(hiddenTextarea);\n  }\n  var paddingSize = sizingData.paddingSize,\n    borderSize = sizingData.borderSize,\n    sizingStyle = sizingData.sizingStyle;\n  var boxSizing = sizingStyle.boxSizing;\n  Object.keys(sizingStyle).forEach(function (_key) {\n    var key = _key;\n    hiddenTextarea.style[key] = sizingStyle[key];\n  });\n  forceHiddenStyles$1(hiddenTextarea);\n  hiddenTextarea.value = value;\n  var height = getHeight(hiddenTextarea, sizingData);\n  // Double set and calc due to Firefox bug: https://bugzilla.mozilla.org/show_bug.cgi?id=1795904\n  hiddenTextarea.value = value;\n  height = getHeight(hiddenTextarea, sizingData);\n\n  // measure height of a textarea with a single row\n  hiddenTextarea.value = 'x';\n  var rowHeight = hiddenTextarea.scrollHeight - paddingSize;\n  var minHeight = rowHeight * minRows;\n  if (boxSizing === 'border-box') {\n    minHeight = minHeight + paddingSize + borderSize;\n  }\n  height = Math.max(minHeight, height);\n  var maxHeight = rowHeight * maxRows;\n  if (boxSizing === 'border-box') {\n    maxHeight = maxHeight + paddingSize + borderSize;\n  }\n  height = Math.min(maxHeight, height);\n  return [height, rowHeight];\n}\n\nvar noop = function noop() {};\nvar pick = function pick(props, obj) {\n  return props.reduce(function (acc, prop) {\n    acc[prop] = obj[prop];\n    return acc;\n  }, {});\n};\n\nvar SIZING_STYLE = ['borderBottomWidth', 'borderLeftWidth', 'borderRightWidth', 'borderTopWidth', 'boxSizing', 'fontFamily', 'fontSize', 'fontStyle', 'fontWeight', 'letterSpacing', 'lineHeight', 'paddingBottom', 'paddingLeft', 'paddingRight', 'paddingTop',\n// non-standard\n'tabSize', 'textIndent',\n// non-standard\n'textRendering', 'textTransform', 'width', 'wordBreak', 'wordSpacing', 'scrollbarGutter'];\nvar isIE = isBrowser ? !!document.documentElement.currentStyle : false;\nvar getSizingData = function getSizingData(node) {\n  var style = window.getComputedStyle(node);\n  if (style === null) {\n    return null;\n  }\n  var sizingStyle = pick(SIZING_STYLE, style);\n  var boxSizing = sizingStyle.boxSizing;\n\n  // probably node is detached from DOM, can't read computed dimensions\n  if (boxSizing === '') {\n    return null;\n  }\n\n  // IE (Edge has already correct behaviour) returns content width as computed width\n  // so we need to add manually padding and border widths\n  if (isIE && boxSizing === 'border-box') {\n    sizingStyle.width = parseFloat(sizingStyle.width) + parseFloat(sizingStyle.borderRightWidth) + parseFloat(sizingStyle.borderLeftWidth) + parseFloat(sizingStyle.paddingRight) + parseFloat(sizingStyle.paddingLeft) + 'px';\n  }\n  var paddingSize = parseFloat(sizingStyle.paddingBottom) + parseFloat(sizingStyle.paddingTop);\n  var borderSize = parseFloat(sizingStyle.borderBottomWidth) + parseFloat(sizingStyle.borderTopWidth);\n  return {\n    sizingStyle: sizingStyle,\n    paddingSize: paddingSize,\n    borderSize: borderSize\n  };\n};\nvar getSizingData$1 = getSizingData;\n\nfunction useListener(target, type, listener) {\n  var latestListener = (0,use_latest__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(listener);\n  react__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect(function () {\n    var handler = function handler(ev) {\n      return latestListener.current(ev);\n    };\n    // might happen if document.fonts is not defined, for instance\n    if (!target) {\n      return;\n    }\n    target.addEventListener(type, handler);\n    return function () {\n      return target.removeEventListener(type, handler);\n    };\n  }, []);\n}\nvar useFormResetListener = function useFormResetListener(libRef, listener) {\n  useListener(document.body, 'reset', function (ev) {\n    if (libRef.current.form === ev.target) {\n      listener(ev);\n    }\n  });\n};\nvar useWindowResizeListener = function useWindowResizeListener(listener) {\n  useListener(window, 'resize', listener);\n};\nvar useFontsLoadedListener = function useFontsLoadedListener(listener) {\n  useListener(document.fonts, 'loadingdone', listener);\n};\n\nvar _excluded = [\"cacheMeasurements\", \"maxRows\", \"minRows\", \"onChange\", \"onHeightChange\"];\nvar TextareaAutosize = function TextareaAutosize(_ref, userRef) {\n  var cacheMeasurements = _ref.cacheMeasurements,\n    maxRows = _ref.maxRows,\n    minRows = _ref.minRows,\n    _ref$onChange = _ref.onChange,\n    onChange = _ref$onChange === void 0 ? noop : _ref$onChange,\n    _ref$onHeightChange = _ref.onHeightChange,\n    onHeightChange = _ref$onHeightChange === void 0 ? noop : _ref$onHeightChange,\n    props = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_ref, _excluded);\n  if (props.style) {\n    if ('maxHeight' in props.style) {\n      throw new Error('Using `style.maxHeight` for <TextareaAutosize/> is not supported. Please use `maxRows`.');\n    }\n    if ('minHeight' in props.style) {\n      throw new Error('Using `style.minHeight` for <TextareaAutosize/> is not supported. Please use `minRows`.');\n    }\n  }\n  var isControlled = props.value !== undefined;\n  var libRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(null);\n  var ref = (0,use_composed_ref__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(libRef, userRef);\n  var heightRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(0);\n  var measurementsCacheRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef();\n  var resizeTextarea = function resizeTextarea() {\n    var node = libRef.current;\n    var nodeSizingData = cacheMeasurements && measurementsCacheRef.current ? measurementsCacheRef.current : getSizingData$1(node);\n    if (!nodeSizingData) {\n      return;\n    }\n    measurementsCacheRef.current = nodeSizingData;\n    var _calculateNodeHeight = calculateNodeHeight(nodeSizingData, node.value || node.placeholder || 'x', minRows, maxRows),\n      height = _calculateNodeHeight[0],\n      rowHeight = _calculateNodeHeight[1];\n    if (heightRef.current !== height) {\n      heightRef.current = height;\n      node.style.setProperty('height', height + \"px\", 'important');\n      onHeightChange(height, {\n        rowHeight: rowHeight\n      });\n    }\n  };\n  var handleChange = function handleChange(event) {\n    if (!isControlled) {\n      resizeTextarea();\n    }\n    onChange(event);\n  };\n  if (isBrowser) {\n    react__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect(resizeTextarea);\n    useFormResetListener(libRef, function () {\n      if (!isControlled) {\n        var currentValue = libRef.current.value;\n        requestAnimationFrame(function () {\n          var node = libRef.current;\n          if (node && currentValue !== node.value) {\n            resizeTextarea();\n          }\n        });\n      }\n    });\n    useWindowResizeListener(resizeTextarea);\n    useFontsLoadedListener(resizeTextarea);\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(\"textarea\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, props, {\n      onChange: handleChange,\n      ref: ref\n    }));\n  }\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(\"textarea\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, props, {\n    onChange: onChange,\n    ref: ref\n  }));\n};\nvar index = /* #__PURE__ */react__WEBPACK_IMPORTED_MODULE_2__.forwardRef(TextareaAutosize);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-textarea-autosize/dist/react-textarea-autosize.development.esm.js\n");

/***/ })

};
;