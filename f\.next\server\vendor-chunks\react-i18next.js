"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-i18next";
exports.ids = ["vendor-chunks/react-i18next"];
exports.modules = {

/***/ "(rsc)/./node_modules/react-i18next/dist/es/defaults.js":
/*!********************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/defaults.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDefaults: () => (/* binding */ getDefaults),\n/* harmony export */   setDefaults: () => (/* binding */ setDefaults)\n/* harmony export */ });\n/* harmony import */ var _unescape_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./unescape.js */ \"(rsc)/./node_modules/react-i18next/dist/es/unescape.js\");\n\nlet defaultOptions = {\n  bindI18n: 'languageChanged',\n  bindI18nStore: '',\n  transEmptyNodeValue: '',\n  transSupportBasicHtmlNodes: true,\n  transWrapTextNodes: '',\n  transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'p'],\n  useSuspense: true,\n  unescape: _unescape_js__WEBPACK_IMPORTED_MODULE_0__.unescape\n};\nconst setDefaults = (options = {}) => {\n  defaultOptions = {\n    ...defaultOptions,\n    ...options\n  };\n};\nconst getDefaults = () => defaultOptions;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL2RlZmF1bHRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF5QztBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ08saUNBQWlDO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzb3JyeVxcRGVza3RvcFxcdGVzdEJOXFxwcm9qZWN0c1xcZlxcbm9kZV9tb2R1bGVzXFxyZWFjdC1pMThuZXh0XFxkaXN0XFxlc1xcZGVmYXVsdHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdW5lc2NhcGUgfSBmcm9tICcuL3VuZXNjYXBlLmpzJztcbmxldCBkZWZhdWx0T3B0aW9ucyA9IHtcbiAgYmluZEkxOG46ICdsYW5ndWFnZUNoYW5nZWQnLFxuICBiaW5kSTE4blN0b3JlOiAnJyxcbiAgdHJhbnNFbXB0eU5vZGVWYWx1ZTogJycsXG4gIHRyYW5zU3VwcG9ydEJhc2ljSHRtbE5vZGVzOiB0cnVlLFxuICB0cmFuc1dyYXBUZXh0Tm9kZXM6ICcnLFxuICB0cmFuc0tlZXBCYXNpY0h0bWxOb2Rlc0ZvcjogWydicicsICdzdHJvbmcnLCAnaScsICdwJ10sXG4gIHVzZVN1c3BlbnNlOiB0cnVlLFxuICB1bmVzY2FwZVxufTtcbmV4cG9ydCBjb25zdCBzZXREZWZhdWx0cyA9IChvcHRpb25zID0ge30pID0+IHtcbiAgZGVmYXVsdE9wdGlvbnMgPSB7XG4gICAgLi4uZGVmYXVsdE9wdGlvbnMsXG4gICAgLi4ub3B0aW9uc1xuICB9O1xufTtcbmV4cG9ydCBjb25zdCBnZXREZWZhdWx0cyA9ICgpID0+IGRlZmF1bHRPcHRpb25zOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/react-i18next/dist/es/defaults.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/react-i18next/dist/es/i18nInstance.js":
/*!************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/i18nInstance.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getI18n: () => (/* binding */ getI18n),\n/* harmony export */   setI18n: () => (/* binding */ setI18n)\n/* harmony export */ });\nlet i18nInstance;\nconst setI18n = instance => {\n  i18nInstance = instance;\n};\nconst getI18n = () => i18nInstance;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL2kxOG5JbnN0YW5jZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ087QUFDUDtBQUNBO0FBQ08iLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc29ycnlcXERlc2t0b3BcXHRlc3RCTlxccHJvamVjdHNcXGZcXG5vZGVfbW9kdWxlc1xccmVhY3QtaTE4bmV4dFxcZGlzdFxcZXNcXGkxOG5JbnN0YW5jZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJsZXQgaTE4bkluc3RhbmNlO1xuZXhwb3J0IGNvbnN0IHNldEkxOG4gPSBpbnN0YW5jZSA9PiB7XG4gIGkxOG5JbnN0YW5jZSA9IGluc3RhbmNlO1xufTtcbmV4cG9ydCBjb25zdCBnZXRJMThuID0gKCkgPT4gaTE4bkluc3RhbmNlOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/react-i18next/dist/es/i18nInstance.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/react-i18next/dist/es/initReactI18next.js":
/*!****************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/initReactI18next.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initReactI18next: () => (/* binding */ initReactI18next)\n/* harmony export */ });\n/* harmony import */ var _defaults_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./defaults.js */ \"(rsc)/./node_modules/react-i18next/dist/es/defaults.js\");\n/* harmony import */ var _i18nInstance_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./i18nInstance.js */ \"(rsc)/./node_modules/react-i18next/dist/es/i18nInstance.js\");\n\n\nconst initReactI18next = {\n  type: '3rdParty',\n  init(instance) {\n    (0,_defaults_js__WEBPACK_IMPORTED_MODULE_0__.setDefaults)(instance.options.react);\n    (0,_i18nInstance_js__WEBPACK_IMPORTED_MODULE_1__.setI18n)(instance);\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL2luaXRSZWFjdEkxOG5leHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0E7QUFDckM7QUFDUDtBQUNBO0FBQ0EsSUFBSSx5REFBVztBQUNmLElBQUkseURBQU87QUFDWDtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNvcnJ5XFxEZXNrdG9wXFx0ZXN0Qk5cXHByb2plY3RzXFxmXFxub2RlX21vZHVsZXNcXHJlYWN0LWkxOG5leHRcXGRpc3RcXGVzXFxpbml0UmVhY3RJMThuZXh0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHNldERlZmF1bHRzIH0gZnJvbSAnLi9kZWZhdWx0cy5qcyc7XG5pbXBvcnQgeyBzZXRJMThuIH0gZnJvbSAnLi9pMThuSW5zdGFuY2UuanMnO1xuZXhwb3J0IGNvbnN0IGluaXRSZWFjdEkxOG5leHQgPSB7XG4gIHR5cGU6ICczcmRQYXJ0eScsXG4gIGluaXQoaW5zdGFuY2UpIHtcbiAgICBzZXREZWZhdWx0cyhpbnN0YW5jZS5vcHRpb25zLnJlYWN0KTtcbiAgICBzZXRJMThuKGluc3RhbmNlKTtcbiAgfVxufTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/react-i18next/dist/es/initReactI18next.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/react-i18next/dist/es/unescape.js":
/*!********************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/unescape.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unescape: () => (/* binding */ unescape)\n/* harmony export */ });\nconst matchHtmlEntity = /&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g;\nconst htmlEntities = {\n  '&amp;': '&',\n  '&#38;': '&',\n  '&lt;': '<',\n  '&#60;': '<',\n  '&gt;': '>',\n  '&#62;': '>',\n  '&apos;': \"'\",\n  '&#39;': \"'\",\n  '&quot;': '\"',\n  '&#34;': '\"',\n  '&nbsp;': ' ',\n  '&#160;': ' ',\n  '&copy;': '©',\n  '&#169;': '©',\n  '&reg;': '®',\n  '&#174;': '®',\n  '&hellip;': '…',\n  '&#8230;': '…',\n  '&#x2F;': '/',\n  '&#47;': '/'\n};\nconst unescapeHtmlEntity = m => htmlEntities[m];\nconst unescape = text => text.replace(matchHtmlEntity, unescapeHtmlEntity);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL3VuZXNjYXBlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSx5SEFBeUg7QUFDekg7QUFDQSxRQUFRO0FBQ1IsUUFBUTtBQUNSLE9BQU87QUFDUCxRQUFRO0FBQ1IsT0FBTztBQUNQLFFBQVE7QUFDUixTQUFTO0FBQ1QsUUFBUTtBQUNSLFNBQVM7QUFDVCxRQUFRO0FBQ1IsU0FBUztBQUNULFNBQVM7QUFDVCxTQUFTO0FBQ1QsU0FBUztBQUNULFFBQVE7QUFDUixTQUFTO0FBQ1QsV0FBVztBQUNYLFVBQVU7QUFDVixTQUFTO0FBQ1QsUUFBUTtBQUNSO0FBQ0E7QUFDTyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzb3JyeVxcRGVza3RvcFxcdGVzdEJOXFxwcm9qZWN0c1xcZlxcbm9kZV9tb2R1bGVzXFxyZWFjdC1pMThuZXh0XFxkaXN0XFxlc1xcdW5lc2NhcGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgbWF0Y2hIdG1sRW50aXR5ID0gLyYoPzphbXB8IzM4fGx0fCM2MHxndHwjNjJ8YXBvc3wjMzl8cXVvdHwjMzR8bmJzcHwjMTYwfGNvcHl8IzE2OXxyZWd8IzE3NHxoZWxsaXB8IzgyMzB8I3gyRnwjNDcpOy9nO1xuY29uc3QgaHRtbEVudGl0aWVzID0ge1xuICAnJmFtcDsnOiAnJicsXG4gICcmIzM4Oyc6ICcmJyxcbiAgJyZsdDsnOiAnPCcsXG4gICcmIzYwOyc6ICc8JyxcbiAgJyZndDsnOiAnPicsXG4gICcmIzYyOyc6ICc+JyxcbiAgJyZhcG9zOyc6IFwiJ1wiLFxuICAnJiMzOTsnOiBcIidcIixcbiAgJyZxdW90Oyc6ICdcIicsXG4gICcmIzM0Oyc6ICdcIicsXG4gICcmbmJzcDsnOiAnICcsXG4gICcmIzE2MDsnOiAnICcsXG4gICcmY29weTsnOiAnwqknLFxuICAnJiMxNjk7JzogJ8KpJyxcbiAgJyZyZWc7JzogJ8KuJyxcbiAgJyYjMTc0Oyc6ICfCricsXG4gICcmaGVsbGlwOyc6ICfigKYnLFxuICAnJiM4MjMwOyc6ICfigKYnLFxuICAnJiN4MkY7JzogJy8nLFxuICAnJiM0NzsnOiAnLydcbn07XG5jb25zdCB1bmVzY2FwZUh0bWxFbnRpdHkgPSBtID0+IGh0bWxFbnRpdGllc1ttXTtcbmV4cG9ydCBjb25zdCB1bmVzY2FwZSA9IHRleHQgPT4gdGV4dC5yZXBsYWNlKG1hdGNoSHRtbEVudGl0eSwgdW5lc2NhcGVIdG1sRW50aXR5KTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/react-i18next/dist/es/unescape.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/I18nextProvider.js":
/*!***************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/I18nextProvider.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I18nextProvider: () => (/* binding */ I18nextProvider)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./context.js */ \"(ssr)/./node_modules/react-i18next/dist/es/context.js\");\n\n\nfunction I18nextProvider({\n  i18n,\n  defaultNS,\n  children\n}) {\n  const value = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    i18n,\n    defaultNS\n  }), [i18n, defaultNS]);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(_context_js__WEBPACK_IMPORTED_MODULE_1__.I18nContext.Provider, {\n    value\n  }, children);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL0kxOG5leHRQcm92aWRlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBK0M7QUFDSjtBQUNwQztBQUNQO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRCxnQkFBZ0IsOENBQU87QUFDdkI7QUFDQTtBQUNBLEdBQUc7QUFDSCxTQUFTLG9EQUFhLENBQUMsb0RBQVc7QUFDbEM7QUFDQSxHQUFHO0FBQ0giLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc29ycnlcXERlc2t0b3BcXHRlc3RCTlxccHJvamVjdHNcXGZcXG5vZGVfbW9kdWxlc1xccmVhY3QtaTE4bmV4dFxcZGlzdFxcZXNcXEkxOG5leHRQcm92aWRlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVFbGVtZW50LCB1c2VNZW1vIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgSTE4bkNvbnRleHQgfSBmcm9tICcuL2NvbnRleHQuanMnO1xuZXhwb3J0IGZ1bmN0aW9uIEkxOG5leHRQcm92aWRlcih7XG4gIGkxOG4sXG4gIGRlZmF1bHROUyxcbiAgY2hpbGRyZW5cbn0pIHtcbiAgY29uc3QgdmFsdWUgPSB1c2VNZW1vKCgpID0+ICh7XG4gICAgaTE4bixcbiAgICBkZWZhdWx0TlNcbiAgfSksIFtpMThuLCBkZWZhdWx0TlNdKTtcbiAgcmV0dXJuIGNyZWF0ZUVsZW1lbnQoSTE4bkNvbnRleHQuUHJvdmlkZXIsIHtcbiAgICB2YWx1ZVxuICB9LCBjaGlsZHJlbik7XG59Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/I18nextProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/Trans.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/Trans.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Trans: () => (/* binding */ Trans),\n/* harmony export */   nodesToString: () => (/* reexport safe */ _TransWithoutContext_js__WEBPACK_IMPORTED_MODULE_1__.nodesToString)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _TransWithoutContext_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./TransWithoutContext.js */ \"(ssr)/./node_modules/react-i18next/dist/es/TransWithoutContext.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./context.js */ \"(ssr)/./node_modules/react-i18next/dist/es/context.js\");\n\n\n\n\nfunction Trans({\n  children,\n  count,\n  parent,\n  i18nKey,\n  context,\n  tOptions = {},\n  values,\n  defaults,\n  components,\n  ns,\n  i18n: i18nFromProps,\n  t: tFromProps,\n  shouldUnescape,\n  ...additionalProps\n}) {\n  const {\n    i18n: i18nFromContext,\n    defaultNS: defaultNSFromContext\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_js__WEBPACK_IMPORTED_MODULE_2__.I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || (0,_context_js__WEBPACK_IMPORTED_MODULE_2__.getI18n)();\n  const t = tFromProps || i18n?.t.bind(i18n);\n  return (0,_TransWithoutContext_js__WEBPACK_IMPORTED_MODULE_1__.Trans)({\n    children,\n    count,\n    parent,\n    i18nKey,\n    context,\n    tOptions,\n    values,\n    defaults,\n    components,\n    ns: ns || t?.ns || defaultNSFromContext || i18n?.options?.defaultNS,\n    i18n,\n    t: tFromProps,\n    shouldUnescape,\n    ...additionalProps\n  });\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/Trans.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/TransWithoutContext.js":
/*!*******************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/TransWithoutContext.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Trans: () => (/* binding */ Trans),\n/* harmony export */   nodesToString: () => (/* binding */ nodesToString)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var html_parse_stringify__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! html-parse-stringify */ \"(ssr)/./node_modules/html-parse-stringify/dist/html-parse-stringify.module.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/react-i18next/dist/es/utils.js\");\n/* harmony import */ var _defaults_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./defaults.js */ \"(ssr)/./node_modules/react-i18next/dist/es/defaults.js\");\n/* harmony import */ var _i18nInstance_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./i18nInstance.js */ \"(ssr)/./node_modules/react-i18next/dist/es/i18nInstance.js\");\n\n\n\n\n\nconst hasChildren = (node, checkLength) => {\n  if (!node) return false;\n  const base = node.props?.children ?? node.children;\n  if (checkLength) return base.length > 0;\n  return !!base;\n};\nconst getChildren = node => {\n  if (!node) return [];\n  const children = node.props?.children ?? node.children;\n  return node.props?.i18nIsDynamicList ? getAsArray(children) : children;\n};\nconst hasValidReactChildren = children => Array.isArray(children) && children.every(react__WEBPACK_IMPORTED_MODULE_0__.isValidElement);\nconst getAsArray = data => Array.isArray(data) ? data : [data];\nconst mergeProps = (source, target) => {\n  const newTarget = {\n    ...target\n  };\n  newTarget.props = Object.assign(source.props, target.props);\n  return newTarget;\n};\nconst nodesToString = (children, i18nOptions, i18n, i18nKey) => {\n  if (!children) return '';\n  let stringNode = '';\n  const childrenArray = getAsArray(children);\n  const keepArray = i18nOptions?.transSupportBasicHtmlNodes ? i18nOptions.transKeepBasicHtmlNodesFor ?? [] : [];\n  childrenArray.forEach((child, childIndex) => {\n    if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(child)) {\n      stringNode += `${child}`;\n      return;\n    }\n    if ((0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(child)) {\n      const {\n        props,\n        type\n      } = child;\n      const childPropsCount = Object.keys(props).length;\n      const shouldKeepChild = keepArray.indexOf(type) > -1;\n      const childChildren = props.children;\n      if (!childChildren && shouldKeepChild && !childPropsCount) {\n        stringNode += `<${type}/>`;\n        return;\n      }\n      if (!childChildren && (!shouldKeepChild || childPropsCount) || props.i18nIsDynamicList) {\n        stringNode += `<${childIndex}></${childIndex}>`;\n        return;\n      }\n      if (shouldKeepChild && childPropsCount === 1 && (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(childChildren)) {\n        stringNode += `<${type}>${childChildren}</${type}>`;\n        return;\n      }\n      const content = nodesToString(childChildren, i18nOptions, i18n, i18nKey);\n      stringNode += `<${childIndex}>${content}</${childIndex}>`;\n      return;\n    }\n    if (child === null) {\n      (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warn)(i18n, 'TRANS_NULL_VALUE', `Passed in a null value as child`, {\n        i18nKey\n      });\n      return;\n    }\n    if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(child)) {\n      const {\n        format,\n        ...clone\n      } = child;\n      const keys = Object.keys(clone);\n      if (keys.length === 1) {\n        const value = format ? `${keys[0]}, ${format}` : keys[0];\n        stringNode += `{{${value}}}`;\n        return;\n      }\n      (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warn)(i18n, 'TRANS_INVALID_OBJ', `Invalid child - Object should only have keys {{ value, format }} (format is optional).`, {\n        i18nKey,\n        child\n      });\n      return;\n    }\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warn)(i18n, 'TRANS_INVALID_VAR', `Passed in a variable like {number} - pass variables for interpolation as full objects like {{number}}.`, {\n      i18nKey,\n      child\n    });\n  });\n  return stringNode;\n};\nconst renderNodes = (children, knownComponentsMap, targetString, i18n, i18nOptions, combinedTOpts, shouldUnescape) => {\n  if (targetString === '') return [];\n  const keepArray = i18nOptions.transKeepBasicHtmlNodesFor || [];\n  const emptyChildrenButNeedsHandling = targetString && new RegExp(keepArray.map(keep => `<${keep}`).join('|')).test(targetString);\n  if (!children && !knownComponentsMap && !emptyChildrenButNeedsHandling && !shouldUnescape) return [targetString];\n  const data = knownComponentsMap ?? {};\n  const getData = childs => {\n    const childrenArray = getAsArray(childs);\n    childrenArray.forEach(child => {\n      if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(child)) return;\n      if (hasChildren(child)) getData(getChildren(child));else if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(child) && !(0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(child)) Object.assign(data, child);\n    });\n  };\n  getData(children);\n  const ast = html_parse_stringify__WEBPACK_IMPORTED_MODULE_1__[\"default\"].parse(`<0>${targetString}</0>`);\n  const opts = {\n    ...data,\n    ...combinedTOpts\n  };\n  const renderInner = (child, node, rootReactNode) => {\n    const childs = getChildren(child);\n    const mappedChildren = mapAST(childs, node.children, rootReactNode);\n    return hasValidReactChildren(childs) && mappedChildren.length === 0 || child.props?.i18nIsDynamicList ? childs : mappedChildren;\n  };\n  const pushTranslatedJSX = (child, inner, mem, i, isVoid) => {\n    if (child.dummy) {\n      child.children = inner;\n      mem.push((0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(child, {\n        key: i\n      }, isVoid ? undefined : inner));\n    } else {\n      mem.push(...react__WEBPACK_IMPORTED_MODULE_0__.Children.map([child], c => {\n        const props = {\n          ...c.props\n        };\n        delete props.i18nIsDynamicList;\n        return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(c.type, {\n          ...props,\n          key: i,\n          ref: c.props.ref ?? c.ref\n        }, isVoid ? null : inner);\n      }));\n    }\n  };\n  const mapAST = (reactNode, astNode, rootReactNode) => {\n    const reactNodes = getAsArray(reactNode);\n    const astNodes = getAsArray(astNode);\n    return astNodes.reduce((mem, node, i) => {\n      const translationContent = node.children?.[0]?.content && i18n.services.interpolator.interpolate(node.children[0].content, opts, i18n.language);\n      if (node.type === 'tag') {\n        let tmp = reactNodes[parseInt(node.name, 10)];\n        if (!tmp && knownComponentsMap) tmp = knownComponentsMap[node.name];\n        if (rootReactNode.length === 1 && !tmp) tmp = rootReactNode[0][node.name];\n        if (!tmp) tmp = {};\n        const child = Object.keys(node.attrs).length !== 0 ? mergeProps({\n          props: node.attrs\n        }, tmp) : tmp;\n        const isElement = (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(child);\n        const isValidTranslationWithChildren = isElement && hasChildren(node, true) && !node.voidElement;\n        const isEmptyTransWithHTML = emptyChildrenButNeedsHandling && (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(child) && child.dummy && !isElement;\n        const isKnownComponent = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(knownComponentsMap) && Object.hasOwnProperty.call(knownComponentsMap, node.name);\n        if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(child)) {\n          const value = i18n.services.interpolator.interpolate(child, opts, i18n.language);\n          mem.push(value);\n        } else if (hasChildren(child) || isValidTranslationWithChildren) {\n          const inner = renderInner(child, node, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (isEmptyTransWithHTML) {\n          const inner = mapAST(reactNodes, node.children, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (Number.isNaN(parseFloat(node.name))) {\n          if (isKnownComponent) {\n            const inner = renderInner(child, node, rootReactNode);\n            pushTranslatedJSX(child, inner, mem, i, node.voidElement);\n          } else if (i18nOptions.transSupportBasicHtmlNodes && keepArray.indexOf(node.name) > -1) {\n            if (node.voidElement) {\n              mem.push((0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(node.name, {\n                key: `${node.name}-${i}`\n              }));\n            } else {\n              const inner = mapAST(reactNodes, node.children, rootReactNode);\n              mem.push((0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(node.name, {\n                key: `${node.name}-${i}`\n              }, inner));\n            }\n          } else if (node.voidElement) {\n            mem.push(`<${node.name} />`);\n          } else {\n            const inner = mapAST(reactNodes, node.children, rootReactNode);\n            mem.push(`<${node.name}>${inner}</${node.name}>`);\n          }\n        } else if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(child) && !isElement) {\n          const content = node.children[0] ? translationContent : null;\n          if (content) mem.push(content);\n        } else {\n          pushTranslatedJSX(child, translationContent, mem, i, node.children.length !== 1 || !translationContent);\n        }\n      } else if (node.type === 'text') {\n        const wrapTextNodes = i18nOptions.transWrapTextNodes;\n        const content = shouldUnescape ? i18nOptions.unescape(i18n.services.interpolator.interpolate(node.content, opts, i18n.language)) : i18n.services.interpolator.interpolate(node.content, opts, i18n.language);\n        if (wrapTextNodes) {\n          mem.push((0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(wrapTextNodes, {\n            key: `${node.name}-${i}`\n          }, content));\n        } else {\n          mem.push(content);\n        }\n      }\n      return mem;\n    }, []);\n  };\n  const result = mapAST([{\n    dummy: true,\n    children: children || []\n  }], ast, getAsArray(children || []));\n  return getChildren(result[0]);\n};\nconst fixComponentProps = (component, index, translation) => {\n  const componentKey = component.key || index;\n  const comp = (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(component, {\n    key: componentKey\n  });\n  if (!comp.props || !comp.props.children || translation.indexOf(`${index}/>`) < 0 && translation.indexOf(`${index} />`) < 0) {\n    return comp;\n  }\n  function Componentized() {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, comp);\n  }\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(Componentized, {\n    key: componentKey\n  });\n};\nconst generateArrayComponents = (components, translation) => components.map((c, index) => fixComponentProps(c, index, translation));\nconst generateObjectComponents = (components, translation) => {\n  const componentMap = {};\n  Object.keys(components).forEach(c => {\n    Object.assign(componentMap, {\n      [c]: fixComponentProps(components[c], c, translation)\n    });\n  });\n  return componentMap;\n};\nconst generateComponents = (components, translation, i18n, i18nKey) => {\n  if (!components) return null;\n  if (Array.isArray(components)) {\n    return generateArrayComponents(components, translation);\n  }\n  if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(components)) {\n    return generateObjectComponents(components, translation);\n  }\n  (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warnOnce)(i18n, 'TRANS_INVALID_COMPONENTS', `<Trans /> \"components\" prop expects an object or array`, {\n    i18nKey\n  });\n  return null;\n};\nconst isComponentsMap = object => {\n  if (!(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(object)) return false;\n  if (Array.isArray(object)) return false;\n  return Object.keys(object).reduce((acc, key) => acc && Number.isNaN(Number.parseFloat(key)), true);\n};\nfunction Trans({\n  children,\n  count,\n  parent,\n  i18nKey,\n  context,\n  tOptions = {},\n  values,\n  defaults,\n  components,\n  ns,\n  i18n: i18nFromProps,\n  t: tFromProps,\n  shouldUnescape,\n  ...additionalProps\n}) {\n  const i18n = i18nFromProps || (0,_i18nInstance_js__WEBPACK_IMPORTED_MODULE_4__.getI18n)();\n  if (!i18n) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warnOnce)(i18n, 'NO_I18NEXT_INSTANCE', `Trans: You need to pass in an i18next instance using i18nextReactModule`, {\n      i18nKey\n    });\n    return children;\n  }\n  const t = tFromProps || i18n.t.bind(i18n) || (k => k);\n  const reactI18nextOptions = {\n    ...(0,_defaults_js__WEBPACK_IMPORTED_MODULE_3__.getDefaults)(),\n    ...i18n.options?.react\n  };\n  let namespaces = ns || t.ns || i18n.options?.defaultNS;\n  namespaces = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(namespaces) ? [namespaces] : namespaces || ['translation'];\n  const nodeAsString = nodesToString(children, reactI18nextOptions, i18n, i18nKey);\n  const defaultValue = defaults || nodeAsString || reactI18nextOptions.transEmptyNodeValue || i18nKey;\n  const {\n    hashTransKey\n  } = reactI18nextOptions;\n  const key = i18nKey || (hashTransKey ? hashTransKey(nodeAsString || defaultValue) : nodeAsString || defaultValue);\n  if (i18n.options?.interpolation?.defaultVariables) {\n    values = values && Object.keys(values).length > 0 ? {\n      ...values,\n      ...i18n.options.interpolation.defaultVariables\n    } : {\n      ...i18n.options.interpolation.defaultVariables\n    };\n  }\n  const interpolationOverride = values || count !== undefined && !i18n.options?.interpolation?.alwaysFormat || !children ? tOptions.interpolation : {\n    interpolation: {\n      ...tOptions.interpolation,\n      prefix: '#$?',\n      suffix: '?$#'\n    }\n  };\n  const combinedTOpts = {\n    ...tOptions,\n    context: context || tOptions.context,\n    count,\n    ...values,\n    ...interpolationOverride,\n    defaultValue,\n    ns: namespaces\n  };\n  const translation = key ? t(key, combinedTOpts) : defaultValue;\n  const generatedComponents = generateComponents(components, translation, i18n, i18nKey);\n  let indexedChildren = generatedComponents || children;\n  let componentsMap = null;\n  if (isComponentsMap(generatedComponents)) {\n    componentsMap = generatedComponents;\n    indexedChildren = children;\n  }\n  const content = renderNodes(indexedChildren, componentsMap, translation, i18n, reactI18nextOptions, combinedTOpts, shouldUnescape);\n  const useAsParent = parent ?? reactI18nextOptions.defaultTransParent;\n  return useAsParent ? (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(useAsParent, additionalProps, content) : content;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/TransWithoutContext.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/Translation.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/Translation.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Translation: () => (/* binding */ Translation)\n/* harmony export */ });\n/* harmony import */ var _useTranslation_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useTranslation.js */ \"(ssr)/./node_modules/react-i18next/dist/es/useTranslation.js\");\n\nconst Translation = ({\n  ns,\n  children,\n  ...options\n}) => {\n  const [t, i18n, ready] = (0,_useTranslation_js__WEBPACK_IMPORTED_MODULE_0__.useTranslation)(ns, options);\n  return children(t, {\n    i18n,\n    lng: i18n.language\n  }, ready);\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL1RyYW5zbGF0aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXFEO0FBQzlDO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNELDJCQUEyQixrRUFBYztBQUN6QztBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0giLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc29ycnlcXERlc2t0b3BcXHRlc3RCTlxccHJvamVjdHNcXGZcXG5vZGVfbW9kdWxlc1xccmVhY3QtaTE4bmV4dFxcZGlzdFxcZXNcXFRyYW5zbGF0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVRyYW5zbGF0aW9uIH0gZnJvbSAnLi91c2VUcmFuc2xhdGlvbi5qcyc7XG5leHBvcnQgY29uc3QgVHJhbnNsYXRpb24gPSAoe1xuICBucyxcbiAgY2hpbGRyZW4sXG4gIC4uLm9wdGlvbnNcbn0pID0+IHtcbiAgY29uc3QgW3QsIGkxOG4sIHJlYWR5XSA9IHVzZVRyYW5zbGF0aW9uKG5zLCBvcHRpb25zKTtcbiAgcmV0dXJuIGNoaWxkcmVuKHQsIHtcbiAgICBpMThuLFxuICAgIGxuZzogaTE4bi5sYW5ndWFnZVxuICB9LCByZWFkeSk7XG59OyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/Translation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/context.js":
/*!*******************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/context.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I18nContext: () => (/* binding */ I18nContext),\n/* harmony export */   ReportNamespaces: () => (/* binding */ ReportNamespaces),\n/* harmony export */   composeInitialProps: () => (/* binding */ composeInitialProps),\n/* harmony export */   getDefaults: () => (/* reexport safe */ _defaults_js__WEBPACK_IMPORTED_MODULE_1__.getDefaults),\n/* harmony export */   getI18n: () => (/* reexport safe */ _i18nInstance_js__WEBPACK_IMPORTED_MODULE_2__.getI18n),\n/* harmony export */   getInitialProps: () => (/* binding */ getInitialProps),\n/* harmony export */   initReactI18next: () => (/* reexport safe */ _initReactI18next_js__WEBPACK_IMPORTED_MODULE_3__.initReactI18next),\n/* harmony export */   setDefaults: () => (/* reexport safe */ _defaults_js__WEBPACK_IMPORTED_MODULE_1__.setDefaults),\n/* harmony export */   setI18n: () => (/* reexport safe */ _i18nInstance_js__WEBPACK_IMPORTED_MODULE_2__.setI18n)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _defaults_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./defaults.js */ \"(ssr)/./node_modules/react-i18next/dist/es/defaults.js\");\n/* harmony import */ var _i18nInstance_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./i18nInstance.js */ \"(ssr)/./node_modules/react-i18next/dist/es/i18nInstance.js\");\n/* harmony import */ var _initReactI18next_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./initReactI18next.js */ \"(ssr)/./node_modules/react-i18next/dist/es/initReactI18next.js\");\n\n\n\n\n\nconst I18nContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)();\nclass ReportNamespaces {\n  constructor() {\n    this.usedNamespaces = {};\n  }\n  addUsedNamespaces(namespaces) {\n    namespaces.forEach(ns => {\n      if (!this.usedNamespaces[ns]) this.usedNamespaces[ns] = true;\n    });\n  }\n  getUsedNamespaces() {\n    return Object.keys(this.usedNamespaces);\n  }\n}\nconst composeInitialProps = ForComponent => async ctx => {\n  const componentsInitialProps = (await ForComponent.getInitialProps?.(ctx)) ?? {};\n  const i18nInitialProps = getInitialProps();\n  return {\n    ...componentsInitialProps,\n    ...i18nInitialProps\n  };\n};\nconst getInitialProps = () => {\n  const i18n = (0,_i18nInstance_js__WEBPACK_IMPORTED_MODULE_2__.getI18n)();\n  const namespaces = i18n.reportNamespaces?.getUsedNamespaces() ?? [];\n  const ret = {};\n  const initialI18nStore = {};\n  i18n.languages.forEach(l => {\n    initialI18nStore[l] = {};\n    namespaces.forEach(ns => {\n      initialI18nStore[l][ns] = i18n.getResourceBundle(l, ns) || {};\n    });\n  });\n  ret.initialI18nStore = initialI18nStore;\n  ret.initialLanguage = i18n.language;\n  return ret;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/context.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/defaults.js":
/*!********************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/defaults.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDefaults: () => (/* binding */ getDefaults),\n/* harmony export */   setDefaults: () => (/* binding */ setDefaults)\n/* harmony export */ });\n/* harmony import */ var _unescape_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./unescape.js */ \"(ssr)/./node_modules/react-i18next/dist/es/unescape.js\");\n\nlet defaultOptions = {\n  bindI18n: 'languageChanged',\n  bindI18nStore: '',\n  transEmptyNodeValue: '',\n  transSupportBasicHtmlNodes: true,\n  transWrapTextNodes: '',\n  transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'p'],\n  useSuspense: true,\n  unescape: _unescape_js__WEBPACK_IMPORTED_MODULE_0__.unescape\n};\nconst setDefaults = (options = {}) => {\n  defaultOptions = {\n    ...defaultOptions,\n    ...options\n  };\n};\nconst getDefaults = () => defaultOptions;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL2RlZmF1bHRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF5QztBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ08saUNBQWlDO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzb3JyeVxcRGVza3RvcFxcdGVzdEJOXFxwcm9qZWN0c1xcZlxcbm9kZV9tb2R1bGVzXFxyZWFjdC1pMThuZXh0XFxkaXN0XFxlc1xcZGVmYXVsdHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdW5lc2NhcGUgfSBmcm9tICcuL3VuZXNjYXBlLmpzJztcbmxldCBkZWZhdWx0T3B0aW9ucyA9IHtcbiAgYmluZEkxOG46ICdsYW5ndWFnZUNoYW5nZWQnLFxuICBiaW5kSTE4blN0b3JlOiAnJyxcbiAgdHJhbnNFbXB0eU5vZGVWYWx1ZTogJycsXG4gIHRyYW5zU3VwcG9ydEJhc2ljSHRtbE5vZGVzOiB0cnVlLFxuICB0cmFuc1dyYXBUZXh0Tm9kZXM6ICcnLFxuICB0cmFuc0tlZXBCYXNpY0h0bWxOb2Rlc0ZvcjogWydicicsICdzdHJvbmcnLCAnaScsICdwJ10sXG4gIHVzZVN1c3BlbnNlOiB0cnVlLFxuICB1bmVzY2FwZVxufTtcbmV4cG9ydCBjb25zdCBzZXREZWZhdWx0cyA9IChvcHRpb25zID0ge30pID0+IHtcbiAgZGVmYXVsdE9wdGlvbnMgPSB7XG4gICAgLi4uZGVmYXVsdE9wdGlvbnMsXG4gICAgLi4ub3B0aW9uc1xuICB9O1xufTtcbmV4cG9ydCBjb25zdCBnZXREZWZhdWx0cyA9ICgpID0+IGRlZmF1bHRPcHRpb25zOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/defaults.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/i18nInstance.js":
/*!************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/i18nInstance.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getI18n: () => (/* binding */ getI18n),\n/* harmony export */   setI18n: () => (/* binding */ setI18n)\n/* harmony export */ });\nlet i18nInstance;\nconst setI18n = instance => {\n  i18nInstance = instance;\n};\nconst getI18n = () => i18nInstance;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL2kxOG5JbnN0YW5jZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ087QUFDUDtBQUNBO0FBQ08iLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc29ycnlcXERlc2t0b3BcXHRlc3RCTlxccHJvamVjdHNcXGZcXG5vZGVfbW9kdWxlc1xccmVhY3QtaTE4bmV4dFxcZGlzdFxcZXNcXGkxOG5JbnN0YW5jZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJsZXQgaTE4bkluc3RhbmNlO1xuZXhwb3J0IGNvbnN0IHNldEkxOG4gPSBpbnN0YW5jZSA9PiB7XG4gIGkxOG5JbnN0YW5jZSA9IGluc3RhbmNlO1xufTtcbmV4cG9ydCBjb25zdCBnZXRJMThuID0gKCkgPT4gaTE4bkluc3RhbmNlOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/i18nInstance.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I18nContext: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_11__.I18nContext),\n/* harmony export */   I18nextProvider: () => (/* reexport safe */ _I18nextProvider_js__WEBPACK_IMPORTED_MODULE_5__.I18nextProvider),\n/* harmony export */   Trans: () => (/* reexport safe */ _Trans_js__WEBPACK_IMPORTED_MODULE_0__.Trans),\n/* harmony export */   TransWithoutContext: () => (/* reexport safe */ _TransWithoutContext_js__WEBPACK_IMPORTED_MODULE_1__.Trans),\n/* harmony export */   Translation: () => (/* reexport safe */ _Translation_js__WEBPACK_IMPORTED_MODULE_4__.Translation),\n/* harmony export */   composeInitialProps: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_11__.composeInitialProps),\n/* harmony export */   date: () => (/* binding */ date),\n/* harmony export */   getDefaults: () => (/* reexport safe */ _defaults_js__WEBPACK_IMPORTED_MODULE_9__.getDefaults),\n/* harmony export */   getI18n: () => (/* reexport safe */ _i18nInstance_js__WEBPACK_IMPORTED_MODULE_10__.getI18n),\n/* harmony export */   getInitialProps: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_11__.getInitialProps),\n/* harmony export */   initReactI18next: () => (/* reexport safe */ _initReactI18next_js__WEBPACK_IMPORTED_MODULE_8__.initReactI18next),\n/* harmony export */   number: () => (/* binding */ number),\n/* harmony export */   plural: () => (/* binding */ plural),\n/* harmony export */   select: () => (/* binding */ select),\n/* harmony export */   selectOrdinal: () => (/* binding */ selectOrdinal),\n/* harmony export */   setDefaults: () => (/* reexport safe */ _defaults_js__WEBPACK_IMPORTED_MODULE_9__.setDefaults),\n/* harmony export */   setI18n: () => (/* reexport safe */ _i18nInstance_js__WEBPACK_IMPORTED_MODULE_10__.setI18n),\n/* harmony export */   time: () => (/* binding */ time),\n/* harmony export */   useSSR: () => (/* reexport safe */ _useSSR_js__WEBPACK_IMPORTED_MODULE_7__.useSSR),\n/* harmony export */   useTranslation: () => (/* reexport safe */ _useTranslation_js__WEBPACK_IMPORTED_MODULE_2__.useTranslation),\n/* harmony export */   withSSR: () => (/* reexport safe */ _withSSR_js__WEBPACK_IMPORTED_MODULE_6__.withSSR),\n/* harmony export */   withTranslation: () => (/* reexport safe */ _withTranslation_js__WEBPACK_IMPORTED_MODULE_3__.withTranslation)\n/* harmony export */ });\n/* harmony import */ var _Trans_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Trans.js */ \"(ssr)/./node_modules/react-i18next/dist/es/Trans.js\");\n/* harmony import */ var _TransWithoutContext_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./TransWithoutContext.js */ \"(ssr)/./node_modules/react-i18next/dist/es/TransWithoutContext.js\");\n/* harmony import */ var _useTranslation_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useTranslation.js */ \"(ssr)/./node_modules/react-i18next/dist/es/useTranslation.js\");\n/* harmony import */ var _withTranslation_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./withTranslation.js */ \"(ssr)/./node_modules/react-i18next/dist/es/withTranslation.js\");\n/* harmony import */ var _Translation_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Translation.js */ \"(ssr)/./node_modules/react-i18next/dist/es/Translation.js\");\n/* harmony import */ var _I18nextProvider_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./I18nextProvider.js */ \"(ssr)/./node_modules/react-i18next/dist/es/I18nextProvider.js\");\n/* harmony import */ var _withSSR_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./withSSR.js */ \"(ssr)/./node_modules/react-i18next/dist/es/withSSR.js\");\n/* harmony import */ var _useSSR_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./useSSR.js */ \"(ssr)/./node_modules/react-i18next/dist/es/useSSR.js\");\n/* harmony import */ var _initReactI18next_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./initReactI18next.js */ \"(ssr)/./node_modules/react-i18next/dist/es/initReactI18next.js\");\n/* harmony import */ var _defaults_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./defaults.js */ \"(ssr)/./node_modules/react-i18next/dist/es/defaults.js\");\n/* harmony import */ var _i18nInstance_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./i18nInstance.js */ \"(ssr)/./node_modules/react-i18next/dist/es/i18nInstance.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./context.js */ \"(ssr)/./node_modules/react-i18next/dist/es/context.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nconst date = () => '';\nconst time = () => '';\nconst number = () => '';\nconst select = () => '';\nconst plural = () => '';\nconst selectOrdinal = () => '';//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBbUM7QUFDcUM7QUFDbkI7QUFDRTtBQUNSO0FBQ1E7QUFDaEI7QUFDRjtBQUNvQjtBQUNBO0FBQ0o7QUFDNEI7QUFDMUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNvcnJ5XFxEZXNrdG9wXFx0ZXN0Qk5cXHByb2plY3RzXFxmXFxub2RlX21vZHVsZXNcXHJlYWN0LWkxOG5leHRcXGRpc3RcXGVzXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBUcmFucyB9IGZyb20gJy4vVHJhbnMuanMnO1xuZXhwb3J0IHsgVHJhbnMgYXMgVHJhbnNXaXRob3V0Q29udGV4dCB9IGZyb20gJy4vVHJhbnNXaXRob3V0Q29udGV4dC5qcyc7XG5leHBvcnQgeyB1c2VUcmFuc2xhdGlvbiB9IGZyb20gJy4vdXNlVHJhbnNsYXRpb24uanMnO1xuZXhwb3J0IHsgd2l0aFRyYW5zbGF0aW9uIH0gZnJvbSAnLi93aXRoVHJhbnNsYXRpb24uanMnO1xuZXhwb3J0IHsgVHJhbnNsYXRpb24gfSBmcm9tICcuL1RyYW5zbGF0aW9uLmpzJztcbmV4cG9ydCB7IEkxOG5leHRQcm92aWRlciB9IGZyb20gJy4vSTE4bmV4dFByb3ZpZGVyLmpzJztcbmV4cG9ydCB7IHdpdGhTU1IgfSBmcm9tICcuL3dpdGhTU1IuanMnO1xuZXhwb3J0IHsgdXNlU1NSIH0gZnJvbSAnLi91c2VTU1IuanMnO1xuZXhwb3J0IHsgaW5pdFJlYWN0STE4bmV4dCB9IGZyb20gJy4vaW5pdFJlYWN0STE4bmV4dC5qcyc7XG5leHBvcnQgeyBzZXREZWZhdWx0cywgZ2V0RGVmYXVsdHMgfSBmcm9tICcuL2RlZmF1bHRzLmpzJztcbmV4cG9ydCB7IHNldEkxOG4sIGdldEkxOG4gfSBmcm9tICcuL2kxOG5JbnN0YW5jZS5qcyc7XG5leHBvcnQgeyBJMThuQ29udGV4dCwgY29tcG9zZUluaXRpYWxQcm9wcywgZ2V0SW5pdGlhbFByb3BzIH0gZnJvbSAnLi9jb250ZXh0LmpzJztcbmV4cG9ydCBjb25zdCBkYXRlID0gKCkgPT4gJyc7XG5leHBvcnQgY29uc3QgdGltZSA9ICgpID0+ICcnO1xuZXhwb3J0IGNvbnN0IG51bWJlciA9ICgpID0+ICcnO1xuZXhwb3J0IGNvbnN0IHNlbGVjdCA9ICgpID0+ICcnO1xuZXhwb3J0IGNvbnN0IHBsdXJhbCA9ICgpID0+ICcnO1xuZXhwb3J0IGNvbnN0IHNlbGVjdE9yZGluYWwgPSAoKSA9PiAnJzsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/initReactI18next.js":
/*!****************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/initReactI18next.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initReactI18next: () => (/* binding */ initReactI18next)\n/* harmony export */ });\n/* harmony import */ var _defaults_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./defaults.js */ \"(ssr)/./node_modules/react-i18next/dist/es/defaults.js\");\n/* harmony import */ var _i18nInstance_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./i18nInstance.js */ \"(ssr)/./node_modules/react-i18next/dist/es/i18nInstance.js\");\n\n\nconst initReactI18next = {\n  type: '3rdParty',\n  init(instance) {\n    (0,_defaults_js__WEBPACK_IMPORTED_MODULE_0__.setDefaults)(instance.options.react);\n    (0,_i18nInstance_js__WEBPACK_IMPORTED_MODULE_1__.setI18n)(instance);\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL2luaXRSZWFjdEkxOG5leHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0E7QUFDckM7QUFDUDtBQUNBO0FBQ0EsSUFBSSx5REFBVztBQUNmLElBQUkseURBQU87QUFDWDtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNvcnJ5XFxEZXNrdG9wXFx0ZXN0Qk5cXHByb2plY3RzXFxmXFxub2RlX21vZHVsZXNcXHJlYWN0LWkxOG5leHRcXGRpc3RcXGVzXFxpbml0UmVhY3RJMThuZXh0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHNldERlZmF1bHRzIH0gZnJvbSAnLi9kZWZhdWx0cy5qcyc7XG5pbXBvcnQgeyBzZXRJMThuIH0gZnJvbSAnLi9pMThuSW5zdGFuY2UuanMnO1xuZXhwb3J0IGNvbnN0IGluaXRSZWFjdEkxOG5leHQgPSB7XG4gIHR5cGU6ICczcmRQYXJ0eScsXG4gIGluaXQoaW5zdGFuY2UpIHtcbiAgICBzZXREZWZhdWx0cyhpbnN0YW5jZS5vcHRpb25zLnJlYWN0KTtcbiAgICBzZXRJMThuKGluc3RhbmNlKTtcbiAgfVxufTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/initReactI18next.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/unescape.js":
/*!********************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/unescape.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unescape: () => (/* binding */ unescape)\n/* harmony export */ });\nconst matchHtmlEntity = /&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g;\nconst htmlEntities = {\n  '&amp;': '&',\n  '&#38;': '&',\n  '&lt;': '<',\n  '&#60;': '<',\n  '&gt;': '>',\n  '&#62;': '>',\n  '&apos;': \"'\",\n  '&#39;': \"'\",\n  '&quot;': '\"',\n  '&#34;': '\"',\n  '&nbsp;': ' ',\n  '&#160;': ' ',\n  '&copy;': '©',\n  '&#169;': '©',\n  '&reg;': '®',\n  '&#174;': '®',\n  '&hellip;': '…',\n  '&#8230;': '…',\n  '&#x2F;': '/',\n  '&#47;': '/'\n};\nconst unescapeHtmlEntity = m => htmlEntities[m];\nconst unescape = text => text.replace(matchHtmlEntity, unescapeHtmlEntity);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL3VuZXNjYXBlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSx5SEFBeUg7QUFDekg7QUFDQSxRQUFRO0FBQ1IsUUFBUTtBQUNSLE9BQU87QUFDUCxRQUFRO0FBQ1IsT0FBTztBQUNQLFFBQVE7QUFDUixTQUFTO0FBQ1QsUUFBUTtBQUNSLFNBQVM7QUFDVCxRQUFRO0FBQ1IsU0FBUztBQUNULFNBQVM7QUFDVCxTQUFTO0FBQ1QsU0FBUztBQUNULFFBQVE7QUFDUixTQUFTO0FBQ1QsV0FBVztBQUNYLFVBQVU7QUFDVixTQUFTO0FBQ1QsUUFBUTtBQUNSO0FBQ0E7QUFDTyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzb3JyeVxcRGVza3RvcFxcdGVzdEJOXFxwcm9qZWN0c1xcZlxcbm9kZV9tb2R1bGVzXFxyZWFjdC1pMThuZXh0XFxkaXN0XFxlc1xcdW5lc2NhcGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgbWF0Y2hIdG1sRW50aXR5ID0gLyYoPzphbXB8IzM4fGx0fCM2MHxndHwjNjJ8YXBvc3wjMzl8cXVvdHwjMzR8bmJzcHwjMTYwfGNvcHl8IzE2OXxyZWd8IzE3NHxoZWxsaXB8IzgyMzB8I3gyRnwjNDcpOy9nO1xuY29uc3QgaHRtbEVudGl0aWVzID0ge1xuICAnJmFtcDsnOiAnJicsXG4gICcmIzM4Oyc6ICcmJyxcbiAgJyZsdDsnOiAnPCcsXG4gICcmIzYwOyc6ICc8JyxcbiAgJyZndDsnOiAnPicsXG4gICcmIzYyOyc6ICc+JyxcbiAgJyZhcG9zOyc6IFwiJ1wiLFxuICAnJiMzOTsnOiBcIidcIixcbiAgJyZxdW90Oyc6ICdcIicsXG4gICcmIzM0Oyc6ICdcIicsXG4gICcmbmJzcDsnOiAnICcsXG4gICcmIzE2MDsnOiAnICcsXG4gICcmY29weTsnOiAnwqknLFxuICAnJiMxNjk7JzogJ8KpJyxcbiAgJyZyZWc7JzogJ8KuJyxcbiAgJyYjMTc0Oyc6ICfCricsXG4gICcmaGVsbGlwOyc6ICfigKYnLFxuICAnJiM4MjMwOyc6ICfigKYnLFxuICAnJiN4MkY7JzogJy8nLFxuICAnJiM0NzsnOiAnLydcbn07XG5jb25zdCB1bmVzY2FwZUh0bWxFbnRpdHkgPSBtID0+IGh0bWxFbnRpdGllc1ttXTtcbmV4cG9ydCBjb25zdCB1bmVzY2FwZSA9IHRleHQgPT4gdGV4dC5yZXBsYWNlKG1hdGNoSHRtbEVudGl0eSwgdW5lc2NhcGVIdG1sRW50aXR5KTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/unescape.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/useSSR.js":
/*!******************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/useSSR.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSSR: () => (/* binding */ useSSR)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./context.js */ \"(ssr)/./node_modules/react-i18next/dist/es/context.js\");\n\n\nconst useSSR = (initialI18nStore, initialLanguage, props = {}) => {\n  const {\n    i18n: i18nFromProps\n  } = props;\n  const {\n    i18n: i18nFromContext\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_js__WEBPACK_IMPORTED_MODULE_1__.I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || (0,_context_js__WEBPACK_IMPORTED_MODULE_1__.getI18n)();\n  if (i18n.options?.isClone) return;\n  if (initialI18nStore && !i18n.initializedStoreOnce) {\n    i18n.services.resourceStore.data = initialI18nStore;\n    i18n.options.ns = Object.values(initialI18nStore).reduce((mem, lngResources) => {\n      Object.keys(lngResources).forEach(ns => {\n        if (mem.indexOf(ns) < 0) mem.push(ns);\n      });\n      return mem;\n    }, i18n.options.ns);\n    i18n.initializedStoreOnce = true;\n    i18n.isInitialized = true;\n  }\n  if (initialLanguage && !i18n.initializedLanguageOnce) {\n    i18n.changeLanguage(initialLanguage);\n    i18n.initializedLanguageOnce = true;\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL3VzZVNTUi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBbUM7QUFDaUI7QUFDN0MsNkRBQTZEO0FBQ3BFO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBLElBQUksRUFBRSxpREFBVSxDQUFDLG9EQUFXO0FBQzVCLG1EQUFtRCxvREFBTztBQUMxRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzb3JyeVxcRGVza3RvcFxcdGVzdEJOXFxwcm9qZWN0c1xcZlxcbm9kZV9tb2R1bGVzXFxyZWFjdC1pMThuZXh0XFxkaXN0XFxlc1xcdXNlU1NSLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUNvbnRleHQgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBnZXRJMThuLCBJMThuQ29udGV4dCB9IGZyb20gJy4vY29udGV4dC5qcyc7XG5leHBvcnQgY29uc3QgdXNlU1NSID0gKGluaXRpYWxJMThuU3RvcmUsIGluaXRpYWxMYW5ndWFnZSwgcHJvcHMgPSB7fSkgPT4ge1xuICBjb25zdCB7XG4gICAgaTE4bjogaTE4bkZyb21Qcm9wc1xuICB9ID0gcHJvcHM7XG4gIGNvbnN0IHtcbiAgICBpMThuOiBpMThuRnJvbUNvbnRleHRcbiAgfSA9IHVzZUNvbnRleHQoSTE4bkNvbnRleHQpIHx8IHt9O1xuICBjb25zdCBpMThuID0gaTE4bkZyb21Qcm9wcyB8fCBpMThuRnJvbUNvbnRleHQgfHwgZ2V0STE4bigpO1xuICBpZiAoaTE4bi5vcHRpb25zPy5pc0Nsb25lKSByZXR1cm47XG4gIGlmIChpbml0aWFsSTE4blN0b3JlICYmICFpMThuLmluaXRpYWxpemVkU3RvcmVPbmNlKSB7XG4gICAgaTE4bi5zZXJ2aWNlcy5yZXNvdXJjZVN0b3JlLmRhdGEgPSBpbml0aWFsSTE4blN0b3JlO1xuICAgIGkxOG4ub3B0aW9ucy5ucyA9IE9iamVjdC52YWx1ZXMoaW5pdGlhbEkxOG5TdG9yZSkucmVkdWNlKChtZW0sIGxuZ1Jlc291cmNlcykgPT4ge1xuICAgICAgT2JqZWN0LmtleXMobG5nUmVzb3VyY2VzKS5mb3JFYWNoKG5zID0+IHtcbiAgICAgICAgaWYgKG1lbS5pbmRleE9mKG5zKSA8IDApIG1lbS5wdXNoKG5zKTtcbiAgICAgIH0pO1xuICAgICAgcmV0dXJuIG1lbTtcbiAgICB9LCBpMThuLm9wdGlvbnMubnMpO1xuICAgIGkxOG4uaW5pdGlhbGl6ZWRTdG9yZU9uY2UgPSB0cnVlO1xuICAgIGkxOG4uaXNJbml0aWFsaXplZCA9IHRydWU7XG4gIH1cbiAgaWYgKGluaXRpYWxMYW5ndWFnZSAmJiAhaTE4bi5pbml0aWFsaXplZExhbmd1YWdlT25jZSkge1xuICAgIGkxOG4uY2hhbmdlTGFuZ3VhZ2UoaW5pdGlhbExhbmd1YWdlKTtcbiAgICBpMThuLmluaXRpYWxpemVkTGFuZ3VhZ2VPbmNlID0gdHJ1ZTtcbiAgfVxufTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/useSSR.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/useTranslation.js":
/*!**************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/useTranslation.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTranslation: () => (/* binding */ useTranslation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./context.js */ \"(ssr)/./node_modules/react-i18next/dist/es/context.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/react-i18next/dist/es/utils.js\");\n\n\n\nconst usePrevious = (value, ignore) => {\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    ref.current = ignore ? ref.current : value;\n  }, [value, ignore]);\n  return ref.current;\n};\nconst alwaysNewT = (i18n, language, namespace, keyPrefix) => i18n.getFixedT(language, namespace, keyPrefix);\nconst useMemoizedT = (i18n, language, namespace, keyPrefix) => (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(alwaysNewT(i18n, language, namespace, keyPrefix), [i18n, language, namespace, keyPrefix]);\nconst useTranslation = (ns, props = {}) => {\n  const {\n    i18n: i18nFromProps\n  } = props;\n  const {\n    i18n: i18nFromContext,\n    defaultNS: defaultNSFromContext\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_js__WEBPACK_IMPORTED_MODULE_1__.I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || (0,_context_js__WEBPACK_IMPORTED_MODULE_1__.getI18n)();\n  if (i18n && !i18n.reportNamespaces) i18n.reportNamespaces = new _context_js__WEBPACK_IMPORTED_MODULE_1__.ReportNamespaces();\n  if (!i18n) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warnOnce)(i18n, 'NO_I18NEXT_INSTANCE', 'useTranslation: You will need to pass in an i18next instance by using initReactI18next');\n    const notReadyT = (k, optsOrDefaultValue) => {\n      if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(optsOrDefaultValue)) return optsOrDefaultValue;\n      if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(optsOrDefaultValue) && (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(optsOrDefaultValue.defaultValue)) return optsOrDefaultValue.defaultValue;\n      return Array.isArray(k) ? k[k.length - 1] : k;\n    };\n    const retNotReady = [notReadyT, {}, false];\n    retNotReady.t = notReadyT;\n    retNotReady.i18n = {};\n    retNotReady.ready = false;\n    return retNotReady;\n  }\n  if (i18n.options.react?.wait) (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warnOnce)(i18n, 'DEPRECATED_OPTION', 'useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.');\n  const i18nOptions = {\n    ...(0,_context_js__WEBPACK_IMPORTED_MODULE_1__.getDefaults)(),\n    ...i18n.options.react,\n    ...props\n  };\n  const {\n    useSuspense,\n    keyPrefix\n  } = i18nOptions;\n  let namespaces = ns || defaultNSFromContext || i18n.options?.defaultNS;\n  namespaces = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(namespaces) ? [namespaces] : namespaces || ['translation'];\n  i18n.reportNamespaces.addUsedNamespaces?.(namespaces);\n  const ready = (i18n.isInitialized || i18n.initializedStoreOnce) && namespaces.every(n => (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.hasLoadedNamespace)(n, i18n, i18nOptions));\n  const memoGetT = useMemoizedT(i18n, props.lng || null, i18nOptions.nsMode === 'fallback' ? namespaces : namespaces[0], keyPrefix);\n  const getT = () => memoGetT;\n  const getNewT = () => alwaysNewT(i18n, props.lng || null, i18nOptions.nsMode === 'fallback' ? namespaces : namespaces[0], keyPrefix);\n  const [t, setT] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(getT);\n  let joinedNS = namespaces.join();\n  if (props.lng) joinedNS = `${props.lng}${joinedNS}`;\n  const previousJoinedNS = usePrevious(joinedNS);\n  const isMounted = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(true);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const {\n      bindI18n,\n      bindI18nStore\n    } = i18nOptions;\n    isMounted.current = true;\n    if (!ready && !useSuspense) {\n      if (props.lng) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.loadLanguages)(i18n, props.lng, namespaces, () => {\n          if (isMounted.current) setT(getNewT);\n        });\n      } else {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.loadNamespaces)(i18n, namespaces, () => {\n          if (isMounted.current) setT(getNewT);\n        });\n      }\n    }\n    if (ready && previousJoinedNS && previousJoinedNS !== joinedNS && isMounted.current) {\n      setT(getNewT);\n    }\n    const boundReset = () => {\n      if (isMounted.current) setT(getNewT);\n    };\n    if (bindI18n) i18n?.on(bindI18n, boundReset);\n    if (bindI18nStore) i18n?.store.on(bindI18nStore, boundReset);\n    return () => {\n      isMounted.current = false;\n      if (i18n && bindI18n) bindI18n?.split(' ').forEach(e => i18n.off(e, boundReset));\n      if (bindI18nStore && i18n) bindI18nStore.split(' ').forEach(e => i18n.store.off(e, boundReset));\n    };\n  }, [i18n, joinedNS]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (isMounted.current && ready) {\n      setT(getT);\n    }\n  }, [i18n, keyPrefix, ready]);\n  const ret = [t, i18n, ready];\n  ret.t = t;\n  ret.i18n = i18n;\n  ret.ready = ready;\n  if (ready) return ret;\n  if (!ready && !useSuspense) return ret;\n  throw new Promise(resolve => {\n    if (props.lng) {\n      (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.loadLanguages)(i18n, props.lng, namespaces, () => resolve());\n    } else {\n      (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.loadNamespaces)(i18n, namespaces, () => resolve());\n    }\n  });\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/useTranslation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/utils.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/utils.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDisplayName: () => (/* binding */ getDisplayName),\n/* harmony export */   hasLoadedNamespace: () => (/* binding */ hasLoadedNamespace),\n/* harmony export */   isObject: () => (/* binding */ isObject),\n/* harmony export */   isString: () => (/* binding */ isString),\n/* harmony export */   loadLanguages: () => (/* binding */ loadLanguages),\n/* harmony export */   loadNamespaces: () => (/* binding */ loadNamespaces),\n/* harmony export */   warn: () => (/* binding */ warn),\n/* harmony export */   warnOnce: () => (/* binding */ warnOnce)\n/* harmony export */ });\nconst warn = (i18n, code, msg, rest) => {\n  const args = [msg, {\n    code,\n    ...(rest || {})\n  }];\n  if (i18n?.services?.logger?.forward) {\n    return i18n.services.logger.forward(args, 'warn', 'react-i18next::', true);\n  }\n  if (isString(args[0])) args[0] = `react-i18next:: ${args[0]}`;\n  if (i18n?.services?.logger?.warn) {\n    i18n.services.logger.warn(...args);\n  } else if (console?.warn) {\n    console.warn(...args);\n  }\n};\nconst alreadyWarned = {};\nconst warnOnce = (i18n, code, msg, rest) => {\n  if (isString(msg) && alreadyWarned[msg]) return;\n  if (isString(msg)) alreadyWarned[msg] = new Date();\n  warn(i18n, code, msg, rest);\n};\nconst loadedClb = (i18n, cb) => () => {\n  if (i18n.isInitialized) {\n    cb();\n  } else {\n    const initialized = () => {\n      setTimeout(() => {\n        i18n.off('initialized', initialized);\n      }, 0);\n      cb();\n    };\n    i18n.on('initialized', initialized);\n  }\n};\nconst loadNamespaces = (i18n, ns, cb) => {\n  i18n.loadNamespaces(ns, loadedClb(i18n, cb));\n};\nconst loadLanguages = (i18n, lng, ns, cb) => {\n  if (isString(ns)) ns = [ns];\n  if (i18n.options.preload && i18n.options.preload.indexOf(lng) > -1) return loadNamespaces(i18n, ns, cb);\n  ns.forEach(n => {\n    if (i18n.options.ns.indexOf(n) < 0) i18n.options.ns.push(n);\n  });\n  i18n.loadLanguages(lng, loadedClb(i18n, cb));\n};\nconst hasLoadedNamespace = (ns, i18n, options = {}) => {\n  if (!i18n.languages || !i18n.languages.length) {\n    warnOnce(i18n, 'NO_LANGUAGES', 'i18n.languages were undefined or empty', {\n      languages: i18n.languages\n    });\n    return true;\n  }\n  return i18n.hasLoadedNamespace(ns, {\n    lng: options.lng,\n    precheck: (i18nInstance, loadNotPending) => {\n      if (options.bindI18n && options.bindI18n.indexOf('languageChanging') > -1 && i18nInstance.services.backendConnector.backend && i18nInstance.isLanguageChangingTo && !loadNotPending(i18nInstance.isLanguageChangingTo, ns)) return false;\n    }\n  });\n};\nconst getDisplayName = Component => Component.displayName || Component.name || (isString(Component) && Component.length > 0 ? Component : 'Unknown');\nconst isString = obj => typeof obj === 'string';\nconst isObject = obj => typeof obj === 'object' && obj !== null;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/withSSR.js":
/*!*******************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/withSSR.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   withSSR: () => (/* binding */ withSSR)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _useSSR_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useSSR.js */ \"(ssr)/./node_modules/react-i18next/dist/es/useSSR.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./context.js */ \"(ssr)/./node_modules/react-i18next/dist/es/context.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/react-i18next/dist/es/utils.js\");\n\n\n\n\nconst withSSR = () => function Extend(WrappedComponent) {\n  function I18nextWithSSR({\n    initialI18nStore,\n    initialLanguage,\n    ...rest\n  }) {\n    (0,_useSSR_js__WEBPACK_IMPORTED_MODULE_1__.useSSR)(initialI18nStore, initialLanguage);\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(WrappedComponent, {\n      ...rest\n    });\n  }\n  I18nextWithSSR.getInitialProps = (0,_context_js__WEBPACK_IMPORTED_MODULE_2__.composeInitialProps)(WrappedComponent);\n  I18nextWithSSR.displayName = `withI18nextSSR(${(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.getDisplayName)(WrappedComponent)})`;\n  I18nextWithSSR.WrappedComponent = WrappedComponent;\n  return I18nextWithSSR;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL3dpdGhTU1IuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBc0M7QUFDRDtBQUNjO0FBQ1A7QUFDckM7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCxJQUFJLGtEQUFNO0FBQ1YsV0FBVyxvREFBYTtBQUN4QjtBQUNBLEtBQUs7QUFDTDtBQUNBLG1DQUFtQyxnRUFBbUI7QUFDdEQsaURBQWlELHlEQUFjLG1CQUFtQjtBQUNsRjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc29ycnlcXERlc2t0b3BcXHRlc3RCTlxccHJvamVjdHNcXGZcXG5vZGVfbW9kdWxlc1xccmVhY3QtaTE4bmV4dFxcZGlzdFxcZXNcXHdpdGhTU1IuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlRWxlbWVudCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVNTUiB9IGZyb20gJy4vdXNlU1NSLmpzJztcbmltcG9ydCB7IGNvbXBvc2VJbml0aWFsUHJvcHMgfSBmcm9tICcuL2NvbnRleHQuanMnO1xuaW1wb3J0IHsgZ2V0RGlzcGxheU5hbWUgfSBmcm9tICcuL3V0aWxzLmpzJztcbmV4cG9ydCBjb25zdCB3aXRoU1NSID0gKCkgPT4gZnVuY3Rpb24gRXh0ZW5kKFdyYXBwZWRDb21wb25lbnQpIHtcbiAgZnVuY3Rpb24gSTE4bmV4dFdpdGhTU1Ioe1xuICAgIGluaXRpYWxJMThuU3RvcmUsXG4gICAgaW5pdGlhbExhbmd1YWdlLFxuICAgIC4uLnJlc3RcbiAgfSkge1xuICAgIHVzZVNTUihpbml0aWFsSTE4blN0b3JlLCBpbml0aWFsTGFuZ3VhZ2UpO1xuICAgIHJldHVybiBjcmVhdGVFbGVtZW50KFdyYXBwZWRDb21wb25lbnQsIHtcbiAgICAgIC4uLnJlc3RcbiAgICB9KTtcbiAgfVxuICBJMThuZXh0V2l0aFNTUi5nZXRJbml0aWFsUHJvcHMgPSBjb21wb3NlSW5pdGlhbFByb3BzKFdyYXBwZWRDb21wb25lbnQpO1xuICBJMThuZXh0V2l0aFNTUi5kaXNwbGF5TmFtZSA9IGB3aXRoSTE4bmV4dFNTUigke2dldERpc3BsYXlOYW1lKFdyYXBwZWRDb21wb25lbnQpfSlgO1xuICBJMThuZXh0V2l0aFNTUi5XcmFwcGVkQ29tcG9uZW50ID0gV3JhcHBlZENvbXBvbmVudDtcbiAgcmV0dXJuIEkxOG5leHRXaXRoU1NSO1xufTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/withSSR.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/withTranslation.js":
/*!***************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/withTranslation.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   withTranslation: () => (/* binding */ withTranslation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _useTranslation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useTranslation.js */ \"(ssr)/./node_modules/react-i18next/dist/es/useTranslation.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/react-i18next/dist/es/utils.js\");\n\n\n\nconst withTranslation = (ns, options = {}) => function Extend(WrappedComponent) {\n  function I18nextWithTranslation({\n    forwardedRef,\n    ...rest\n  }) {\n    const [t, i18n, ready] = (0,_useTranslation_js__WEBPACK_IMPORTED_MODULE_1__.useTranslation)(ns, {\n      ...rest,\n      keyPrefix: options.keyPrefix\n    });\n    const passDownProps = {\n      ...rest,\n      t,\n      i18n,\n      tReady: ready\n    };\n    if (options.withRef && forwardedRef) {\n      passDownProps.ref = forwardedRef;\n    } else if (!options.withRef && forwardedRef) {\n      passDownProps.forwardedRef = forwardedRef;\n    }\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(WrappedComponent, passDownProps);\n  }\n  I18nextWithTranslation.displayName = `withI18nextTranslation(${(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.getDisplayName)(WrappedComponent)})`;\n  I18nextWithTranslation.WrappedComponent = WrappedComponent;\n  const forwardRef = (props, ref) => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(I18nextWithTranslation, Object.assign({}, props, {\n    forwardedRef: ref\n  }));\n  return options.withRef ? (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(forwardRef) : I18nextWithTranslation;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/withTranslation.js\n");

/***/ })

};
;