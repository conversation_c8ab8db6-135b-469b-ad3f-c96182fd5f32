"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/i18next-browser-languagedetector";
exports.ids = ["vendor-chunks/i18next-browser-languagedetector"];
exports.modules = {

/***/ "(ssr)/./node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Browser)\n/* harmony export */ });\nconst {\n  slice,\n  forEach\n} = [];\nfunction defaults(obj) {\n  forEach.call(slice.call(arguments, 1), source => {\n    if (source) {\n      for (const prop in source) {\n        if (obj[prop] === undefined) obj[prop] = source[prop];\n      }\n    }\n  });\n  return obj;\n}\nfunction hasXSS(input) {\n  if (typeof input !== 'string') return false;\n\n  // Common XSS attack patterns\n  const xssPatterns = [/<\\s*script.*?>/i, /<\\s*\\/\\s*script\\s*>/i, /<\\s*img.*?on\\w+\\s*=/i, /<\\s*\\w+\\s*on\\w+\\s*=.*?>/i, /javascript\\s*:/i, /vbscript\\s*:/i, /expression\\s*\\(/i, /eval\\s*\\(/i, /alert\\s*\\(/i, /document\\.cookie/i, /document\\.write\\s*\\(/i, /window\\.location/i, /innerHTML/i];\n  return xssPatterns.some(pattern => pattern.test(input));\n}\n\n// eslint-disable-next-line no-control-regex\nconst fieldContentRegExp = /^[\\u0009\\u0020-\\u007e\\u0080-\\u00ff]+$/;\nconst serializeCookie = function (name, val) {\n  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {\n    path: '/'\n  };\n  const opt = options;\n  const value = encodeURIComponent(val);\n  let str = `${name}=${value}`;\n  if (opt.maxAge > 0) {\n    const maxAge = opt.maxAge - 0;\n    if (Number.isNaN(maxAge)) throw new Error('maxAge should be a Number');\n    str += `; Max-Age=${Math.floor(maxAge)}`;\n  }\n  if (opt.domain) {\n    if (!fieldContentRegExp.test(opt.domain)) {\n      throw new TypeError('option domain is invalid');\n    }\n    str += `; Domain=${opt.domain}`;\n  }\n  if (opt.path) {\n    if (!fieldContentRegExp.test(opt.path)) {\n      throw new TypeError('option path is invalid');\n    }\n    str += `; Path=${opt.path}`;\n  }\n  if (opt.expires) {\n    if (typeof opt.expires.toUTCString !== 'function') {\n      throw new TypeError('option expires is invalid');\n    }\n    str += `; Expires=${opt.expires.toUTCString()}`;\n  }\n  if (opt.httpOnly) str += '; HttpOnly';\n  if (opt.secure) str += '; Secure';\n  if (opt.sameSite) {\n    const sameSite = typeof opt.sameSite === 'string' ? opt.sameSite.toLowerCase() : opt.sameSite;\n    switch (sameSite) {\n      case true:\n        str += '; SameSite=Strict';\n        break;\n      case 'lax':\n        str += '; SameSite=Lax';\n        break;\n      case 'strict':\n        str += '; SameSite=Strict';\n        break;\n      case 'none':\n        str += '; SameSite=None';\n        break;\n      default:\n        throw new TypeError('option sameSite is invalid');\n    }\n  }\n  if (opt.partitioned) str += '; Partitioned';\n  return str;\n};\nconst cookie = {\n  create(name, value, minutes, domain) {\n    let cookieOptions = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : {\n      path: '/',\n      sameSite: 'strict'\n    };\n    if (minutes) {\n      cookieOptions.expires = new Date();\n      cookieOptions.expires.setTime(cookieOptions.expires.getTime() + minutes * 60 * 1000);\n    }\n    if (domain) cookieOptions.domain = domain;\n    document.cookie = serializeCookie(name, value, cookieOptions);\n  },\n  read(name) {\n    const nameEQ = `${name}=`;\n    const ca = document.cookie.split(';');\n    for (let i = 0; i < ca.length; i++) {\n      let c = ca[i];\n      while (c.charAt(0) === ' ') c = c.substring(1, c.length);\n      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);\n    }\n    return null;\n  },\n  remove(name, domain) {\n    this.create(name, '', -1, domain);\n  }\n};\nvar cookie$1 = {\n  name: 'cookie',\n  // Deconstruct the options object and extract the lookupCookie property\n  lookup(_ref) {\n    let {\n      lookupCookie\n    } = _ref;\n    if (lookupCookie && typeof document !== 'undefined') {\n      return cookie.read(lookupCookie) || undefined;\n    }\n    return undefined;\n  },\n  // Deconstruct the options object and extract the lookupCookie, cookieMinutes, cookieDomain, and cookieOptions properties\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupCookie,\n      cookieMinutes,\n      cookieDomain,\n      cookieOptions\n    } = _ref2;\n    if (lookupCookie && typeof document !== 'undefined') {\n      cookie.create(lookupCookie, lng, cookieMinutes, cookieDomain, cookieOptions);\n    }\n  }\n};\n\nvar querystring = {\n  name: 'querystring',\n  // Deconstruct the options object and extract the lookupQuerystring property\n  lookup(_ref) {\n    let {\n      lookupQuerystring\n    } = _ref;\n    let found;\n    if (typeof window !== 'undefined') {\n      let {\n        search\n      } = window.location;\n      if (!window.location.search && window.location.hash?.indexOf('?') > -1) {\n        search = window.location.hash.substring(window.location.hash.indexOf('?'));\n      }\n      const query = search.substring(1);\n      const params = query.split('&');\n      for (let i = 0; i < params.length; i++) {\n        const pos = params[i].indexOf('=');\n        if (pos > 0) {\n          const key = params[i].substring(0, pos);\n          if (key === lookupQuerystring) {\n            found = params[i].substring(pos + 1);\n          }\n        }\n      }\n    }\n    return found;\n  }\n};\n\nvar hash = {\n  name: 'hash',\n  // Deconstruct the options object and extract the lookupHash property and the lookupFromHashIndex property\n  lookup(_ref) {\n    let {\n      lookupHash,\n      lookupFromHashIndex\n    } = _ref;\n    let found;\n    if (typeof window !== 'undefined') {\n      const {\n        hash\n      } = window.location;\n      if (hash && hash.length > 2) {\n        const query = hash.substring(1);\n        if (lookupHash) {\n          const params = query.split('&');\n          for (let i = 0; i < params.length; i++) {\n            const pos = params[i].indexOf('=');\n            if (pos > 0) {\n              const key = params[i].substring(0, pos);\n              if (key === lookupHash) {\n                found = params[i].substring(pos + 1);\n              }\n            }\n          }\n        }\n        if (found) return found;\n        if (!found && lookupFromHashIndex > -1) {\n          const language = hash.match(/\\/([a-zA-Z-]*)/g);\n          if (!Array.isArray(language)) return undefined;\n          const index = typeof lookupFromHashIndex === 'number' ? lookupFromHashIndex : 0;\n          return language[index]?.replace('/', '');\n        }\n      }\n    }\n    return found;\n  }\n};\n\nlet hasLocalStorageSupport = null;\nconst localStorageAvailable = () => {\n  if (hasLocalStorageSupport !== null) return hasLocalStorageSupport;\n  try {\n    hasLocalStorageSupport = typeof window !== 'undefined' && window.localStorage !== null;\n    if (!hasLocalStorageSupport) {\n      return false;\n    }\n    const testKey = 'i18next.translate.boo';\n    window.localStorage.setItem(testKey, 'foo');\n    window.localStorage.removeItem(testKey);\n  } catch (e) {\n    hasLocalStorageSupport = false;\n  }\n  return hasLocalStorageSupport;\n};\nvar localStorage = {\n  name: 'localStorage',\n  // Deconstruct the options object and extract the lookupLocalStorage property\n  lookup(_ref) {\n    let {\n      lookupLocalStorage\n    } = _ref;\n    if (lookupLocalStorage && localStorageAvailable()) {\n      return window.localStorage.getItem(lookupLocalStorage) || undefined; // Undefined ensures type consistency with the previous version of this function\n    }\n    return undefined;\n  },\n  // Deconstruct the options object and extract the lookupLocalStorage property\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupLocalStorage\n    } = _ref2;\n    if (lookupLocalStorage && localStorageAvailable()) {\n      window.localStorage.setItem(lookupLocalStorage, lng);\n    }\n  }\n};\n\nlet hasSessionStorageSupport = null;\nconst sessionStorageAvailable = () => {\n  if (hasSessionStorageSupport !== null) return hasSessionStorageSupport;\n  try {\n    hasSessionStorageSupport = typeof window !== 'undefined' && window.sessionStorage !== null;\n    if (!hasSessionStorageSupport) {\n      return false;\n    }\n    const testKey = 'i18next.translate.boo';\n    window.sessionStorage.setItem(testKey, 'foo');\n    window.sessionStorage.removeItem(testKey);\n  } catch (e) {\n    hasSessionStorageSupport = false;\n  }\n  return hasSessionStorageSupport;\n};\nvar sessionStorage = {\n  name: 'sessionStorage',\n  lookup(_ref) {\n    let {\n      lookupSessionStorage\n    } = _ref;\n    if (lookupSessionStorage && sessionStorageAvailable()) {\n      return window.sessionStorage.getItem(lookupSessionStorage) || undefined;\n    }\n    return undefined;\n  },\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupSessionStorage\n    } = _ref2;\n    if (lookupSessionStorage && sessionStorageAvailable()) {\n      window.sessionStorage.setItem(lookupSessionStorage, lng);\n    }\n  }\n};\n\nvar navigator$1 = {\n  name: 'navigator',\n  lookup(options) {\n    const found = [];\n    if (typeof navigator !== 'undefined') {\n      const {\n        languages,\n        userLanguage,\n        language\n      } = navigator;\n      if (languages) {\n        // chrome only; not an array, so can't use .push.apply instead of iterating\n        for (let i = 0; i < languages.length; i++) {\n          found.push(languages[i]);\n        }\n      }\n      if (userLanguage) {\n        found.push(userLanguage);\n      }\n      if (language) {\n        found.push(language);\n      }\n    }\n    return found.length > 0 ? found : undefined;\n  }\n};\n\nvar htmlTag = {\n  name: 'htmlTag',\n  // Deconstruct the options object and extract the htmlTag property\n  lookup(_ref) {\n    let {\n      htmlTag\n    } = _ref;\n    let found;\n    const internalHtmlTag = htmlTag || (typeof document !== 'undefined' ? document.documentElement : null);\n    if (internalHtmlTag && typeof internalHtmlTag.getAttribute === 'function') {\n      found = internalHtmlTag.getAttribute('lang');\n    }\n    return found;\n  }\n};\n\nvar path = {\n  name: 'path',\n  // Deconstruct the options object and extract the lookupFromPathIndex property\n  lookup(_ref) {\n    let {\n      lookupFromPathIndex\n    } = _ref;\n    if (typeof window === 'undefined') return undefined;\n    const language = window.location.pathname.match(/\\/([a-zA-Z-]*)/g);\n    if (!Array.isArray(language)) return undefined;\n    const index = typeof lookupFromPathIndex === 'number' ? lookupFromPathIndex : 0;\n    return language[index]?.replace('/', '');\n  }\n};\n\nvar subdomain = {\n  name: 'subdomain',\n  lookup(_ref) {\n    let {\n      lookupFromSubdomainIndex\n    } = _ref;\n    // If given get the subdomain index else 1\n    const internalLookupFromSubdomainIndex = typeof lookupFromSubdomainIndex === 'number' ? lookupFromSubdomainIndex + 1 : 1;\n    // get all matches if window.location. is existing\n    // first item of match is the match itself and the second is the first group match which should be the first subdomain match\n    // is the hostname no public domain get the or option of localhost\n    const language = typeof window !== 'undefined' && window.location?.hostname?.match(/^(\\w{2,5})\\.(([a-z0-9-]{1,63}\\.[a-z]{2,6})|localhost)/i);\n\n    // if there is no match (null) return undefined\n    if (!language) return undefined;\n    // return the given group match\n    return language[internalLookupFromSubdomainIndex];\n  }\n};\n\n// some environments, throws when accessing document.cookie\nlet canCookies = false;\ntry {\n  // eslint-disable-next-line no-unused-expressions\n  document.cookie;\n  canCookies = true;\n  // eslint-disable-next-line no-empty\n} catch (e) {}\nconst order = ['querystring', 'cookie', 'localStorage', 'sessionStorage', 'navigator', 'htmlTag'];\nif (!canCookies) order.splice(1, 1);\nconst getDefaults = () => ({\n  order,\n  lookupQuerystring: 'lng',\n  lookupCookie: 'i18next',\n  lookupLocalStorage: 'i18nextLng',\n  lookupSessionStorage: 'i18nextLng',\n  // cache user language\n  caches: ['localStorage'],\n  excludeCacheFor: ['cimode'],\n  // cookieMinutes: 10,\n  // cookieDomain: 'myDomain'\n\n  convertDetectedLanguage: l => l\n});\nclass Browser {\n  constructor(services) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.type = 'languageDetector';\n    this.detectors = {};\n    this.init(services, options);\n  }\n  init() {\n    let services = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n      languageUtils: {}\n    };\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let i18nOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    this.services = services;\n    this.options = defaults(options, this.options || {}, getDefaults());\n    if (typeof this.options.convertDetectedLanguage === 'string' && this.options.convertDetectedLanguage.indexOf('15897') > -1) {\n      this.options.convertDetectedLanguage = l => l.replace('-', '_');\n    }\n\n    // backwards compatibility\n    if (this.options.lookupFromUrlIndex) this.options.lookupFromPathIndex = this.options.lookupFromUrlIndex;\n    this.i18nOptions = i18nOptions;\n    this.addDetector(cookie$1);\n    this.addDetector(querystring);\n    this.addDetector(localStorage);\n    this.addDetector(sessionStorage);\n    this.addDetector(navigator$1);\n    this.addDetector(htmlTag);\n    this.addDetector(path);\n    this.addDetector(subdomain);\n    this.addDetector(hash);\n  }\n  addDetector(detector) {\n    this.detectors[detector.name] = detector;\n    return this;\n  }\n  detect() {\n    let detectionOrder = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.options.order;\n    let detected = [];\n    detectionOrder.forEach(detectorName => {\n      if (this.detectors[detectorName]) {\n        let lookup = this.detectors[detectorName].lookup(this.options);\n        if (lookup && typeof lookup === 'string') lookup = [lookup];\n        if (lookup) detected = detected.concat(lookup);\n      }\n    });\n    detected = detected.filter(d => d !== undefined && d !== null && !hasXSS(d)).map(d => this.options.convertDetectedLanguage(d));\n    if (this.services && this.services.languageUtils && this.services.languageUtils.getBestMatchFromCodes) return detected; // new i18next v19.5.0\n    return detected.length > 0 ? detected[0] : null; // a little backward compatibility\n  }\n  cacheUserLanguage(lng) {\n    let caches = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this.options.caches;\n    if (!caches) return;\n    if (this.options.excludeCacheFor && this.options.excludeCacheFor.indexOf(lng) > -1) return;\n    caches.forEach(cacheName => {\n      if (this.detectors[cacheName]) this.detectors[cacheName].cacheUserLanguage(lng, this.options);\n    });\n  }\n}\nBrowser.type = 'languageDetector';\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js\n");

/***/ })

};
;