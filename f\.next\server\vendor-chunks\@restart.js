"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@restart";
exports.ids = ["vendor-chunks/@restart"];
exports.modules = {

/***/ "(ssr)/./node_modules/@restart/hooks/esm/useCallbackRef.js":
/*!***********************************************************!*\
  !*** ./node_modules/@restart/hooks/esm/useCallbackRef.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useCallbackRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\n/**\n * A convenience hook around `useState` designed to be paired with\n * the component [callback ref](https://reactjs.org/docs/refs-and-the-dom.html#callback-refs) api.\n * Callback refs are useful over `useRef()` when you need to respond to the ref being set\n * instead of lazily accessing it in an effect.\n *\n * ```ts\n * const [element, attachRef] = useCallbackRef<HTMLDivElement>()\n *\n * useEffect(() => {\n *   if (!element) return\n *\n *   const calendar = new FullCalendar.Calendar(element)\n *\n *   return () => {\n *     calendar.destroy()\n *   }\n * }, [element])\n *\n * return <div ref={attachRef} />\n * ```\n *\n * @category refs\n */\nfunction useCallbackRef() {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJlc3RhcnQvaG9va3MvZXNtL3VzZUNhbGxiYWNrUmVmLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFpQzs7QUFFakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBLG9CQUFvQixXQUFXO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ2U7QUFDZixTQUFTLCtDQUFRO0FBQ2pCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNvcnJ5XFxEZXNrdG9wXFx0ZXN0Qk5cXHByb2plY3RzXFxmXFxub2RlX21vZHVsZXNcXEByZXN0YXJ0XFxob29rc1xcZXNtXFx1c2VDYWxsYmFja1JlZi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcblxuLyoqXG4gKiBBIGNvbnZlbmllbmNlIGhvb2sgYXJvdW5kIGB1c2VTdGF0ZWAgZGVzaWduZWQgdG8gYmUgcGFpcmVkIHdpdGhcbiAqIHRoZSBjb21wb25lbnQgW2NhbGxiYWNrIHJlZl0oaHR0cHM6Ly9yZWFjdGpzLm9yZy9kb2NzL3JlZnMtYW5kLXRoZS1kb20uaHRtbCNjYWxsYmFjay1yZWZzKSBhcGkuXG4gKiBDYWxsYmFjayByZWZzIGFyZSB1c2VmdWwgb3ZlciBgdXNlUmVmKClgIHdoZW4geW91IG5lZWQgdG8gcmVzcG9uZCB0byB0aGUgcmVmIGJlaW5nIHNldFxuICogaW5zdGVhZCBvZiBsYXppbHkgYWNjZXNzaW5nIGl0IGluIGFuIGVmZmVjdC5cbiAqXG4gKiBgYGB0c1xuICogY29uc3QgW2VsZW1lbnQsIGF0dGFjaFJlZl0gPSB1c2VDYWxsYmFja1JlZjxIVE1MRGl2RWxlbWVudD4oKVxuICpcbiAqIHVzZUVmZmVjdCgoKSA9PiB7XG4gKiAgIGlmICghZWxlbWVudCkgcmV0dXJuXG4gKlxuICogICBjb25zdCBjYWxlbmRhciA9IG5ldyBGdWxsQ2FsZW5kYXIuQ2FsZW5kYXIoZWxlbWVudClcbiAqXG4gKiAgIHJldHVybiAoKSA9PiB7XG4gKiAgICAgY2FsZW5kYXIuZGVzdHJveSgpXG4gKiAgIH1cbiAqIH0sIFtlbGVtZW50XSlcbiAqXG4gKiByZXR1cm4gPGRpdiByZWY9e2F0dGFjaFJlZn0gLz5cbiAqIGBgYFxuICpcbiAqIEBjYXRlZ29yeSByZWZzXG4gKi9cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHVzZUNhbGxiYWNrUmVmKCkge1xuICByZXR1cm4gdXNlU3RhdGUobnVsbCk7XG59Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@restart/hooks/esm/useCallbackRef.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@restart/hooks/esm/useCommittedRef.js":
/*!************************************************************!*\
  !*** ./node_modules/@restart/hooks/esm/useCommittedRef.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\n/**\n * Creates a `Ref` whose value is updated in an effect, ensuring the most recent\n * value is the one rendered with. Generally only required for Concurrent mode usage\n * where previous work in `render()` may be discarded before being used.\n *\n * This is safe to access in an event handler.\n *\n * @param value The `Ref` value\n */\nfunction useCommittedRef(value) {\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(value);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    ref.current = value;\n  }, [value]);\n  return ref;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useCommittedRef);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJlc3RhcnQvaG9va3MvZXNtL3VzZUNvbW1pdHRlZFJlZi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBMEM7O0FBRTFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyw2Q0FBTTtBQUNwQixFQUFFLGdEQUFTO0FBQ1g7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLGlFQUFlLGVBQWUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc29ycnlcXERlc2t0b3BcXHRlc3RCTlxccHJvamVjdHNcXGZcXG5vZGVfbW9kdWxlc1xcQHJlc3RhcnRcXGhvb2tzXFxlc21cXHVzZUNvbW1pdHRlZFJlZi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVJlZiB9IGZyb20gJ3JlYWN0JztcblxuLyoqXG4gKiBDcmVhdGVzIGEgYFJlZmAgd2hvc2UgdmFsdWUgaXMgdXBkYXRlZCBpbiBhbiBlZmZlY3QsIGVuc3VyaW5nIHRoZSBtb3N0IHJlY2VudFxuICogdmFsdWUgaXMgdGhlIG9uZSByZW5kZXJlZCB3aXRoLiBHZW5lcmFsbHkgb25seSByZXF1aXJlZCBmb3IgQ29uY3VycmVudCBtb2RlIHVzYWdlXG4gKiB3aGVyZSBwcmV2aW91cyB3b3JrIGluIGByZW5kZXIoKWAgbWF5IGJlIGRpc2NhcmRlZCBiZWZvcmUgYmVpbmcgdXNlZC5cbiAqXG4gKiBUaGlzIGlzIHNhZmUgdG8gYWNjZXNzIGluIGFuIGV2ZW50IGhhbmRsZXIuXG4gKlxuICogQHBhcmFtIHZhbHVlIFRoZSBgUmVmYCB2YWx1ZVxuICovXG5mdW5jdGlvbiB1c2VDb21taXR0ZWRSZWYodmFsdWUpIHtcbiAgY29uc3QgcmVmID0gdXNlUmVmKHZhbHVlKTtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICByZWYuY3VycmVudCA9IHZhbHVlO1xuICB9LCBbdmFsdWVdKTtcbiAgcmV0dXJuIHJlZjtcbn1cbmV4cG9ydCBkZWZhdWx0IHVzZUNvbW1pdHRlZFJlZjsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@restart/hooks/esm/useCommittedRef.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@restart/hooks/esm/useEventCallback.js":
/*!*************************************************************!*\
  !*** ./node_modules/@restart/hooks/esm/useEventCallback.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useEventCallback)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _useCommittedRef__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useCommittedRef */ \"(ssr)/./node_modules/@restart/hooks/esm/useCommittedRef.js\");\n\n\nfunction useEventCallback(fn) {\n  const ref = (0,_useCommittedRef__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(fn);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (...args) {\n    return ref.current && ref.current(...args);\n  }, [ref]);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJlc3RhcnQvaG9va3MvZXNtL3VzZUV2ZW50Q2FsbGJhY2suanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFvQztBQUNZO0FBQ2pDO0FBQ2YsY0FBYyw0REFBZTtBQUM3QixTQUFTLGtEQUFXO0FBQ3BCO0FBQ0EsR0FBRztBQUNIIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNvcnJ5XFxEZXNrdG9wXFx0ZXN0Qk5cXHByb2plY3RzXFxmXFxub2RlX21vZHVsZXNcXEByZXN0YXJ0XFxob29rc1xcZXNtXFx1c2VFdmVudENhbGxiYWNrLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHVzZUNvbW1pdHRlZFJlZiBmcm9tICcuL3VzZUNvbW1pdHRlZFJlZic7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiB1c2VFdmVudENhbGxiYWNrKGZuKSB7XG4gIGNvbnN0IHJlZiA9IHVzZUNvbW1pdHRlZFJlZihmbik7XG4gIHJldHVybiB1c2VDYWxsYmFjayhmdW5jdGlvbiAoLi4uYXJncykge1xuICAgIHJldHVybiByZWYuY3VycmVudCAmJiByZWYuY3VycmVudCguLi5hcmdzKTtcbiAgfSwgW3JlZl0pO1xufSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@restart/hooks/esm/useEventCallback.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@restart/hooks/esm/useMergedRefs.js":
/*!**********************************************************!*\
  !*** ./node_modules/@restart/hooks/esm/useMergedRefs.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   mergeRefs: () => (/* binding */ mergeRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst toFnRef = ref => !ref || typeof ref === 'function' ? ref : value => {\n  ref.current = value;\n};\nfunction mergeRefs(refA, refB) {\n  const a = toFnRef(refA);\n  const b = toFnRef(refB);\n  return value => {\n    if (a) a(value);\n    if (b) b(value);\n  };\n}\n\n/**\n * Create and returns a single callback ref composed from two other Refs.\n *\n * ```tsx\n * const Button = React.forwardRef((props, ref) => {\n *   const [element, attachRef] = useCallbackRef<HTMLButtonElement>();\n *   const mergedRef = useMergedRefs(ref, attachRef);\n *\n *   return <button ref={mergedRef} {...props}/>\n * })\n * ```\n *\n * @param refA A Callback or mutable Ref\n * @param refB A Callback or mutable Ref\n * @category refs\n */\nfunction useMergedRefs(refA, refB) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => mergeRefs(refA, refB), [refA, refB]);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useMergedRefs);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJlc3RhcnQvaG9va3MvZXNtL3VzZU1lcmdlZFJlZnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFnQztBQUNoQztBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsWUFBWSxTQUFTO0FBQzlDLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMsOENBQU87QUFDaEI7QUFDQSxpRUFBZSxhQUFhIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNvcnJ5XFxEZXNrdG9wXFx0ZXN0Qk5cXHByb2plY3RzXFxmXFxub2RlX21vZHVsZXNcXEByZXN0YXJ0XFxob29rc1xcZXNtXFx1c2VNZXJnZWRSZWZzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZU1lbW8gfSBmcm9tICdyZWFjdCc7XG5jb25zdCB0b0ZuUmVmID0gcmVmID0+ICFyZWYgfHwgdHlwZW9mIHJlZiA9PT0gJ2Z1bmN0aW9uJyA/IHJlZiA6IHZhbHVlID0+IHtcbiAgcmVmLmN1cnJlbnQgPSB2YWx1ZTtcbn07XG5leHBvcnQgZnVuY3Rpb24gbWVyZ2VSZWZzKHJlZkEsIHJlZkIpIHtcbiAgY29uc3QgYSA9IHRvRm5SZWYocmVmQSk7XG4gIGNvbnN0IGIgPSB0b0ZuUmVmKHJlZkIpO1xuICByZXR1cm4gdmFsdWUgPT4ge1xuICAgIGlmIChhKSBhKHZhbHVlKTtcbiAgICBpZiAoYikgYih2YWx1ZSk7XG4gIH07XG59XG5cbi8qKlxuICogQ3JlYXRlIGFuZCByZXR1cm5zIGEgc2luZ2xlIGNhbGxiYWNrIHJlZiBjb21wb3NlZCBmcm9tIHR3byBvdGhlciBSZWZzLlxuICpcbiAqIGBgYHRzeFxuICogY29uc3QgQnV0dG9uID0gUmVhY3QuZm9yd2FyZFJlZigocHJvcHMsIHJlZikgPT4ge1xuICogICBjb25zdCBbZWxlbWVudCwgYXR0YWNoUmVmXSA9IHVzZUNhbGxiYWNrUmVmPEhUTUxCdXR0b25FbGVtZW50PigpO1xuICogICBjb25zdCBtZXJnZWRSZWYgPSB1c2VNZXJnZWRSZWZzKHJlZiwgYXR0YWNoUmVmKTtcbiAqXG4gKiAgIHJldHVybiA8YnV0dG9uIHJlZj17bWVyZ2VkUmVmfSB7Li4ucHJvcHN9Lz5cbiAqIH0pXG4gKiBgYGBcbiAqXG4gKiBAcGFyYW0gcmVmQSBBIENhbGxiYWNrIG9yIG11dGFibGUgUmVmXG4gKiBAcGFyYW0gcmVmQiBBIENhbGxiYWNrIG9yIG11dGFibGUgUmVmXG4gKiBAY2F0ZWdvcnkgcmVmc1xuICovXG5mdW5jdGlvbiB1c2VNZXJnZWRSZWZzKHJlZkEsIHJlZkIpIHtcbiAgcmV0dXJuIHVzZU1lbW8oKCkgPT4gbWVyZ2VSZWZzKHJlZkEsIHJlZkIpLCBbcmVmQSwgcmVmQl0pO1xufVxuZXhwb3J0IGRlZmF1bHQgdXNlTWVyZ2VkUmVmczsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@restart/hooks/esm/useMergedRefs.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@restart/hooks/esm/useMounted.js":
/*!*******************************************************!*\
  !*** ./node_modules/@restart/hooks/esm/useMounted.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useMounted)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\n/**\n * Track whether a component is current mounted. Generally less preferable than\n * properlly canceling effects so they don't run after a component is unmounted,\n * but helpful in cases where that isn't feasible, such as a `Promise` resolution.\n *\n * @returns a function that returns the current isMounted state of the component\n *\n * ```ts\n * const [data, setData] = useState(null)\n * const isMounted = useMounted()\n *\n * useEffect(() => {\n *   fetchdata().then((newData) => {\n *      if (isMounted()) {\n *        setData(newData);\n *      }\n *   })\n * })\n * ```\n */\nfunction useMounted() {\n  const mounted = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(true);\n  const isMounted = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(() => mounted.current);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    mounted.current = true;\n    return () => {\n      mounted.current = false;\n    };\n  }, []);\n  return isMounted.current;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJlc3RhcnQvaG9va3MvZXNtL3VzZU1vdW50ZWQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTBDOztBQUUxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTixJQUFJO0FBQ0o7QUFDQTtBQUNlO0FBQ2Ysa0JBQWtCLDZDQUFNO0FBQ3hCLG9CQUFvQiw2Q0FBTTtBQUMxQixFQUFFLGdEQUFTO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzb3JyeVxcRGVza3RvcFxcdGVzdEJOXFxwcm9qZWN0c1xcZlxcbm9kZV9tb2R1bGVzXFxAcmVzdGFydFxcaG9va3NcXGVzbVxcdXNlTW91bnRlZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VSZWYsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcblxuLyoqXG4gKiBUcmFjayB3aGV0aGVyIGEgY29tcG9uZW50IGlzIGN1cnJlbnQgbW91bnRlZC4gR2VuZXJhbGx5IGxlc3MgcHJlZmVyYWJsZSB0aGFuXG4gKiBwcm9wZXJsbHkgY2FuY2VsaW5nIGVmZmVjdHMgc28gdGhleSBkb24ndCBydW4gYWZ0ZXIgYSBjb21wb25lbnQgaXMgdW5tb3VudGVkLFxuICogYnV0IGhlbHBmdWwgaW4gY2FzZXMgd2hlcmUgdGhhdCBpc24ndCBmZWFzaWJsZSwgc3VjaCBhcyBhIGBQcm9taXNlYCByZXNvbHV0aW9uLlxuICpcbiAqIEByZXR1cm5zIGEgZnVuY3Rpb24gdGhhdCByZXR1cm5zIHRoZSBjdXJyZW50IGlzTW91bnRlZCBzdGF0ZSBvZiB0aGUgY29tcG9uZW50XG4gKlxuICogYGBgdHNcbiAqIGNvbnN0IFtkYXRhLCBzZXREYXRhXSA9IHVzZVN0YXRlKG51bGwpXG4gKiBjb25zdCBpc01vdW50ZWQgPSB1c2VNb3VudGVkKClcbiAqXG4gKiB1c2VFZmZlY3QoKCkgPT4ge1xuICogICBmZXRjaGRhdGEoKS50aGVuKChuZXdEYXRhKSA9PiB7XG4gKiAgICAgIGlmIChpc01vdW50ZWQoKSkge1xuICogICAgICAgIHNldERhdGEobmV3RGF0YSk7XG4gKiAgICAgIH1cbiAqICAgfSlcbiAqIH0pXG4gKiBgYGBcbiAqL1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdXNlTW91bnRlZCgpIHtcbiAgY29uc3QgbW91bnRlZCA9IHVzZVJlZih0cnVlKTtcbiAgY29uc3QgaXNNb3VudGVkID0gdXNlUmVmKCgpID0+IG1vdW50ZWQuY3VycmVudCk7XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgbW91bnRlZC5jdXJyZW50ID0gdHJ1ZTtcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgbW91bnRlZC5jdXJyZW50ID0gZmFsc2U7XG4gICAgfTtcbiAgfSwgW10pO1xuICByZXR1cm4gaXNNb3VudGVkLmN1cnJlbnQ7XG59Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@restart/hooks/esm/useMounted.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@restart/hooks/esm/useSafeState.js":
/*!*********************************************************!*\
  !*** ./node_modules/@restart/hooks/esm/useSafeState.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _useMounted__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useMounted */ \"(ssr)/./node_modules/@restart/hooks/esm/useMounted.js\");\n\n\n\n/**\n * `useSafeState` takes the return value of a `useState` hook and wraps the\n * setter to prevent updates onces the component has unmounted. Can used\n * with `useMergeState` and `useStateAsync` as well\n *\n * @param state The return value of a useStateHook\n *\n * ```ts\n * const [show, setShow] = useSafeState(useState(true));\n * ```\n */\n\nfunction useSafeState(state) {\n  const isMounted = (0,_useMounted__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n  return [state[0], (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(nextState => {\n    if (!isMounted()) return;\n    return state[1](nextState);\n  }, [isMounted, state[1]])];\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useSafeState);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJlc3RhcnQvaG9va3MvZXNtL3VzZVNhZmVTdGF0ZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQW9DO0FBQ0U7O0FBRXRDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxvQkFBb0IsdURBQVU7QUFDOUIsb0JBQW9CLGtEQUFXO0FBQy9CO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQSxpRUFBZSxZQUFZIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNvcnJ5XFxEZXNrdG9wXFx0ZXN0Qk5cXHByb2plY3RzXFxmXFxub2RlX21vZHVsZXNcXEByZXN0YXJ0XFxob29rc1xcZXNtXFx1c2VTYWZlU3RhdGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlQ2FsbGJhY2sgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgdXNlTW91bnRlZCBmcm9tICcuL3VzZU1vdW50ZWQnO1xuXG4vKipcbiAqIGB1c2VTYWZlU3RhdGVgIHRha2VzIHRoZSByZXR1cm4gdmFsdWUgb2YgYSBgdXNlU3RhdGVgIGhvb2sgYW5kIHdyYXBzIHRoZVxuICogc2V0dGVyIHRvIHByZXZlbnQgdXBkYXRlcyBvbmNlcyB0aGUgY29tcG9uZW50IGhhcyB1bm1vdW50ZWQuIENhbiB1c2VkXG4gKiB3aXRoIGB1c2VNZXJnZVN0YXRlYCBhbmQgYHVzZVN0YXRlQXN5bmNgIGFzIHdlbGxcbiAqXG4gKiBAcGFyYW0gc3RhdGUgVGhlIHJldHVybiB2YWx1ZSBvZiBhIHVzZVN0YXRlSG9va1xuICpcbiAqIGBgYHRzXG4gKiBjb25zdCBbc2hvdywgc2V0U2hvd10gPSB1c2VTYWZlU3RhdGUodXNlU3RhdGUodHJ1ZSkpO1xuICogYGBgXG4gKi9cblxuZnVuY3Rpb24gdXNlU2FmZVN0YXRlKHN0YXRlKSB7XG4gIGNvbnN0IGlzTW91bnRlZCA9IHVzZU1vdW50ZWQoKTtcbiAgcmV0dXJuIFtzdGF0ZVswXSwgdXNlQ2FsbGJhY2sobmV4dFN0YXRlID0+IHtcbiAgICBpZiAoIWlzTW91bnRlZCgpKSByZXR1cm47XG4gICAgcmV0dXJuIHN0YXRlWzFdKG5leHRTdGF0ZSk7XG4gIH0sIFtpc01vdW50ZWQsIHN0YXRlWzFdXSldO1xufVxuZXhwb3J0IGRlZmF1bHQgdXNlU2FmZVN0YXRlOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@restart/hooks/esm/useSafeState.js\n");

/***/ })

};
;